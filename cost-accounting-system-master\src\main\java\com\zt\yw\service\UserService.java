package com.zt.yw.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zt.yw.entity.Succession;

import com.zt.yw.entity.User;
import org.springframework.stereotype.Service;

//User行为管理
@Service
public interface UserService {
    //分页查询用户
    Page<User> selectUser(Page<User> page, User user);
    //修改用户
    Integer updateUser(User user);
    //删除用户
    Integer  deleteUser(User user);
    //修改密码
    Integer updatePass(User user);
}
