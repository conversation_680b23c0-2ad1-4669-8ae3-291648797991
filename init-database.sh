#!/bin/bash

echo "========================================"
echo "        数据库初始化脚本"
echo "========================================"

echo "请确保MySQL服务已启动，并且可以使用root用户连接"

echo
read -s -p "请输入MySQL root密码: " mysql_password
echo

echo
echo "正在创建数据库和导入数据..."

mysql -u root -p$mysql_password -e "CREATE DATABASE IF NOT EXISTS yw_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

if [ $? -ne 0 ]; then
    echo "错误: 数据库创建失败，请检查MySQL连接"
    exit 1
fi

mysql -u root -p$mysql_password yw_database < cost-accounting-system-master/yw_database.sql

if [ $? -ne 0 ]; then
    echo "错误: 数据导入失败"
    exit 1
fi

echo
echo "数据库初始化完成！"
echo "数据库名: yw_database"
echo "用户名: root"
echo "密码: [已设置]"
echo
