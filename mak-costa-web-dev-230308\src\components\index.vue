<template>
	<div class="layout">
    <el-container style="height:100%;">
			<el-header>
				<div class="layout-title">
					<img class="layout-logo" src="../assets/logo.jpg">
					<div class="layout-name">成本核算系统</div>
				</div>
				<div class="layout-nav">
					<el-button type="primary" size="mini" @click="loginOut">登出</el-button>
				</div>
				<div class="layout-user">
					<span class="user-name">欢迎您！ {{userName}}({{ POWER_DIC[power] }})</span>
					<span class="change-pwd" @click="handleModal=true">修改密码</span>
				</div>
			</el-header>
			<el-container>
				<el-aside width="201px">
					<el-menu
						mode="vertical"
						:default-active="$route.path"
						router>
						<el-menu-item v-if="power === '1' || power === '7' || power === '8'" index="/product" style="padding-left: 0px;">
							<i class="el-icon-collection"></i>
							<span slot="title">主品配置</span>
						</el-menu-item>
						<el-menu-item v-if="power === '1' || power === '8'" index="/succession" style="padding-left: 0px;">
							<i class="el-icon-menu"></i>
							<span slot="title">系列配置</span>
						</el-menu-item>
						<el-menu-item v-if="power === '1' || power === '2' || power === '7' || power === '8'" index="/material" style="padding-left: 0px;">
							<i class="el-icon-box"></i>
							<span slot="title">物料配置</span>
						</el-menu-item>
						<el-menu-item v-if="power === '1' || power === '6' || power === '8'" index="/laborCost" style="padding-left: 0px;">
							<i class="el-icon-timer"></i>
							<span slot="title">工角配置</span>
						</el-menu-item>
						<el-menu-item v-if="power === '1' || power === '2' || power === '7' || power === '8'" index="/package" style="padding-left: 0px;">
							<i class="el-icon-receiving"></i>
							<span slot="title">包装配置</span>
						</el-menu-item>
						<el-menu-item v-if="power === '1'" index="/user" style="padding-left: 0px;">
							<i class="el-icon-user"></i>
							<span slot="title">用户管理</span>
						</el-menu-item>
						<el-menu-item v-if="power === '1' || power === '8' || power === '7'" index="/rates" style="padding-left: 0px;">
							<i class="el-icon-money"></i>
							<span slot="title">汇率维护</span>
						</el-menu-item>
					</el-menu>
					<div style="position: fixed;bottom: 0;left:4px;text-align: left;">
						<p style="color: #bbb;margin: 0;">当前版本：<el-button type="text" @click="drawer=true">20250721-025</el-button></p>
						<p style="color: #bbb;font-size: 13px;margin: 0 0 12px 0;">公测版本，如有BUG请及时反馈</p>
					</div>
				</el-aside>
				<el-main :style="{padding: '0 0 20px 20px', overflow: 'hidden'}">
					<el-main class="view-wrap">
						<transition name="el-fade-in">
						<keep-alive>
							<router-view />
						</keep-alive>
						</transition>
					</el-main>
				</el-main>
			</el-container>
      <!-- 版本更新记录 -->
      <el-drawer
        title="更新记录"
        :visible.sync="drawer"
        direction="ltr">
        <el-timeline style="text-align: left;">
          <el-timeline-item
            v-for="(activity, index) in historiesList"
            placement="top"
            :key="index"
            :type="activity.type"
            :color="activity.color"
            :size="activity.size"
            :timestamp="activity.timestamp">
            <p v-for="(cont, index) in activity.content" :key="index" >{{cont}}</p>
          </el-timeline-item>
        </el-timeline>
      </el-drawer>
			<!-- 修改密码 -->
			<el-dialog title="修改密码" :visible.sync="handleModal" width="35%" @close="handleModalClose">
				<el-form :model="pwdForm" ref="pwdForm" :rules="rules" size="mini">
					<el-form-item label="新密码" prop="newPassword">
						<el-input v-model="pwdForm.newPassword" autocomplete="off"></el-input>
					</el-form-item>
					<el-form-item label="确认密码" prop="checkPassword">
						<el-input v-model="pwdForm.checkPassword" autocomplete="off"></el-input>
					</el-form-item>
				</el-form>
				<div slot="footer" class="dialog-footer">
					<el-button size="mini" @click="handleModal=false">取 消</el-button>
					<el-button size="mini" type="primary" @click="handleChangePWD('pwdForm')">确 定</el-button>
				</div>
			</el-dialog>
		</el-container>
	</div>
</template>
<script>
import ajax from '../common/axiosHttp'
import { mapState } from 'vuex'
import { cookies } from '../common/utils.js'
export default {
  name: 'layout',
  data () {
    // 检测新密码和确认密码一致性
    let checkpass = (rule, value, callback) => {
      if (value === this.pwdForm.newPassword) {
        callback()
      } else {
        callback(new Error('密码不一致'))
      }
    }
    return {
      drawer: false,
      userName: cookies.getCookie('userName'),
      power: cookies.getCookie('power'),
      handleModal: false,
      pwdForm: {
        newPassword: '',
        checkPassword: ''
      },
      rules: {
        newPassword: [
          {required: true, message: '请输入新密码', trigger: 'blur'}
        ],
        checkPassword: [
          {required: true, message: '不能为空', trigger: 'blur'},
          {validator: checkpass, trigger: 'blur'}
        ]
      }
    }
  },
  computed: {
    ...mapState(['historiesList', 'POWER_DIC'])
  },
  methods: {
    // 修改密码
    handleChangePWD (formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          ajax({
            method: 'POST',
            url: '/api/updateUser',
            data: {
              userid: cookies.getCookie('userid'),
              password: this.pwdForm.newPassword
            }
          }).then(res => {
            this.handleModal = false
            this.$message({
              offset: 80,
              message: '密码修改成功，请重新登录！',
              type: 'success'
            })
            setTimeout(() => {
              this.loginOut()
            }, 2000)
          })
        } else {
          return false
        }
      })
    },
    handleModalClose () {
      this.$refs['pwdForm'].resetFields()
    },
    // 登出
    loginOut () {
      cookies.delCookie('userName')
      cookies.delCookie('userid')
      cookies.delCookie('power')
      this.$router.replace({path: '/login'})
    }
  }
}
</script>
<style lang="less" scoped>
.layout {
	position: relative;
	width: 100vw;
    height: 100vh;
    overflow: hidden;
	background: #f5f7f9;
	.el-header {
		background-color: #EBEEF5;
		height: 58px;
		line-height: 58px;
	}
	&-title {
    position: relative;
    float: left;
		margin: 0 auto;
  }
  &-name {
    display: inline-block;
    // color: #606266;
		font-size: 26px;
		margin-left: 24px;
  }
  &-logo {
    position: relative;
    float: left;
    width: 128px;
    height: 48px;
    top: 4px;
	}
	.layout-user {
		float: right;
		margin-right: 30px;
		color: #606266;
		.change-pwd {
			font-size: 12px;
			color: #409EFF;
			margin-left: 12px;
			cursor: pointer;
		}
		.change-pwd:hover {
			color: rgb(121, 187, 255);
		}
	}
	.layout-nav {
		position: relative;
		float: right;
		margin: 0 auto;
	}
	.el-aside {
		overflow: auto;
		background: #fff;
		border-right: solid 1px #e6e6e6;
	}
	.el-menu {
		// height: 100%;
		border-right: none;
	}
	.view-wrap {
		padding: 20px;
		min-height: 320px;
		background: #fff;
		// overflow: hidden;
		height: 93%;
		border-left: 1px solid #dcdfe6;
		border-right: 1px solid #dcdfe6;
		border-bottom: 1px solid #dcdfe6;
	}
	.icon-zhinengshebeijiankong {
		font-size: 16px;
	}
}
</style>
