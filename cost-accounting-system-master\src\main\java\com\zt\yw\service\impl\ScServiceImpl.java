package com.zt.yw.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.liuyueyi.quick.transfer.ChineseUtils;
import com.zt.yw.dao.LcMapper;
import com.zt.yw.dao.RmMapper;
import com.zt.yw.dao.ScMapper;
import com.zt.yw.entity.*;
import com.zt.yw.service.ScService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

//系列新增
@Service
public class ScServiceImpl implements ScService {

    @Resource
    private ScMapper scMapper;

    @Resource
    private LcMapper lcMapper;

    @Resource
    private RmMapper rmMapper;



    @Override
    public boolean savaSc(Succession succession) {
        Date now1 = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        succession.setUuid(format.format(now1));
        scMapper.insert(succession);
        return true;
    }

    @Override
    public boolean insertScRm(RmTable rmTable) {
        rmMapper.insert(rmTable);
        return true;
    }

    @Override
    public Page<Succession> selectSc(Page<Succession> page, Succession succession) {
        QueryWrapper<Succession> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().orderByDesc(Succession::getSc_date);//进行排序查询
        //if判断 如果用户没输入品号以及品名条件就执行无条件全部查询，如有输入就按照值来查询，如果用户同时输入品号以及品名那么会if优先执行品号查询
        if ((succession.getSc_name()!=null)&(!succession.getSc_name().isEmpty())){   //该判断适合字符串对比
            // 简体转台湾繁体
            String twOut = ChineseUtils.s2tw(succession.getSc_name());
            System.out.println("s2tw --> " + twOut);
            //进行简繁同时查询(lc_name LIKE ? OR lc_name LIKE ?)
            queryWrapper.like("sc_name", succession.getSc_name()).or().like("sc_name",twOut); //like代表前后的可模糊查询 %aaa%
          //  log.info("主品名执行按照品名模糊查询");
        }else {
           // log.info("主品名执行查询全部");
        }
        return scMapper.selectPage(page,queryWrapper);
    }

    @Override
    public Integer updateSc(Succession succession ) {
        Date now1 = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        succession.setSc_date(format.format(now1));
        return scMapper.updateById(succession);
    }

    @Override
    public R<String> deleteSc(@RequestBody Succession succession) {
        if (StringUtils.hasText(succession.getUuid())) {
            R.fail(500, "删除失败");
        }
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("uuid", succession.getUuid());//相当于where uuid="xxxx"
        try {
            rmMapper.delete(wrapper); //附表删除 原材料
            lcMapper.delete(wrapper);//附表删除
            //     pkMapper.delete(wrapper);//附表删除
            //     frMapper.delete(wrapper);//附表删除
            scMapper.delete(wrapper);
            return R.ok("删除成功");
        } catch (Exception e) {
            // log.info("LOG: 成本核算系统：删除操作：发生异常：{}", e.toString());
            return R.fail(500, "删除失败");

        }
    }

    @Override
    public Map<String, Object> selectScList(Succession succession) {
        succession =  scMapper.selectById(succession.getSc_id());
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("uuid",succession.getUuid());//相当于where uuid="xxxx"
      //  log.info("获取到关键值UUID==="+maintable.getUuid());
        List<RmTable> rmTableList = rmMapper.selectList(wrapper); //附表查询 原材料
        List<Lctable> lctableList = lcMapper.selectList(wrapper);//附表查询
        //多表查询结果list传到map赋值数组名
        Map<String,Object> map = new HashMap<>();
        map.put( "postForm",succession);
        map.put("rawMaterialTableData", rmTableList);
        map.put("LaborCostTableData", lctableList);
        return map;
    }
}
