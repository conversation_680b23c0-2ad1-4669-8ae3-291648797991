<template>
  <div class="main">
    <div class="query">
      <div class="query-wrapper">
        <div class="query-info">
          <span>系列：</span>
          <el-input
            v-model="queryName"
            size="mini"
            placeholder="请输入系列查询"
            @keyup.enter.native="seach"
            clearable>
          </el-input>
        </div>
        <div class="query-info">
          <el-button size="mini" @click="seach" icon="el-icon-search" type="primary" plain>查询</el-button>
        </div>
        <div class="query-info" style="text-align:right">
          <el-button v-if="power === '1' || power === '8'" type="primary" size="mini" icon="el-icon-plus" @click="handleCreate">新增</el-button>
        </div>
      </div>
    </div>
    <!-- 表格 -->
    <el-table
      class="mainTable"
      :data="tableList"
      :header-cell-style='{"backgroundColor": "rgb(244, 244, 245)"}'
      :height="tableHeight"
      :span-method="successionSpanMethod"
      stripe
    >
      <el-table-column
        prop="sc_name"
        label="系列名称"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="sc_type"
        label="型号"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="sc_standard"
        label="标准"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="sc_discripte"
        label="系列说明"
      >
      </el-table-column>
      <el-table-column
        prop="sc_user"
        label="最后更新人"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="sc_date"
        label="最后更新日期"
      >
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="100">
        <template slot-scope="scope">
          <el-button v-if="power === '1' || power === '8'" type="text" icon="el-icon-folder-add" @click="handleDetail(scope.row)">配置</el-button>
          <el-button v-if="power === '1' || power === '8'" type="text" icon="el-icon-edit" @click="updateProductForm(scope.row)">修改</el-button>
          <el-button v-if="power === '1' || power === '8'" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="page">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentNum"
        :page-sizes="[50, 100, 150, 200]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalNum">
      </el-pagination>
    </div>
    <!-- 新增/修改 -->
    <el-dialog :title="handleType==='create'?'新增':`${editTitle}`" :visible.sync="handleAddModal" :before-close="handleMainClose">
      <el-form :model="mainFormData" class="form-wrap" :rules="rules" ref="ruleForm">
        <el-form-item prop="sc_name" label="系列名称" label-width="100px">
          <el-input v-model="mainFormData.sc_name" placeholder="请输入系列名称" clearable>
          </el-input>
        </el-form-item>
        <el-form-item prop="sc_standard" label="标准" label-width="100px" style="text-align: left;">
          <el-radio-group v-model="mainFormData.sc_standard" :disabled="handleType==='update'">
            <el-radio v-for="(op, i) in standard_options" :key="i" :label="op.label"></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="sc_type" label="型号" label-width="100px" style="text-align: left;">
          <el-input v-model="mainFormData.sc_type" placeholder="请输入型号名称" clearable>
          </el-input>
          <!-- <el-radio-group v-model="mainFormData.sc_type">
            <el-radio v-for="(op, i) in type_options" :key="i" :label="op.value">{{ op.label }}</el-radio>
          </el-radio-group> -->
        </el-form-item>
        <el-form-item prop="sc_discripte" label="系列说明" label-width="100px">
          <el-input v-model="mainFormData.sc_discripte" placeholder="请填写系列说明" type="textarea" :rows="2" clearable>
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="callOf('ruleForm')">取 消</el-button>
        <el-button type="primary" size="mini" @click="postAddMain('ruleForm')">保 存</el-button>
      </div>
    </el-dialog>
    <!-- 配置 -->
    <el-dialog :title="`《${editTitle}》系列配置`" width="90%" :visible.sync="handleEditModal" :before-close="handleDetailClose">
      <el-collapse v-model="activeNames">
        <!-- 物料 -->
        <el-collapse-item title="物料" name="rm">
          <el-button :disabled="rm_btn" class="addBtn" type="primary" size="mini" icon="el-icon-plus" @click="addRawMaterialRow">新增</el-button>
          <el-form :model="detailObjData" ref="rmTableForm" :rules="rmRules" size="small">
            <el-table
              :data="detailObjData.rawMaterialTableData"
              :header-cell-style='{"backgroundColor": "rgb(217, 236, 255)"}'
              :row-style='{"backgroundColor": "rgb(236, 245, 255)"}'
              style="width: 100%"
              class="list-table"
              max-height="540"
              >
              <el-table-column
                label="#"
                type="index"
                align="center"
                width="60">
              </el-table-column>
              <el-table-column label="品号" align="left" prop="rm_number" min-width="160">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'rawMaterialTableData.' + scope.$index + '.rm_number'"
                    :rules="rmRules.rm_number"
                  >
                    <el-autocomplete
                      v-model="scope.row.rm_number"
                      :popper-append-to-body="false"
                      :fetch-suggestions="queryRmNumberSearchAsync"
                      placeholder="请填品号"
                      @select="handleRmNumberSelect"
                      clearable
                    />
                  </el-form-item>
                  <span v-else>{{scope.row.rm_number}}</span>
                </template>
              </el-table-column>
              <el-table-column label="品名" align="left" prop="rm_material" min-width="160">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'rawMaterialTableData.' + scope.$index + '.rm_material'"
                    :rules="rmRules.rm_material"
                  >
                    <el-autocomplete
                      v-model="scope.row.rm_material"
                      :fetch-suggestions="queryNameSearchAsync"
                      placeholder="请填写物料名称"
                      :popper-append-to-body="false"
                      @select="handleNameSelect"
                      clearable
                    />
                  </el-form-item>
                  <span v-else>{{scope.row.rm_material}}</span>
                </template>
              </el-table-column>
              <el-table-column label="种类" align="center" prop="rm_itemType">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'rawMaterialTableData.' + scope.$index + '.rm_itemType'"
                    :rules="rmRules.rm_itemType"
                  >
                    <el-select v-model="scope.row.rm_itemType" @change="handleSelectItemType" size="mini" style="width:70%;">
                      <el-option
                        v-for="(item, idx) in itemOtions"
                        :key="idx"
                        :value="item">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <span v-else>{{scope.row.rm_itemType}}</span>
                </template>
              </el-table-column>
              <el-table-column label="厂商" align="center" prop="rm_supplier">
                <template slot-scope="scope">
                  <span>{{scope.row.rm_supplier}}</span>
                </template>
              </el-table-column>
              <el-table-column label="单价" align="center" prop="rm_price">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'rawMaterialTableData.' + scope.$index + '.rm_price'"
                    :rules="rmRules.rm_price"
                  >
                    <el-input-number v-model="scope.row.rm_price" controls-position="right" placeholder="请填写" :min="0" :precision="5" clearable />
                  </el-form-item>
                  <span v-else>{{scope.row.rm_price}}</span>
                </template>
              </el-table-column>
              <el-table-column label="单位" align="center" prop="rm_unit">
                <template slot-scope="scope">
                  <span>{{scope.row.rm_unit}}</span>
                </template>
              </el-table-column>
              <el-table-column label="用量" align="center" prop="rm_consumption">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'rawMaterialTableData.' + scope.$index + '.rm_consumption'"
                    :rules="rmRules.rm_consumption"
                  >
                    <el-input-number v-model="scope.row.rm_consumption" controls-position="right" placeholder="请填写" :min="0" :step="1" :precision="5" clearable />
                  </el-form-item>
                  <span v-else>{{scope.row.rm_consumption}}</span>
                </template>
              </el-table-column>
              <!-- <el-table-column label="币种" align="center" prop="rm_currencyType">
                <template slot-scope="scope">
                  <span>{{CURRENCY_DIC[scope.row.rm_currencyType]}}</span>
                </template>
              </el-table-column> -->
              <el-table-column label="汇率" align="center" prop="rm_exchangeRate">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'rawMaterialTableData.' + scope.$index + '.rm_exchangeRate'"
                    :rules="rmRules.rm_exchangeRate"
                  >
                    <el-input-number v-model="scope.row.rm_exchangeRate" controls-position="right" placeholder="请填写" :min="0" disabled />
                  </el-form-item>
                  <span v-else>{{scope.row.rm_exchangeRate}}</span>
                </template>
              </el-table-column>
              <el-table-column label="损耗" align="center" prop="rm_loss">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'rawMaterialTableData.' + scope.$index + '.rm_loss'"
                    :rules="rmRules.rm_loss"
                  >
                    <el-input-number v-model="scope.row.rm_loss" controls-position="right" placeholder="请填写" :step="0.1" :min="0" clearable />
                  </el-form-item>
                  <span v-else>{{scope.row.rm_loss}}</span>
                </template>
              </el-table-column>
              <el-table-column label="金额" align="center" prop="rm_amounts">
                  <template slot-scope="scope">
                    <el-button v-if="scope.row.showInput" type="text" icon="el-icon-search" @click="calculateRM(scope.row)" />
                    <span>{{scope.row.rm_amounts}}</span>
                  </template>
              </el-table-column>
              <el-table-column label="操作" align="center" min-width="100">
                <template slot-scope="scope">
                  <el-button v-if="scope.row.showInput" type="text" icon="el-icon-check" @click.native.prevent="saveRawMaterialRow(scope.row)">提交</el-button>
                  <el-button v-else :disabled="rm_btn" type="text" icon="el-icon-edit" @click="updateRawMaterialRow(scope.row, scope.$index)">修改</el-button>
                  <el-button v-if="scope.row.showInput" type="text" icon="el-icon-close" @click.native.prevent="cancelRawMaterialRow(scope.row)">取消</el-button>
                  <el-button v-else :disabled="rm_btn && !scope.row.showInput" type="text" icon="el-icon-delete" @click.native.prevent="deleteRawMaterialRow(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </el-collapse-item>
        <!-- 工角 -->
        <el-collapse-item title="工角" name="lc">
          <el-button :disabled="lc_btn" class="addBtn" type="primary" size="mini" icon="el-icon-plus" @click="addLaborRow">新增</el-button>
        <el-form :model="detailObjData" ref="lcTableForm" :rules="lcRules" size="small">
          <el-table
            :data="detailObjData.LaborCostTableData"
            :header-cell-style='{"backgroundColor": "rgb(225, 243, 216)"}'
            :row-style='{"backgroundColor": "rgb(240, 249, 235)"}'
            style="width: 100%"
            class="list-table"
            max-height="540"
            >
              <el-table-column
                label="#"
                type="index"
                align="center"
                width="50">
              </el-table-column>
              <el-table-column label="工角名称" align="left" prop="lc_name">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'LaborCostTableData.' + scope.$index + '.lc_name'"
                    :rules="lcRules.lc_name"
                  >
                    <el-autocomplete
                      v-model="scope.row.lc_name"
                      :popper-append-to-body="false"
                      :fetch-suggestions="queryLCNameSearchAsync"
                      placeholder="请填写工角名称"
                      @select="handleLCNameSelect"
                      clearable
                    />
                  </el-form-item>
                  <span v-else>{{scope.row.lc_name}}</span>
                </template>
              </el-table-column>
              <el-table-column label="所属工厂" align="center" prop="lc_factory">
                <template slot-scope="scope">
                  <span>{{scope.row.lc_factory}}</span>
                </template>
              </el-table-column>
              <el-table-column label="单价" align="center" prop="lc_price">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'LaborCostTableData.' + scope.$index + '.lc_price'"
                    :rules="lcRules.lc_price"
                  >
                    <el-input-number v-model="scope.row.lc_price" controls-position="right" placeholder="请填写" :min="0" :precision="5" clearable />
                  </el-form-item>
                  <span v-else>{{scope.row.lc_price}}</span>
                </template>
              </el-table-column>
              <!-- <el-table-column label="单位" align="center" prop="lc_unit">
                <template slot-scope="scope">
                  <span>{{scope.row.lc_unit}}</span>
                </template>
              </el-table-column> -->
              <el-table-column label="用量" align="center" prop="lc_consumption">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'LaborCostTableData.' + scope.$index + '.lc_consumption'"
                    :rules="lcRules.lc_consumption"
                  >
                    <el-input-number v-model="scope.row.lc_consumption" controls-position="right" placeholder="请填写" :min="1" :step="1" :precision="0" clearable />
                  </el-form-item>
                  <span v-else>{{scope.row.lc_consumption}}</span>
                </template>
              </el-table-column>
              <el-table-column label="汇率" align="center" prop="lc_exchangeRate">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'LaborCostTableData.' + scope.$index + '.lc_exchangeRate'"
                    :rules="lcRules.lc_exchangeRate"
                  >
                    <el-input-number v-model="scope.row.lc_exchangeRate" controls-position="right" placeholder="请填写" :min="0" disabled />
                  </el-form-item>
                  <span v-else>{{scope.row.lc_exchangeRate}}</span>
                </template>
              </el-table-column>
              <!-- <el-table-column label="金额" align="center" prop="lc_amounts">
                <template slot-scope="scope">
                  <el-button v-if="scope.row.showInput" type="text" icon="el-icon-search" @click="calculateLC(scope.row)" />
                  <span>{{scope.row.lc_amounts}}</span>
                </template>
              </el-table-column> -->
              <el-table-column label="操作" align="center" min-width="100">
                <template slot-scope="scope">
                  <el-button v-if="scope.row.showInput" type="text" icon="el-icon-check" @click="saveLaborCostRow(scope.row)">提交</el-button>
                  <el-button v-else :disabled="lc_btn" type="text" icon="el-icon-edit" @click="updateLaborRow(scope.row)">修改</el-button>
                  <el-button v-if="scope.row.showInput" type="text" icon="el-icon-close" @click="cancelLaborRow(scope.row)">取消</el-button>
                  <el-button v-else :disabled="lc_btn && !scope.row.showInput" type="text" icon="el-icon-delete" @click.native.prevent="deleteLaborRow(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </el-collapse-item>
      </el-collapse>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="resetBTNs">关 闭</el-button>
      </div>
    </el-dialog>
	</div>
</template>

<script>
import { mapState } from 'vuex'
import ajax from '../common/axiosHttp'
import { cookies, formatDate } from '../common/utils'
export default {
  name: 'Succession',
  data () {
    return {
      globalIndex: 0,
      itemOtions: [],
      currentRow: {},
      tableHeight: 0,
      currentNum: 1,
      pageSize: 50,
      totalNum: 0,
      material: '',
      power: cookies.getCookie('power'),
      tableList: [],
      activeNames: ['rm', 'lc'],
      queryName: '',
      laborCostList: [],
      handleAddModal: false,
      handleEditModal: false,
      mainFormData: {},
      handleType: '',
      editTitle: '',
      detailObjData: {
        postForm: {},
        LaborCostTableData: [],
        rawMaterialTableData: []
      },
      rules: {
        sc_name: [
          { required: true, message: '请填写系列名称', trigger: 'blur' }
        ],
        sc_standard: [
          { required: true, message: '请选择标准', trigger: 'blur' }
        ],
        sc_type: [
          { required: true, message: '请选择型号', trigger: 'blur' }
        ]
      },
      rm_btn: false,
      lc_btn: false,
      lc_sum: 0,
      lc_percentage: 156, // 人工费百分比默认：156%
      rmRules: {
        rm_itemType: [
          {
            required: true,
            message: '请选择种类',
            trigger: 'change'
          }
        ],
        rm_exchangeRate: [
          {
            required: true,
            type: 'number',
            message: '请填写汇率',
            trigger: 'blur'
          }
        ],
        rm_price: [
          {
            required: true,
            type: 'number',
            message: '请填写单价',
            trigger: 'blur'
          }
        ],
        rm_consumption: [
          {
            required: true,
            type: 'number',
            message: '请填写用量',
            trigger: 'blur'
          }
        ],
        rm_loss: [
          {
            required: true,
            type: 'number',
            message: '请填写损耗',
            trigger: 'blur'
          }
        ],
        rm_material: [
          {
            required: true,
            message: '请填写名称',
            trigger: 'change'
          }
        ],
        rm_number: [
          {
            required: true,
            message: '请填写物料品号',
            trigger: 'change'
          }
        ],
        rm_unit: [
          {
            required: true,
            message: '请选择单位',
            trigger: 'change'
          }
        ],
        rm_currencyType: [
          {
            required: true,
            message: '请选择币种',
            trigger: 'change'
          }
        ]
      },
      lcRules: {
        lc_unit: [
          {
            required: true,
            message: '请选择单位',
            trigger: 'change'
          }
        ],
        lc_exchangeRate: [
          {
            required: true,
            type: 'number',
            message: '请填写汇率',
            trigger: 'blur'
          }
        ],
        lc_price: [
          {
            required: true,
            type: 'number',
            message: '请填写单价',
            trigger: 'blur'
          }
        ],
        lc_consumption: [
          {
            required: true,
            type: 'number',
            message: '请填写用量',
            trigger: 'blur'
          }
        ],
        lc_name: [
          {
            required: true,
            message: '请填写名称',
            trigger: 'change'
          }
        ]
      }
    }
  },
  computed: {
    ...mapState(['CURRENCY_DIC', 'standard_options'])
  },
  mounted () {
    // 挂载window.onresize事件(动态设置table高度)
    this.$nextTick(() => {
      this.tableHeight = window.innerHeight - 136
    })
    let _this = this
    window.onresize = () => {
      if (_this.resizeFlag) {
        clearTimeout(_this.resizeFlag)
      }
      _this.resizeFlag = setTimeout(() => {
        _this.getTableHeight()
        _this.resizeFlag = null
      }, 100)
    }
  },
  activated () {
    this.getAllList()
    this.getLaborCostList()
  },
  methods: {
    createFilter (queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0)
      }
    },
    successionSpanMethod ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
      // 获取当前单元格的值
        const currentValue = row[column.property]
        // 获取上一行相同列的值
        const preRow = this.tableList[rowIndex - 1]
        const preValue = preRow ? preRow[column.property] : null
        // 如果当前值和上一行的值相同，则将当前单元格隐藏
        if (currentValue === preValue) {
          return { rowspan: 0, colspan: 0 }
        } else {
          // 否则计算当前单元格应该跨越多少行
          let rowspan = 1
          for (let i = rowIndex + 1; i < this.tableList.length; i++) {
            const nextRow = this.tableList[i]
            const nextValue = nextRow[column.property]
            if (nextValue === currentValue) {
              rowspan++
            } else {
              break
            }
          }
          return { rowspan, colspan: 1 }
        }
      }
    },
    // 计算table高度(动态设置table高度)
    getTableHeight () {
      let tableH = 180 // 距离页面下方的高度
      let tableHeightDetil = window.innerHeight - tableH
      if (tableHeightDetil <= 300) {
        this.tableHeight = 300
      } else {
        this.tableHeight = window.innerHeight - tableH
      }
    },
    // 搜索
    seach () {
      this.current = 1
      this.pageSize = 50
      this.getAllList()
    },
    // 获取系列列表
    getAllList () {
      let form = new FormData()
      form.append('current', this.currentNum)
      form.append('size', this.pageSize)
      form.append('sc_name', this.queryName)
      ajax({
        method: 'POST',
        url: '/api/selectSc',
        data: form
      }).then(res => {
        this.totalNum = res.total
        this.tableList = res.records
      })
    },
    // 页码分页
    handleSizeChange (val) {
      this.pageSize = val
      this.getAllList()
    },
    // 分页
    handleCurrentChange (val) {
      this.currentNum = val
      this.getAllList()
    },
    // 查询原材料
    queryRawMaterialList () {
      ajax({
        method: 'POST',
        url: '/api/selectRm',
        data: {
          uuid: this.detailObjData.postForm.uuid
        }
      }).then(res => {
        this.detailObjData.rawMaterialTableData = res
        this.rm_btn = false
      })
    },
    // 原材料金额计算
    calculateRM (row) {
      if (!row.rm_price) return
      if (!row.rm_exchangeRate) return
      if (!row.rm_consumption) return
      if (!row.rm_loss) return
      let AMOUNTS = 0
      if (!row.rm_consumption) {
        AMOUNTS = row.rm_price * row.rm_exchangeRate / row.rm_loss
      } else if (row.rm_consumption > 0 && row.rm_consumption < 1) {
        AMOUNTS = row.rm_price * row.rm_exchangeRate * row.rm_consumption / row.rm_loss // 小用量：单价 x 汇率 x 用量 / 损耗
      } else if (row.rm_consumption >= 1) {
        AMOUNTS = row.rm_price * row.rm_exchangeRate / row.rm_consumption / row.rm_loss // 小用量：单价 x 汇率 / 用量 / 损耗
      }
      row.rm_amounts = AMOUNTS.toFixed(5)
    },
    // 原料名称远程搜索
    queryNameSearchAsync (queryString, cb) {
      let form = new FormData()
      form.append('current', 1)
      form.append('size', 9999)
      form.append('rm_material', queryString)
      form.append('rm_number', '')
      ajax({
        method: 'POST',
        url: '/api/GoodsAll',
        data: form
      }).then(res => {
        var restaurants = res.records.map((good) => {
          return {
            value: good.rm_material,
            ...good
          }
        })
        cb(restaurants)
      })
    },
    // 原料名称选择
    handleNameSelect (item) {
      this.detailObjData.rawMaterialTableData[this.globalIndex].rm_number = item.rm_number
      this.itemOtions = item.rm_itemType.split(',')
      if (this.itemOtions.length === 1) {
        this.detailObjData.rawMaterialTableData[this.globalIndex].rm_itemType = this.itemOtions[0]
      }
      this.detailObjData.rawMaterialTableData[this.globalIndex].rm_supplier = item.rm_supplier
      this.detailObjData.rawMaterialTableData[this.globalIndex].rm_price = item.rm_price
      this.detailObjData.rawMaterialTableData[this.globalIndex].rm_unit = item.rm_unit
      this.detailObjData.rawMaterialTableData[this.globalIndex].rm_exchangeRate = item.rm_exchangeRate
      this.detailObjData.rawMaterialTableData[this.globalIndex].rm_currencyType = item.rm_currencyType
    },
    handleSelectItemType (item) {
      this.detailObjData.rawMaterialTableData[this.globalIndex].rm_itemType = item
    },
    // 原料品号远程搜索
    queryRmNumberSearchAsync (queryString, cb) {
      let form = new FormData()
      form.append('current', 1)
      form.append('size', 9999)
      form.append('rm_material', '')
      form.append('rm_number', queryString)
      ajax({
        method: 'POST',
        url: '/api/GoodsAll',
        data: form
      }).then(res => {
        var restaurants = res.records.map((good) => {
          return {
            value: good.rm_number,
            ...good
          }
        })
        cb(restaurants)
      })
    },
    // 原料品号选择
    handleRmNumberSelect (item) {
      this.detailObjData.rawMaterialTableData[this.globalIndex].rm_material = item.rm_material
      this.itemOtions = item.rm_itemType.split(',')
      if (this.itemOtions.length === 1) {
        this.detailObjData.rawMaterialTableData[this.globalIndex].rm_itemType = this.itemOtions[0]
      }
      this.detailObjData.rawMaterialTableData[this.globalIndex].rm_supplier = item.rm_supplier
      this.detailObjData.rawMaterialTableData[this.globalIndex].rm_price = item.rm_price
      this.detailObjData.rawMaterialTableData[this.globalIndex].rm_unit = item.rm_unit
      this.detailObjData.rawMaterialTableData[this.globalIndex].rm_currencyType = item.rm_currencyType
      this.detailObjData.rawMaterialTableData[this.globalIndex].rm_exchangeRate = item.rm_exchangeRate
    },
    // 新增原材料
    addRawMaterialRow () {
      this.globalIndex = 0
      this.rm_btn = true
      this.detailObjData.rawMaterialTableData.unshift({
        showInput: true,
        uuid: this.detailObjData.postForm.uuid,
        rm_material: '',
        rm_itemType: '',
        rm_price: 0,
        rm_unit: '',
        rm_currencyType: '',
        rm_amounts: '',
        rm_exchangeRate: 1,
        rm_loss: 0.97,
        rm_consumption: 1,
        rm_number: ''
      })
    },
    // 修改原材料
    updateRawMaterialRow (row, index) {
      this.globalIndex = index
      this.itemOtions = row.rm_itemType ? row.rm_itemType.split(',') : []
      row.showInput = true
      this.rm_btn = true
    },
    // 取消修改原材料
    cancelRawMaterialRow (row) {
      if (!row.rm_id) {
        this.detailObjData.rawMaterialTableData.splice(0, 1)
        this.rm_btn = false
        this.itemOtions = []
      } else {
        this.queryRawMaterialList()
      }
    },
    // 提交原材料行
    saveRawMaterialRow (row) {
      this.$refs.rmTableForm.validate(valid => {
        if (valid) {
          // 总价=单价x汇率/基本用量/0.97
          this.calculateRM(row)
          if (!cookies.getCookie('userName')) {
            this.$message.warning('登录超时，请重新登录！')
            return
          }
          let postData = {
            sc_id: this.currentRow.sc_id,
            sc_user: cookies.getCookie('userName'),
            sc_date: formatDate(Date.now(), 'YYYY-MM-DD HH:mm:ss')
          }
          ajax({
            method: 'POST',
            url: '/api/updateSc',
            data: postData
          }).then(res => {
            console.log(res)
          })
          ajax({
            method: 'POST',
            url: row.rm_id ? '/api/updaterm' : '/api/insertScRm',
            data: row
          }).then(res => {
            this.$message({
              offset: 80,
              message: '保存成功！',
              type: 'success'
            })
            row.showInput = false
            this.itemOtions = []
            this.queryRawMaterialList()
          })
        } else {
          return false
        }
      })
    },
    // 删除原材料
    deleteRawMaterialRow (row) {
      this.$refs.rmTableForm.clearValidate()
      if (row.showInput) {
        this.detailObjData.rawMaterialTableData.splice(0, 1)
        this.rm_btn = false
      } else {
        this.$confirm(`是否删除”${row.rm_material}“信息?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          ajax({
            method: 'POST',
            url: '/api/deleterm',
            data: {
              rm_id: row.rm_id
            }
          }).then(res => {
            this.$message({
              offset: 80,
              message: '删除成功！',
              type: 'success'
            })
            row.showInput = false
            this.itemOtions = []
            this.queryRawMaterialList()
          })
        }).catch(() => {
          this.$message({
            offset: 80,
            type: 'info',
            message: '已取消删除！'
          })
        })
      }
    },
    // 获取工角列表
    getLaborCostList () {
      let form = new FormData()
      form.append('current', 1)
      form.append('size', 9999)
      form.append('lc_name', '')
      ajax({
        method: 'POST',
        url: '/api/LaborCostAll',
        data: form
      }).then(res => {
        this.laborCostList = res.records
      })
    },
    // 工角名称远程搜索
    queryLCNameSearchAsync (queryString, cb) {
      var restaurants = this.laborCostList.map((item) => {
        return {
          value: item.lc_name + ' (' + item.lc_factory + ')', // 名称中显示工厂，方便选择
          ...item
        }
      })
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    // 工角名称选择
    handleLCNameSelect (item) {
      this.detailObjData.LaborCostTableData[0].lc_name = item.lc_name + '人工'
      this.detailObjData.LaborCostTableData[0].lc_price = item.lc_price
      this.detailObjData.LaborCostTableData[0].lc_unit = item.lc_unit
      this.detailObjData.LaborCostTableData[0].lc_factory = item.lc_factory
    },
    // 新增
    handleCreate () {
      this.mainFormData = {}
      this.itemOtions = []
      this.handleType = 'create'
      this.handleAddModal = true
    },
    // 提交
    postAddMain (formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (!cookies.getCookie('userName')) {
            this.$message.warning('登录超时，请重新登录！')
            return
          }
          if (this.handleType === 'create') {
            let postData = {
              uuid: '',
              sc_id: '',
              ...this.mainFormData,
              sc_user: cookies.getCookie('userName'),
              sc_date: formatDate(Date.now(), 'YYYY-MM-DD HH:mm:ss')
            }
            ajax({
              method: 'POST',
              url: '/api/ScAdd',
              data: postData
            }).then(res => {
              this.$message({
                offset: 80,
                message: '保存成功！',
                type: 'success'
              })
              this.handleAddModal = false
              this.getAllList()
            })
          }
          if (this.handleType === 'update') {
            let postData = {
              ...this.mainFormData,
              sc_user: cookies.getCookie('userName'),
              sc_date: formatDate(Date.now(), 'YYYY-MM-DD HH:mm:ss')
            }
            ajax({
              method: 'POST',
              url: '/api/updateSc',
              data: postData
            }).then(res => {
              this.$message({
                offset: 80,
                message: '保存成功！',
                type: 'success'
              })
              this.handleAddModal = false
              this.getAllList()
            })
          }
        } else {
          return false
        }
      })
    },
    // 修改
    updateProductForm (row) {
      this.mainFormData = row
      this.handleType = 'update'
      this.handleAddModal = true
    },
    // 配置
    handleDetail (row) {
      this.currentRow = row
      this.lc_percentage = 156
      ajax({
        method: 'POST',
        url: '/api/selectScList',
        data: {
          sc_id: row.sc_id
        }
      }).then(res => {
        this.editTitle = row.sc_type
        this.detailObjData = res
        this.handleEditModal = true
      })
    },
    // 删除
    handleDelete (row) {
      this.$confirm(`此操作将永久删除“${row.sc_type}”系列数据, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ajax({
          method: 'POST',
          url: '/api/deleteSc',
          data: {
            uuid: row.uuid
          }
        }).then(res => {
          this.$message({
            offset: 80,
            type: 'success',
            message: '操作成功！'
          })
          this.getAllList()
        })
      }).catch(() => {
        this.$message({
          offset: 80,
          type: 'info',
          message: '已取消删除！'
        })
      })
    },
    // 关闭对话框确认
    handleMainClose (done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
          this.$refs['ruleForm'].resetFields()
        })
        .catch(_ => {})
    },
    // 关闭模态框
    callOf (formName) {
      this.handleAddModal = false
      this.$refs[formName].resetFields()
      this.getAllList()
    },
    // 关闭详情对话框
    handleDetailClose (done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
          this.resetBTNs()
          this.getAllList()
        })
        .catch(_ => {})
    },
    // 状态清除
    resetBTNs () {
      this.handleEditModal = false
      this.rm_btn = false
      this.lc_btn = false
    },
    // 查询工角
    queryLaborCostList () {
      ajax({
        method: 'POST',
        url: '/api/selectLc',
        data: {
          uuid: this.detailObjData.postForm.uuid
        }
      }).then(res => {
        this.detailObjData.LaborCostTableData = res
        this.lc_btn = false
      })
    },
    // 新增工角
    addLaborRow () {
      this.lc_btn = true
      this.detailObjData.LaborCostTableData.unshift({
        showInput: true,
        uuid: this.detailObjData.postForm.uuid,
        lc_name: '',
        lc_price: 0,
        lc_unit: '',
        // lc_amounts: '',
        lc_exchangeRate: 1,
        lc_consumption: 1
      })
    },
    // 修改工角
    updateLaborRow (row) {
      row.showInput = true
      this.lc_btn = true
    },
    // 工角金额计算
    calculateLC (row) {
      if (!row.lc_price) return
      if (!row.lc_exchangeRate) return
      if (row.lc_consumption) return
      let AMOUNTS = 0
      if (!row.lc_consumption) {
        AMOUNTS = row.lc_price * row.lc_exchangeRate
      } else if (row.lc_consumption > 0 && row.lc_consumption < 1) {
        AMOUNTS = row.lc_price * row.lc_exchangeRate * row.lc_consumption // 小用量
      } else if (row.lc_consumption >= 1) {
        AMOUNTS = row.lc_price * row.lc_exchangeRate / row.lc_consumption // 大用量
      }
      row.lc_amounts = AMOUNTS.toFixed(5)
    },
    // 取消修改工角
    cancelLaborRow (row) {
      if (!row.lc_id) {
        this.detailObjData.LaborCostTableData.splice(0, 1)
        this.lc_btn = false
      } else {
        this.queryLaborCostList()
      }
    },
    // 删除工角
    deleteLaborRow (row) {
      this.$refs.lcTableForm.clearValidate()
      if (row.showInput) {
        this.detailObjData.LaborCostTableData.splice(0, 1)
        this.lc_btn = false
      } else {
        this.$confirm(`是否删除”${row.lc_name}“信息?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          ajax({
            method: 'POST',
            url: '/api/deletelc',
            data: {
              lc_id: row.lc_id
            }
          }).then(res => {
            this.$message({
              offset: 80,
              message: '删除成功！',
              type: 'success'
            })
            row.showInput = false
            this.queryLaborCostList()
          })
        }).catch(() => {
          this.$message({
            offset: 80,
            type: 'info',
            message: '已取消删除！'
          })
        })
      }
    },
    // 提交工角
    saveLaborCostRow (row) {
      this.$refs.lcTableForm.validate(valid => {
        if (valid) {
          // 总价=单价x汇率/基本用量
          this.calculateLC(row)
          if (!cookies.getCookie('userName')) {
            this.$message.warning('登录超时，请重新登录！')
            return
          }
          let postData = {
            sc_id: this.currentRow.sc_id,
            sc_user: cookies.getCookie('userName'),
            sc_date: formatDate(Date.now(), 'YYYY-MM-DD HH:mm:ss')
          }
          ajax({
            method: 'POST',
            url: '/api/updateSc',
            data: postData
          }).then(res => {
            console.log(res)
          })
          ajax({
            method: 'POST',
            url: row.lc_id ? '/api/updatelc' : '/api/lcadd',
            data: row
          }).then(res => {
            this.$message({
              offset: 80,
              message: '保存成功！',
              type: 'success'
            })
            row.showInput = false
            this.queryLaborCostList()
          })
        } else {
          return false
        }
      })
    },
    // 工角小计
    getSummariesLC (param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === columns.length - 2) {
          sums[index] = '合计'
          return
        }
        if (index === columns.length - 1) {
          const values = data.map(item => Number(item[column.property]))
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0)
            sums[index] = (sums[index] * this.lc_percentage / 100).toFixed(5) // 合计加上损耗
          } else {
            sums[index] = ''
          }
        }
      })
      if (!sums[sums.length - 1]) {
        this.lc_sum = 0
      } else {
        this.lc_sum = sums[sums.length - 1]
      }
      this.totalMethod()
      return sums
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .el-autocomplete-suggestion li {
  text-align: left;
  white-space: normal;
  word-break: break-all;
  line-height: 24px;
}
/deep/ .el-dialog__body {
  padding-top: 0;
}
.main {
  height:100vh;
  overflow:hidden;
  .topBtn {
    float: left;
    margin: 0 0 24px 0;
  }
  .addBtn {
    float: right;
    margin: 0 24px 32px;
  }
  .page {
    margin: 20px 0;
    text-align: center;
  }
  .total {
    color: #303133;
    text-align: right;
    padding: 16px 12px 0 0;
    font-size: 14px;
  }
  .lc-collapse {
    position: relative;
    .lc-percent {
      position: absolute;
      bottom: 36px;
      right: 30%;
    }
  }
  .tt-collapse{
    position: relative;
    .tt-marketing {
      position: absolute;
      top: 10px;
      left: 12%;
    }
    .tt-FXRates {
      position: absolute;
      top: 10px;
      left: 60%;
    }
    .tt-currentType {
      position: absolute;
      top: 10px;
      left: 36%
    }
  }

  .query {
    // margin-bottom: 20px;
    &-wrapper {
      display: inline-block;
      width: 100%;
      margin: 0 0 16px 0;
      text-align: left;
      button {
        margin: 5px;
      }
    }
    &-info {
      display: inline-block;
      width: 32%;
      vertical-align: bottom;
      >span {
        font-size: 13px;
        float: left;
        width: 80px;
        line-height: 32px;
      }
      div {
        float: left;
        width: 70%;
      }
    }
  }
}
</style>
