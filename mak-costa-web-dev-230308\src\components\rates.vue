<template>
  <div class="main">
    <el-table
      v-loading="loading"
      ref="table"
      class="mainTable"
      :data="rateTableData"
      :header-cell-style='{"backgroundColor": "rgb(244, 244, 245)"}'
      :height="tableHeight"
      stripe
    >
      <el-table-column
        label="#"
        type="index"
        align="center"
        width="60"
      />
      <el-table-column
        prop="name"
        label="名称"
      />
      <el-table-column
        prop="currency"
        label="币种"
      />
      <el-table-column
        prop="exchangerate"
        label="汇率"
        :formatter="fmtExchangerate"
      />
      <el-table-column
        prop="modifyuser"
        label="维护人员"
      />
      <el-table-column
        prop="mtime"
        label="维护时间"
      />
      <el-table-column v-if="power === '1' || power === '5'" label="操作" align="center" min-width="100">
        <template slot-scope="scope">
          <el-button type="text" icon="el-icon-edit" @click="handleUpdateLabor(scope.row)">修改</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 新增/修改 -->
    <el-dialog :title="`修改《${editTitle}》汇率`" :visible.sync="handleModal" :before-close="handleModalClose">
      <el-form :model="formData" class="form-wrap" :rules="rules" ref="ruleForm" size="mini">
        <el-form-item prop="currency" label="币种: " label-width="100px" style="text-align: left; width: 90%;">
          <el-input v-model="formData.currency" size="small" disabled>
          </el-input>
        </el-form-item>
        <el-form-item prop="exchangerate" label="汇率: " label-width="100px" style="text-align: left; width: 90%;">
          <el-input-number v-model="formData.exchangerate" size="small" placeholder="请填写" controls-position="right">
          </el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="callOf('ruleForm')">取 消</el-button>
        <el-button type="primary" size="mini" @click="handlePostForm('ruleForm')">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import ajax from '../common/axiosHttp'
import { cookies } from '../common/utils'
export default {
  name: 'Rates',
  data () {
    return {
      power: cookies.getCookie('power'),
      loading: false,
      tableHeight: 0,
      rateTableData: [],
      handleModal: false,
      formData: {},
      editTitle: '',
      rules: {
        currency: [
          {
            required: true,
            message: '请填写',
            trigger: 'blur'
          }
        ],
        exchangerate: [
          {
            required: true,
            type: 'number',
            message: '请填写',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    ...mapState(['power_options', 'POWER_DIC'])
  },
  mounted () {
    // 挂载window.onresize事件(动态设置table高度)
    this.$nextTick(() => {
      this.tableHeight = window.innerHeight - 136
    })
    let _this = this
    window.onresize = () => {
      if (_this.resizeFlag) {
        clearTimeout(_this.resizeFlag)
      }
      _this.resizeFlag = setTimeout(() => {
        _this.getTableHeight()
        _this.resizeFlag = null
      }, 100)
    }
  },
  activated () {
    this.getRateList()
  },
  methods: {
    // 格式化汇率
    fmtExchangerate (row, column, cellValue) {
      return cellValue.toFixed(2)
    },
    // 计算table高度(动态设置table高度)
    getTableHeight () {
      let tableH = 180 // 距离页面下方的高度
      let tableHeightDetil = window.innerHeight - tableH
      if (tableHeightDetil <= 300) {
        this.tableHeight = 300
      } else {
        this.tableHeight = window.innerHeight - tableH
      }
    },
    // 获取用户列表
    getRateList () {
      this.loading = true
      ajax({
        method: 'POST',
        url: '/api/selectMoney'
      }).then(res => {
        this.loading = false
        this.rateTableData = res
      }).catch(() => {
        this.loading = false
      })
    },
    // 修改
    handleUpdateLabor (row) {
      this.editTitle = row.name
      this.formData = row
      this.handleModal = true
    },
    // 关闭提醒
    handleModalClose (done) {
      this.$confirm('确认关闭吗？').then(_ => {
        done()
        this.formData = {}
        this.$refs['ruleForm'].resetFields()
        this.getRateList()
      })
        .catch(_ => {})
    },
    // 关闭模态框
    callOf (formName) {
      this.handleModal = false
      this.formData = {}
      this.$refs[formName].resetFields()
      this.getRateList()
    },
    // 提交保存
    handlePostForm (formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.formData.modifyuser = cookies.getCookie('userName')
          ajax({
            method: 'POST',
            url: '/api/updateMoney',
            data: this.formData
          }).then(res => {
            this.$message({
              offset: 80,
              message: '保存成功！',
              type: 'success'
            })
            this.handleModal = false
            this.getRateList()
          })
        }
      })
    },
    // 删除
    handleDelete (row) {
      this.$confirm(`您是否要删除当前汇率?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ajax({
          method: 'POST',
          url: '/api/deleteUser',
          data: {
            userid: row.userid
          }
        }).then(res => {
          this.$message({
            offset: 80,
            type: 'success',
            message: '操作成功！'
          })
          this.current = 1
          this.getRateList()
        })
      }).catch(() => {
        this.$message({
          offset: 80,
          type: 'info',
          message: '已取消删除！'
        })
      })
    }
  }
}
</script>
<style lang="less" scoped>
.main {
  height:100vh;
  overflow:hidden;
  /deep/ .el-select, .el-input, .el-input-number, .el-input_inner {
    width: 100%;
  }
  /deep/ .el-input__inner {
    text-align: left;
  }
  /deep/ .el-form-item--mini .el-form-item__content, .el-form-item--mini .el-form-item__label {
    text-align: left;
  }
  .query {
    // margin-bottom: 20px;
    &-wrapper {
      display: inline-block;
      width: 100%;
      margin: 0 0 16px 0;
      text-align: left;
      button {
        margin: 5px;
      }
    }
    &-info {
      display: inline-block;
      width: 100%;
      vertical-align: bottom;
      >span {
        font-size: 13px;
        float: left;
        width: 80px;
        line-height: 32px;
      }
      div {
        float: left;
        width: 70%;
      }
    }
  }
  .page {
    margin: 20px 0;
    text-align: center;
  }
  .topBtn {
    float: left;
    margin: 0 0 24px 0;
  }
  .addBtn {
    float: right;
    margin: 0 24px 32px;
  }
}
</style>
