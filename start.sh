#!/bin/bash

echo "========================================"
echo "   快速启动 - Makrite 成本核算系统"
echo "========================================"

echo "正在启动后端服务..."
cd cost-accounting-system-master

if [ -f "target/yw-0.0.1-SNAPSHOT.jar" ]; then
    echo "服务地址: http://localhost:7000"
    echo "按 Ctrl+C 停止服务"
    echo "========================================"
    java -jar target/yw-0.0.1-SNAPSHOT.jar
else
    echo "未找到构建文件，请先运行 ./deploy-local.sh 进行完整部署"
fi
