package com.zt.yw.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 字典、字典项异常枚举
 *
 * <AUTHOR>
 * @date 2021-07-06 15:03:28
 */
@Getter
@AllArgsConstructor
public enum DictResultCode implements IResultCode {
    /**
     * 404：字典不存在
     */
    NOT_FOUND(404, "字典不存在"),
    /**
     * 405：字典编码已存在
     */
    CODE_REPEAT(405, "名称已存在，请勿重复提交"),
    /**
     * 406：字典编码不允许修改
     */
    CODE_NOT_ALLOWED_MODIFY(406, "字典编码不允许修改"),
    /**
     * 407：默认字典，不允许删除
     * 407：该字典禁止删除
     */
    NOT_ALLOWED_DELETE(407, "该字典禁止删除"),
    /**
     * 408：字典存在字典项，不允许删除
     */
    EXIST_DICT_DATA(408, "字典存在字典项，不允许删除");

    /**
     * 状态码
     */
    private final int code;
    /**
     * 提示信息
     */
    private final String message;

}