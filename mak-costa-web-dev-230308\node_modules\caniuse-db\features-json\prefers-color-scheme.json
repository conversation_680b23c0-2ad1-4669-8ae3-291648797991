{"title": "prefers-color-scheme media query", "description": "Media query to detect if the user has set their system to use a light or dark color theme.", "spec": "https://drafts.csswg.org/mediaqueries-5/#prefers-color-scheme", "status": "wd", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-color-scheme", "title": "MDN article"}, {"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=1494034", "title": "Firefox implementation bug"}, {"url": "https://bugs.chromium.org/p/chromium/issues/detail?id=889087", "title": "Chromium implementation issue"}, {"url": "https://web.dev/prefers-color-scheme/", "title": "Web.dev article"}], "bugs": [], "categories": ["CSS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "79": "y", "80": "y", "81": "y", "83": "y", "84": "y", "85": "y", "86": "y", "87": "y", "88": "y", "89": "y", "90": "y", "91": "y", "92": "y", "93": "y", "94": "y", "95": "y", "96": "y", "97": "y", "98": "y", "99": "y", "100": "y", "101": "y", "102": "y", "103": "y", "104": "y", "105": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n", "54": "n", "55": "n", "56": "n", "57": "n", "58": "n", "59": "n", "60": "n", "61": "n", "62": "n", "63": "n", "64": "n", "65": "n", "66": "n", "67": "y", "68": "y", "69": "y", "70": "y", "71": "y", "72": "y", "73": "y", "74": "y", "75": "y", "76": "y", "77": "y", "78": "y", "79": "y", "80": "y", "81": "y", "82": "y", "83": "y", "84": "y", "85": "y", "86": "y", "87": "y", "88": "y", "89": "y", "90": "y", "91": "y", "92": "y", "93": "y", "94": "y", "95": "y", "96": "y", "97": "y", "98": "y", "99": "y", "100": "y", "101": "y", "102": "y", "103": "y", "104": "y", "105": "y", "106": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n", "54": "n", "55": "n", "56": "n", "57": "n", "58": "n", "59": "n", "60": "n", "61": "n", "62": "n", "63": "n", "64": "n", "65": "n", "66": "n", "67": "n", "68": "n", "69": "n", "70": "n", "71": "n", "72": "n", "73": "n", "74": "n", "75": "n", "76": "y", "77": "y", "78": "y", "79": "y", "80": "y", "81": "y", "83": "y", "84": "y", "85": "y", "86": "y", "87": "y", "88": "y", "89": "y", "90": "y", "91": "y", "92": "y", "93": "y", "94": "y", "95": "y", "96": "y", "97": "y", "98": "y", "99": "y", "100": "y", "101": "y", "102": "y", "103": "y", "104": "y", "105": "y", "106": "y", "107": "y", "108": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "n", "11.1": "n", "12": "n", "12.1": "y", "13": "y", "13.1": "y", "14": "y", "14.1": "y", "15": "y", "15.1": "y", "15.2-15.3": "y", "15.4": "y", "15.5": "y", "15.6": "y", "16.0": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n", "54": "n", "55": "n", "56": "n", "57": "n", "58": "n", "60": "n", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y", "70": "y", "71": "y", "72": "y", "73": "y", "74": "y", "75": "y", "76": "y", "77": "y", "78": "y", "79": "y", "80": "y", "81": "y", "82": "y", "83": "y", "84": "y", "85": "y", "86": "y", "87": "y", "88": "y", "89": "y", "90": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11.0-11.2": "n", "11.3-11.4": "n", "12.0-12.1": "n", "12.2-12.5": "n", "13.0-13.1": "y", "13.2": "y", "13.3": "y", "13.4-13.7": "y", "14.0-14.4": "y", "14.5-14.8": "y", "15.0-15.1": "y", "15.2-15.3": "y", "15.4": "y", "15.5": "y", "15.6": "y", "16.0": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "105": "y"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "64": "y"}, "and_chr": {"105": "y"}, "and_ff": {"104": "y"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"12.12": "n"}, "samsung": {"4": "n", "5.0-5.4": "n", "6.2-6.4": "n", "7.2-7.4": "n", "8.2": "n", "9.2": "n", "10.1": "n", "11.1-11.2": "n", "12.0": "y", "13.0": "y", "14.0": "y", "15.0": "y", "16.0": "y", "17.0": "y", "18.0": "y"}, "and_qq": {"10.4": "n"}, "baidu": {"7.12": "n"}, "kaios": {"2.5": "n"}}, "notes": "Support will also depend on whether or not the OS has support for a light/dark theme preference.", "notes_by_num": {}, "usage_perc_y": 93.93, "usage_perc_a": 0, "ucprefix": false, "parent": "", "keywords": "prefers-dark-interface", "ie_id": "", "chrome_id": "5109758977638400", "firefox_id": "", "webkit_id": "", "shown": true}