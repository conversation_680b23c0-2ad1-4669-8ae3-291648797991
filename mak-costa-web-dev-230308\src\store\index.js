import Vue from 'vue'
import Vuex from 'vuex'
import ajax from '../common/axiosHttp'

Vue.use(Vuex)

const state = {
  FXRates: {},
  ct_options: [
    {value: 'CNY', label: '人民币(CNY)'},
    {value: 'USD', label: '美元(USD)'},
    {value: 'USDP', label: '美元(利润)'},
    {value: 'EURP', label: '欧元(利润)'},
    {value: 'AUDP', label: '澳币(利润)'},
    {value: 'CADP', label: '加币(利润)'},
    {value: 'GBPP', label: '英镑(利润)'},
    {value: '100JPYP', label: '一百日元(利润)'}
  ],
  CURRENCY_DIC: {
    'CNY': '人民币(CNY)',
    'USD': '美元(USD)',
    'USDP': '美元(利润)',
    'EURP': '欧元(利润)',
    'AUDP': '澳币(利润)',
    'CADP': '加币(利润)',
    'GBPP': '英镑(利润)',
    '100JPYP': '一百日元(利润)'
  },
  standard_options: [
    // {value: 'universal', label: '通用'},
    {value: 'niosh', label: '美规'},
    {value: 'eu', label: '欧规'},
    {value: 'la', label: '国标'},
    {value: 'ds', label: '日规'},
    {value: 'others', label: '其它'}
  ],
  type_options: [
    {value: 'NONE', label: '无'},
    {value: 'V', label: '气阀'},
    {value: 'OV', label: '活性碳'},
    {value: 'VOV', label: '气阀+活性碳'}
  ],
  TYPE_DIC: {
    'NONE': '无',
    'V': '气阀',
    'OV': '活性碳',
    'VOV': '气阀+活性碳'
  },
  model_options: [
    {value: '1', label: '一片式'},
    {value: '2', label: '折叠式'},
    {value: '3', label: '杯状式'}
  ],
  MODEL_DIC: {
    '1': '一片式',
    '2': '折叠式',
    '3': '杯状式'
  },
  itemType_options: [
    {value: 'out', label: '外层'},
    {value: 'mid', label: '中层'},
    {value: 'in', label: '内层'},
    {value: 'nasal', label: '鼻夹'},
    {value: 'binding', label: '松紧带'},
    {value: 'foam', label: '鼻垫'},
    {value: 'others', label: '其它'}
  ],
  ITEM_DIC: {
    'out': '外层',
    'mid': '中层',
    'in': '内层',
    'nasal': '鼻夹',
    'binding': '松紧带',
    'foam': '鼻垫',
    'others': '其它'
  },
  power_options: [
    {value: 1, label: '开发者'},
    {value: 2, label: '物料维护员'},
    {value: 6, label: '工角维护员'},
    {value: 7, label: '业务员'},
    {value: 8, label: '管理员'}
  ],
  company_options: [
    'C1台北',
    'C2知腾',
    'C4上海',
    'C5迅安'
  ],
  POWER_DIC: {
    1: '开发者',
    2: '物料维护员',
    6: '工角维护员',
    7: '业务员',
    8: '管理员'
  },
  unit_options: [
    {value: 'KG', lable: 'KG'},
    {value: '㎡', lable: '㎡'},
    {value: 'PCS', lable: 'PCS'},
    {value: '件', lable: '件'},
    {value: '卷', lable: '卷'}
  ],
  pk_itemType_options: [
    {value: 'plastic', label: '胶袋'},
    {value: 'colorbox', label: '彩盒'},
    {value: 'card', label: '彩/吊卡'},
    {value: 'certificate', label: '合格证'},
    {value: 'instructions', label: '说明书'},
    {value: 'listershell', label: '泡壳'},
    {value: 'tape', label: '封箱胶带'},
    {value: 'carton', label: '外箱'},
    {value: 'innerbox', label: '内箱'},
    {value: 'paperboard', label: '纸盖/纸板'},
    {value: 'paperpallet', label: '纸栈板'},
    {value: 'coil', label: '卷料'},
    {value: 'paperlining', label: '白纸衬'},
    {value: 'seal', label: '绑封口'},
    {value: 'labeling', label: '贴标'},
    {value: 'others', label: '其它'}
  ],
  PK_ITEM_DIC: {
    'paperlining': '白纸衬',
    'seal': '绑封口',
    'tape': '封箱胶带',
    'labeling': '贴标',
    'instructions': '说明书',
    'card': '彩/吊卡',
    'plastic': '胶袋',
    'coil': '卷料',
    'colorbox': '彩盒',
    'carton': '外箱',
    'innerbox': '内箱',
    'paperboard': '纸盖/纸板',
    'paperpallet': '纸栈板',
    'listershell': '泡壳',
    'certificate': '合格证',
    'others': '其它'
  },
  historiesList: [
    {
      content: [
        '复制功能优化人工工角选项功能；',
        '工角配置页面增加生产工厂条件查询；',
        '其它细节优化。'
      ],
      timestamp: '2025-07-21',
      color: '#0bbd87',
      size: 'large'
    },
    {
      content: [
        '报价单运费计算、对比功能价格计算、报价单剔除功能计算公式优化；',
        '优化基础模版数据与主品数据展现形式；',
        '增加多币种利润汇率。'
      ],
      timestamp: '2025-07-04'
    },
    {
      content: [
        '主品页面增加基础模版置顶功能；',
        '主品页面优化多条件查询；',
        '增加默认分页显示条数。'
      ],
      timestamp: '2025-06-09'
    },
    {
      content: [
        '报价单新增运费剔除管销功能；',
        '增加业务员删除权限；',
        '报价单增加日元汇率；',
        '已知bug修复及代码优化。'
      ],
      timestamp: '2025-05-20'
    },
    {
      content: [
        '支持利润手输入精确到小数十分位；',
        '对比功能增加包装信息；',
        '新增品名输入命名规范提示。'
      ],
      timestamp: '2025-03-10'
    },
    {
      content: [
        '系列配置增加物料种类选择功能；',
        '对比列表名称增加工厂、销售类别显示；',
        '规范物料种类名称。'
      ],
      timestamp: '2025-01-15'
    },
    {
      content: [
        '主品新增完成人工工角校验功能；',
        '复制功能优化：复制工角下拉远程查询；',
        '运输配置区分内外销计算，并支持运费修改。'
      ],
      timestamp: '2025-01-02'
    },
    {
      content: [
        '人工工角按生产工厂区分；',
        '复制功能优化：剥离包装工角和人工工角；',
        '调整开发者和管理员权限。'
      ],
      timestamp: '2024-12-19'
    },
    {
      content: [
        '修复主品新增步骤bug；',
        '新增美元（利润）币种字典；',
        '物料和包装配置优化单位选择功能；',
        '主品页面增加查询条件；',
        '列表后台排序优化。'
      ],
      timestamp: '2024-12-6'
    },
    {
      content: [
        '修复彩盒包装散货数量计算异常的bug；',
        '报价单和对比功能美元采用手动汇率(6.9)，与物料汇率区分使用；',
        '增加物料和包装单位修改提示；增加主品名称命名提示',
        '新增按物料品号查询主品信息功能；',
        '修复繁简体模糊搜索功能。'
      ],
      timestamp: '2024-11-22'
    },
    {
      content: [
        '新增或复制中，包装材料单价可以修改；',
        '完成包装材料单价变更提醒功能。'
      ],
      timestamp: '2024-10-25'
    },
    {
      content: [
        '修复成本计算公式bug；',
        '完善修改时间刷新功能；',
        '新增查看历史版本记录功能。'
      ],
      timestamp: '2024-09-18'
    },
    {
      content: [
        '物料和包装单项新增剔除管销功能；',
        '修复运费获取失败的bug；',
        '主品列表增加修改时间。'
      ],
      timestamp: '2024-09-10'
    },
    {
      content: [
        '材料配置增加未填写提示；',
        '复制功能优化：选择是否包含包装数据；',
        '修改主品弹窗bug修复；',
        '成本计算公式bug修复。'
      ],
      timestamp: '2024-08-27'
    },
    {
      content: [
        '运输配置功能细节优化；',
        '导出格式异常修复；',
        '增加货柜容量提醒；',
        '报价单内外销默认币种切换显示。'
      ],
      timestamp: '2024-08-12'
    },
    {
      content: [
        '新增主品物料配置新增单选及重置功能；',
        '新增包装常用材料用量自动计算功能；',
        '运输配置贸易条件整合，去除货柜尺寸选择。'
      ],
      timestamp: '2024-07-31'
    }
  ]
}
const mutations = {
  FX_RATES: (state, data) => {
    state.FXRates = {
      'CNY': '1.00',
      // 'TWD': '0.23',
      ...data
    }
  }
}
const actions = {
  getFXRates: ({commit}) => {
    return ajax({
      method: 'POST',
      url: '/api/selectMoney'
    }).then(res => {
      let obj = {}
      res.forEach(item => {
        obj[item.currency] = Number(item.exchangerate).toFixed(2)
      })
      commit('FX_RATES', obj)
      console.log('state.FXRates', state.FXRates)
    })
  }
}
const store = new Vuex.Store({
  state,
  mutations,
  actions
})
export default store
