<template>
  <div class="main">
    <div class="query">
      <div class="query-wrapper">
        <div class="query-info">
          <span>主品名称：</span>
          <el-input
            v-model="queryProductName"
            size="mini"
            placeholder="请输入主品名称查询"
            @keyup.enter.native="seach"
            clearable>
          </el-input>
        </div>
        <div class="query-info">
          <span>物料品号：</span>
          <el-input
            v-model="queryProductNo"
            size="mini"
            placeholder="请输入物料品号查询"
            @keyup.enter.native="seach"
            clearable>
          </el-input>
        </div>
        <div class="query-info">
          <span>客户公司：</span>
          <el-input
            v-model="productConnection"
            size="mini"
            placeholder="请输入客户公司查询"
            @keyup.enter.native="seach"
            clearable>
          </el-input>
        </div>
        <div class="query-info">
          <span>创建人员：</span>
          <el-input
            v-model="creator"
            size="mini"
            placeholder="请输入创建人员查询"
            @keyup.enter.native="seach"
            clearable>
          </el-input>
        </div>
        <div class="query-info" style="margin-top: 12px;">
          <el-button size="mini" @click="seach" icon="el-icon-search" type="primary" plain>查询</el-button>
          <el-button size="mini" @click="seachMine" icon="el-icon-view" type="primary" plain>我的</el-button>
          <el-button size="mini" @click="standardsDrawer = true" icon="el-icon-tickets" type="success">标准</el-button>
        </div>
        <div class="query-info" style="text-align:right; width: 18%;">
          <el-button type="primary" size="mini" icon="el-icon-plus" @click="createProductForm">新增</el-button>
          <el-badge :value="selsection_ids.length" class="item">
            <el-button type="success" size="mini" icon="el-icon-paperclip" @click="handleMutiple" plain>对比</el-button>
          </el-badge>
        </div>
      </div>
    </div>
    <!-- 主品表格 -->
    <el-table
      v-loading="loading"
      ref="multipleTable"
      class="mainTable"
      :data="tableList"
      :header-cell-style='{"backgroundColor": "rgb(244, 244, 245)"}'
      :height="tableHeight"
      @selection-change="handleCourseSelection"
      stripe
    >
    <el-table-column
      type="selection"
      width="50">
    </el-table-column>
      <el-table-column
        prop="productName"
        label="主品名称"
      >
      </el-table-column>
      <el-table-column
        prop="productConnection"
        label="客户公司"
        width="150"
      >
      </el-table-column>
      <el-table-column
        prop="productFactory"
        label="生产工厂"
        width="120"
      >
      </el-table-column>
      <el-table-column
        prop="productPackage"
        label="包装信息"
        minWidth="150"
      >
        <template slot-scope="scope">
           {{scope.row.bagCount ? '每袋' + scope.row.pcsCount + '个、每盒' + scope.row.bagCount + '袋、每箱' + scope.row.boxCount+ '盒' : '泡壳包装，每盒' + scope.row.pcsCount + '个、每箱' + scope.row.boxCount + '盒' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="productDetail"
        label="产品说明"
        minWidth="150"
      >
      </el-table-column>
      <el-table-column
        prop="creator"
        label="创建人员"
        width="120"
      >
      </el-table-column>
      <el-table-column
        prop="creationDate"
        label="创建时间"
      >
        <template #header>
          <div>
            <span>创建时间</span>
            <br>
            <span>修改时间</span>
          </div>
        </template>
        <template slot-scope="scope">
          {{ scope.row.creationDate }}
          <br>
          {{ scope.row.updateTime }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="120">
        <template slot-scope="scope">
          <el-button type="text" icon="el-icon-document" @click="handleDetail(scope.row)">报价单</el-button>
          <el-button type="text" icon="el-icon-document-copy" @click="copyProductForm(scope.row)">复制</el-button>
          <el-dropdown>
            <span style="margin-left: 12px; cursor: pointer; color: #409EFF;">
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <el-button type="text" icon="el-icon-time" @click="viewhHistory(scope.row)">历史记录</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button v-if="power === '1' || user === scope.row.creator" type="text" icon="el-icon-edit" @click="updateProductForm(scope.row)">修改主品</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button v-if="power === '1' || user === scope.row.creator" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除主品</el-button>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="page">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentNum"
        :page-sizes="[50, 100, 150, 200]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalNum">
      </el-pagination>
    </div>
    <!-- 基础模版表格 -->
    <el-drawer
      title="标准模版列表"
      :visible.sync="standardsDrawer"
      direction="btt"
      size="85%"
      >
      <el-table
        v-loading="loading"
        ref="multipleStandardsTable"
        class="mainTable"
        :data="standardTableList"
        :header-cell-style='{"backgroundColor": "rgb(244, 244, 245)"}'
        @selection-change="handleStandardsSelection"
        stripe
      >
      <el-table-column
        type="selection"
        width="50">
      </el-table-column>
        <el-table-column
          prop="productName"
          label="主品名称"
        >
        </el-table-column>
        <el-table-column
          prop="productConnection"
          label="客户公司"
        >
        </el-table-column>
        <el-table-column
          prop="productFactory"
          label="生产工厂"
        >
        </el-table-column>
        <el-table-column
          prop="productPackage"
          label="包装信息"
        >
          <template slot-scope="scope">
            {{scope.row.bagCount ? '每袋' + scope.row.pcsCount + '个、每盒' + scope.row.bagCount + '袋、每箱' + scope.row.boxCount+ '盒' : '泡壳包装，每盒' + scope.row.pcsCount + '个、每箱' + scope.row.boxCount + '盒' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="creator"
          label="创建人员"
        >
        </el-table-column>
        <el-table-column
          prop="creationDate"
          label="创建时间"
        >
          <template #header>
            <div>
              <span>创建时间</span>
              <br>
              <span>修改时间</span>
            </div>
          </template>
          <template slot-scope="scope">
            {{ scope.row.creationDate }}
            <br>
            {{ scope.row.updateTime }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="text" icon="el-icon-document" @click="handleDetail(scope.row)">报价单</el-button>
            <el-button type="text" icon="el-icon-document-copy" @click="copyProductForm(scope.row)">复制</el-button>
            <el-dropdown>
              <span style="margin-left: 12px; cursor: pointer; color: #409EFF;">
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button type="text" icon="el-icon-time" @click="viewhHistory(scope.row)">历史记录</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="power === '1' || user === scope.row.creator" type="text" icon="el-icon-edit" @click="updateProductForm(scope.row)">修改主品</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="power === '1' || user === scope.row.creator" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除主品</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </el-drawer>
    <!-- 新增主品 -->
    <el-dialog id="configDlg" title="新增主品" :visible.sync="handleAddModal" @closed="handleMainClose" :close-on-click-modal="false" :show-close="false" top="12vh" width="90%">
      <el-steps
        :active="stepIndex"
        align-center
        style="margin:12px 0 24px 0;"
        simple
        finish-status="success"
        process-status="wait"
      >
        <el-step title="主品配置" icon="el-icon-collection"></el-step>
        <el-step title="物料配置" icon="el-icon-box"></el-step>
        <el-step title="包装配置" icon="el-icon-receiving"></el-step>
        <el-step title="工角配置" icon="el-icon-timer"></el-step>
        <el-step title="运输配置" icon="el-icon-truck"></el-step>
      </el-steps>
      <!-- 主品配置 -->
      <div v-show="stepIndex === 1">
        <el-form :model="productFormData" :rules="pr_rules" ref="productForm">
          <el-form-item prop="productName" label="主品名称：">
            <el-input v-model="productFormData.productName" size="small" placeholder="请填写客户公司对应的主品名称" clearable>
            </el-input>
          </el-form-item>
          <el-form-item prop="productConnection" label="客户公司：">
            <el-input v-model="productFormData.productConnection" size="small" placeholder="请填写客户公司" clearable>
            </el-input>
          </el-form-item>
          <el-form-item prop="productFactory" label="生产工厂：" style="text-align: left;">
            <el-radio-group v-model="productFormData.productFactory" size="medium">
              <el-radio label="迅安"></el-radio>
              <el-radio label="知腾"></el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="productDetail" label="主品描述：">
            <el-input v-model="productFormData.productDetail" size="small" placeholder="请填写产品详细信息" clearable>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <!-- 物料配置 -->
      <el-form :model="productFormData" :rules="ma_rules" ref="materialForm" v-show="stepIndex === 2">
        <el-form-item prop="productStandard" label="标准：" label-width="80px" style="text-align: left;">
          <el-radio-group v-model="productFormData.productStandard" @input="selectStandard">
            <el-radio v-for="(op, i) in standard_options" :key="i+op" :label="op.label"></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="productSuccession" label="系列：" label-width="80px" style="text-align: left;">
          <el-radio-group v-model="productFormData.productSuccession" @input="selectSuccession">
            <el-radio v-for="(op, i) in successionList" :key="i" :label="op"></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="productType" label="型号：" label-width="80px" style="text-align: left;">
          <el-radio-group v-model="productFormData.productType" @input="selectType">
            <el-radio v-for="op in typeList" :key="op.sc_id" :label="op.uuid" :disabled="!!productFormData.productType">{{ op.sc_type }}</el-radio>
          </el-radio-group>
          <!-- 重置型号 -->
          <el-button v-if="productFormData.productType" style="margin: 0 0 0 24px;" type="text" @click="selectStandard">重置</el-button>
        </el-form-item>
        <el-form-item prop="productItems" label="材料：" label-width="80px" style="text-align: left;">
          <template>
            <el-form :model="itemDataForm" ref="itemDataForm">
              <el-form-item prop="out" label="外层：" label-width="80px">
                <el-checkbox-group v-model="itemDataForm.out" @change="handleBoxChange($event, 'out')">
                  <el-checkbox v-for="(op, i) in out_options" :key="i" :label="op" :checked="true">{{ op.rm_material }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item prop="mid" label="中层：" label-width="80px">
                <el-checkbox-group v-model="itemDataForm.mid" @change="handleBoxChange($event, 'mid')">
                  <el-checkbox v-for="(op, i) in mid_options" :key="i" :label="op" :checked="false">{{ op.rm_material }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item prop="in" label="内层：" label-width="80px">
                <el-checkbox-group v-model="itemDataForm.in" @change="handleBoxChange($event, 'in')">
                  <el-checkbox v-for="(op, i) in in_options" :key="i" :label="op" :checked="true">{{ op.rm_material }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item prop="nasal" label="鼻夹：" label-width="80px">
                <el-checkbox-group v-model="itemDataForm.nasal" @change="handleBoxChange($event, 'nasal')">
                  <el-checkbox v-for="(op, i) in nasal_options" :key="i" :label="op" :checked="true">{{ op.rm_material }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item prop="binding" label="松紧带：" label-width="80px">
                <el-checkbox-group v-model="itemDataForm.binding" @change="handleBoxChange($event, 'binding')">
                  <el-checkbox v-for="(op, i) in binding_options" :key="i" :label="op" :checked="true">{{ op.rm_material }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item prop="foam" label="鼻垫：" label-width="80px">
                <el-checkbox-group v-model="itemDataForm.foam" @change="handleBoxChange($event, 'foam')">
                  <el-checkbox v-for="(op, i) in foam_options" :key="i" :label="op" :checked="true">{{ op.rm_material }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item prop="others" label="其它：" label-width="80px">
                <el-checkbox-group v-model="itemDataForm.others" @change="handleBoxChange($event, 'others')">
                  <el-checkbox v-for="(op, i) in others_options" :key="i" :label="op" :checked="true">{{ op.rm_material }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form>
          </template>
        </el-form-item>
      </el-form>
      <!-- 包装配置 -->
      <div v-show="stepIndex == 3">
        <el-form inline style="text-align: left;white-space: nowrap;margin-left: 12px;">
        <el-form-item label="包装方式：">
          <el-radio-group v-model="packageType" @input="onTypeChange" size="mini">
            <el-radio-button label="彩盒"></el-radio-button>
            <el-radio-button label="泡壳"></el-radio-button>
            <el-radio-button label="袋装"></el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-form>
        <el-form v-if="packageType === '彩盒'" :model="productFormData" :rules="pk_rules" ref="packageForm" inline style="text-align: left;">
          <el-form-item prop="pcsCount" label="包装规格：">
            <el-input-number v-model="productFormData.pcsCount" size="mini" :min="1" controls-position="right" style="width: 60%" clearable/> PCS / 袋，
          </el-form-item>
          <el-form-item prop="bagCount">
            <el-input-number v-model="productFormData.bagCount" size="mini" :min="1" controls-position="right" style="width: 60%" clearable/> 袋 / 盒，
          </el-form-item>
          <el-form-item prop="boxCount">
            <el-input-number v-model="productFormData.boxCount" size="mini" :min="1" controls-position="right" style="width: 60%" clearable/> 盒 / 箱。
          </el-form-item>
        </el-form>
        <el-form v-if="packageType === '泡壳'" :model="productFormData" :rules="pk_rules" ref="packageForm" inline style="text-align: left;">
          <el-form-item prop="pcsCount" label="包装规格：">
            <el-input-number v-model="productFormData.pcsCount" size="mini" :min="1" controls-position="right" style="width: 60%" clearable/> PCS / 泡壳，
          </el-form-item>
          <el-form-item prop="boxCount">
            <el-input-number v-model="productFormData.boxCount" size="mini" :min="1" controls-position="right" style="width: 60%" clearable/> 泡壳 / 箱。
          </el-form-item>
        </el-form>
        <el-form v-if="packageType === '袋装'" :model="productFormData" :rules="pk_rules" ref="packageForm" inline style="text-align: left;">
          <el-form-item prop="pcsCount" label="包装规格：">
            <el-input-number v-model="productFormData.pcsCount" size="mini" :min="1" controls-position="right" style="width: 60%" clearable/> PCS / 袋，
          </el-form-item>
          <el-form-item prop="boxCount">
            <el-input-number v-model="productFormData.boxCount" size="mini" :min="1" controls-position="right" style="width: 60%" clearable/> 袋 / 箱。
          </el-form-item>
        </el-form>
        <el-container v-show="packageType" style="height:500px;">
          <el-aside width="200px">
            <el-input
              prefix-icon="el-icon-search"
              placeholder="输入关键字查询"
              v-model="filterText"
              size="mini"
              clearable
            >
              <el-button slot="append" @click="drawer = true" disabled>新建</el-button>
            </el-input>
            <el-tree
              class="filter-tree"
              :data="pkTreeData"
              :props="defaultProps"
              :filter-node-method="filterNode"
              node-key="packid"
              ref="tree"
              show-checkbox
              :default-expand-all="false"
              @check="subRoleTreeCheck"
            />
          </el-aside>
          <el-main>
            <div style="text-align: left;">
              <p v-if="packageType==='彩盒'">========<b style="color: #1989fa;">彩盒</b>基本包装包含：<b style="color: #1989fa;">胶袋、彩盒、合格证、外箱、封箱胶带</b>等物料，提交前请确认是否完整========</p>
              <p v-if="packageType==='泡壳'">========<b style="color: #1989fa;">泡壳</b>基本包装包含：<b style="color: #1989fa;">泡壳、彩/吊卡、说明书、外箱、封箱胶带</b>等物料，提交前请确认是否完整========</p>
              <p v-if="packageType==='袋装'">========<b style="color: #1989fa;">袋装</b>基本包装包含：<b style="color: #1989fa;">胶袋、彩/吊卡、说明书、外箱、封箱胶带</b>等物料，提交前请确认是否完整========</p>
              <p v-for="(item, i) in packageFormData" :key="item.packid">
                <span style="width: 29%;display: inline-block;">{{ i+1 +`、名称：`+ item.pk_material }}</span>
                <span style="width: 18%;display: inline-block;">尺寸：{{ item.pk_length ? item.pk_length + '" x ' + item.pk_width + '" x ' + item.pk_height + '"' :  '/'}}</span>
                <span style="width: 15%;display: inline-block;">单价：
                  <el-input-number v-model="item.pk_price" size="mini" controls-position="right" style="width:60%" @change="calculatePackage(item)" clearable/>
                </span>
                <!-- <span style="width: 10%;display: inline-block;">单价：{{ item.pk_price }}</span> -->
                <span style="width: 20%;display: inline-block;">用量：<el-input-number v-model="item.pk_consumption" size="mini" controls-position="right" style="width:60%" @change="calculatePackage(item)" clearable></el-input-number></span>
                <span style="width: 14%;display: inline-block;">金额：{{ item.pk_amounts }}</span>
              </p>
            </div>
          </el-main>
        </el-container>
      </div>
      <!-- 工角配置 -->
      <div v-show="stepIndex == 4">
        <el-form :model="LCFormData" ref="LCForm" :rules="lcFormRules" size="mini" inline style="width: 70%;margin: 0px 0px 24px;display: inline-flex;text-align: left;">
          <el-form-item prop="lcData">
            <el-radio-group v-model="LCFormData.lcData">
              <el-radio v-for="op in laborCostList" :key="op.lc_id" :label="op">{{ op.lc_name }} 【{{ op.lc_factory }}】 单价：
                <el-input-number v-model="op.lc_price" size="mini" controls-position="right" style="width: 30%" clearable />
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-form :model="PKLCTableFormData" ref="PKLCTableForm" :rules="pklcRules" size="mini" inline style="width: 70%;margin: 0px 0px 24px;display: inline-flex;text-align: left;">
          <el-form-item prop="lc_name" label="包装统一工价：" />
          <el-form-item prop="lc_price" label="单价(RMB)：">
            <el-input-number v-model="PKLCTableFormData.lc_price" controls-position="right" placeholder="请填写" :step="1" :precision="5" clearable />
          </el-form-item>
          <el-form-item>
            (包装统一报价，含全部包装工角单价)
          </el-form-item>
        </el-form>
      </div>
      <!-- 运输配置 -->
      <div v-show="stepIndex == 5">
        <div style="overflow: hidden;">
          <el-form id="shipping" :model="freightFormData" :rules="fr_rules" ref="freightForm">
            <el-form-item prop="goodsCount" label="订单数量：" label-width="100px" style="text-align: left; width: 80%;">
              <el-input-number v-model="freightFormData.goodsCount" size="mini" placeholder="不确定订单数量可输入0" controls-position="right" @blur="handleCalculateCTN(freightFormData, productFormData, packageFormData)">
              </el-input-number>
              <span class="tips">不确定订单数量可输入"0"，整柜默认按950材积、散货按2000个彩盒进行计算</span>
            </el-form-item>
            <el-form-item prop="ctn" label="CTN：" label-width="100px" style="text-align: left; width: 80%;">
              <el-input-number v-model="freightFormData.ctn" size="mini" placeholder="CTN如果不满一箱按一箱进行计算" controls-position="right">
              </el-input-number>
              <span class="tips">CTN如果不满一箱按一箱进行计算</span>
            </el-form-item>
            <el-form-item prop="cmb" label="CBM：" label-width="100px" style="text-align: left; width: 80%;">
              <el-input-number v-model="freightFormData.cmb" size="small" placeholder="请输入货柜体积" controls-position="right">
              </el-input-number>
            </el-form-item>
            <el-form-item prop="sales_type" label="销售类型：" label-width="100px" style="text-align: left;">
              <el-radio-group v-model="freightFormData.sales_type">
                <el-radio label="外销"/>
                <el-radio label="内销"/>
              </el-radio-group>
            </el-form-item>
            <!-- 外销 -->
            <template v-if="freightFormData.sales_type && freightFormData.sales_type == '外销'">
              <el-form-item prop="factory" label="出货工厂：" label-width="100px" style="text-align: left;">
                <el-radio-group v-model="freightFormData.factory">
                  <el-radio label="知腾"/>
                  <el-radio label="迅安"/>
                </el-radio-group>
              </el-form-item>
              <el-form-item prop="port" label="出货港口：" label-width="100px" style="text-align: left;">
                <el-radio-group v-model="freightFormData.port">
                  <el-radio label="上海"/>
                  <el-radio label="武汉"/>
                  <el-radio label="深圳"/>
                </el-radio-group>
              </el-form-item>
              <el-form-item prop="terms" label="贸易条件：" label-width="100px" style="text-align: left;">
                <el-radio-group v-model="freightFormData.terms" @change="onTermsChange">
                  <el-radio label="FOB"/>
                  <el-radio label="Ex Works"/>
                  <el-radio label="To Door"/>
                  <el-radio label="CIF"/>
                </el-radio-group>
              </el-form-item>
                <!-- Exit Work无需运费 -->
              <template v-if="freightFormData.terms && freightFormData.terms === 'Ex Works'">
                <el-form-item prop="expense" label="运费(RMB):" label-width="100px" style="text-align: left; width: 49%;">
                  0.00 (Exit Work不产生运费)
                </el-form-item>
              </template>
              <!-- CIF -->
              <template v-if="freightFormData.terms && freightFormData.terms === 'CIF'">
                <el-form-item prop="shippingType" label="出货类别：" label-width="100px" style="text-align: left;">
                  <el-radio-group v-model="freightFormData.shippingType">
                    <el-radio label="整柜"/>
                    <el-radio v-if="freightFormData.port !== '武汉'" label="散货"/>
                  </el-radio-group>
                </el-form-item>
                <el-form-item prop="expense" label="运费(RMB):" label-width="100px" style="text-align: left; width: 49%;">
                  <el-input-number v-model="freightFormData.expense" size="small" placeholder="请填写运费" controls-position="right">
                  </el-input-number>
                  <span>(CIF包含运费及报关费)</span>
                </el-form-item>
              </template>
              <!-- To Door -->
              <template v-if="freightFormData.terms && freightFormData.terms === 'To Door'">
                <el-form-item prop="shippingType" label="出货类别：" label-width="100px" style="text-align: left;">
                  <el-radio-group v-model="freightFormData.shippingType">
                    <el-radio label="整柜"/>
                    <el-radio v-if="freightFormData.port !== '武汉'" label="散货"/>
                  </el-radio-group>
                </el-form-item>
                <el-form-item prop="expense" label="运费(RMB):" label-width="100px" style="text-align: left; width: 49%;">
                  <el-input-number v-model="freightFormData.expense" size="small" placeholder="请填写运费" controls-position="right">
                  </el-input-number>
                </el-form-item>
              </template>
                <!-- FOB分整柜和散货 -->
              <template v-if="freightFormData.terms && freightFormData.terms === 'FOB'">
                <el-form-item prop="shippingType" label="出货类别：" label-width="100px" style="text-align: left;">
                  <el-radio-group v-model="freightFormData.shippingType">
                    <el-radio label="整柜"/>
                    <el-radio v-if="freightFormData.port !== '武汉'" label="散货"/>
                  </el-radio-group>
                </el-form-item>
                <el-form-item prop="expense" label="运输综合费用(RMB)：" label-width="156px" style="text-align: left; width: 49%;">
                  <el-input-number v-model="freightFormData.expense" size="small" placeholder="请输入金额" controls-position="right">
                  </el-input-number>
                </el-form-item>
              </template>
            </template>
            <!-- 内销 -->
            <template v-if="freightFormData.sales_type && freightFormData.sales_type == '内销'">
              <el-form-item prop="factory" label="出货工厂：" label-width="100px" style="text-align: left;">
                <el-radio-group v-model="freightFormData.factory">
                  <el-radio label="知腾"/>
                  <el-radio label="迅安"/>
                </el-radio-group>
              </el-form-item>
              <el-form-item prop="terms" label="贸易条件：" label-width="100px" style="text-align: left;">
                <el-radio-group v-model="freightFormData.terms" @change="onTermsChange">
                  <el-radio label="Ex Works"/>
                  <el-radio label="To Door"/>
                </el-radio-group>
              </el-form-item>
                <!-- Exit Work无需运费 -->
              <template v-if="freightFormData.terms && freightFormData.terms === 'Ex Works'">
                <el-form-item prop="expense" label="运费(RMB):" label-width="100px" style="text-align: left; width: 49%;">
                  0.00 (Exit Work不产生运费)
                </el-form-item>
              </template>
              <!-- To Door -->
              <template v-if="freightFormData.terms && freightFormData.terms === 'To Door'">
                <el-form-item prop="shippingType" label="出货类别：" label-width="100px" style="text-align: left;">
                  <el-radio-group v-model="freightFormData.shippingType">
                    <el-radio label="整柜"/>
                    <el-radio label="散货"/>
                  </el-radio-group>
                </el-form-item>
                <el-form-item prop="expense" label="运费(RMB):" label-width="100px" style="text-align: left; width: 49%;">
                  <el-input-number v-model="freightFormData.expense" size="small" placeholder="请填写运费" controls-position="right">
                  </el-input-number>
                </el-form-item>
              </template>
            </template>
          </el-form>
          <div style="width: 30%;float: left;text-align: left;">
            <h3 style="margin:0 0 36px 0;">货品信息</h3>
            <p style="margin:0 0 36px 0;">单箱材积(cuft)：{{ cuft }}</p>
            <p style="margin:0 0 36px 0;">货品总体积(CBM)：{{ CBM_ALL }}</p>
            <p style="margin:0 0 36px 0;">20尺货柜容量(PCS)：{{ CTN_20INCHI }}</p>
            <p style="margin:0 0 36px 0;">40尺货柜容量(PCS)：{{ CTN_40INCHI }}</p>
            <p v-if="packageType==='彩盒'" style="color: #409EFF;margin:0 0 36px 0;">{{ alertSTR }}</p>
            <p v-if="freightFormData.goodsCount > CTN_20INCHI" style="color: #F56C6C;margin:0 0 36px 0;">注意：当前订单数量已超过20尺货柜最大容量，请选择更大尺寸的货柜！</p>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="callOf">取 消</el-button>
        <el-button size="mini" v-if="stepIndex!=1" @click="stepIndex--">上一步</el-button>
        <!-- <el-button size="mini" v-if="stepIndex!=5" @click="stepIndex++">下一步</el-button> -->
        <el-button size="mini" v-if="stepIndex!=5" @click="handleNextStep">下一步</el-button>
        <el-button type="primary" size="mini" v-if="stepIndex==5" @click="postAddMain('freightForm')">保 存</el-button>
      </div>
    </el-dialog>
    <!-- 历史记录 -->
    <el-dialog :title="`《${editTitle}》修改历史记录`" :visible.sync="historyModal" width="90%">
      <el-collapse v-model="activeNames">
          <!-- 原材料 -->
          <el-collapse-item title="物料" name="rm">
            <el-table
              :data="historyTableData.rawMaterialTableData"
              :header-cell-style='{"backgroundColor": "rgb(217, 236, 255)"}'
              :row-style='{"backgroundColor": "rgb(236, 245, 255)"}'
              style="width: 100%"
              class="list-table"
              max-height="540"
              >
              <el-table-column label="#" type="index" align="center" width="50" />
              <el-table-column label="品号" align="left" prop="rm_number" />
              <el-table-column label="品名" align="left" prop="rm_material" />
              <el-table-column label="厂商" align="center" prop="rm_supplier" />
              <el-table-column label="历史单价" align="center" prop="rm_price" />
              <el-table-column label="单位" align="center" prop="rm_unit" />
              <el-table-column label="更新时间" align="center" prop="updateTime" sortable />
              <el-table-column label="更新人员" align="center" prop="updatePersonnel" />
            </el-table>
          </el-collapse-item>
          <!-- 包装费 -->
          <el-collapse-item title="包装" name="pk">
            <el-table
              :data="historyTableData.packageTableData"
              :header-cell-style='{"backgroundColor": "rgb(250, 236, 216)"}'
              :row-style='{"backgroundColor": "rgb(253, 246, 236)"}'
              style="width: 100%"
              class="list-table"
              max-height="540"
              >
              <el-table-column label="#" type="index" align="center" width="50" />
              <el-table-column label="品号" align="left" prop="pk_number" />
              <el-table-column label="品名" align="left" prop="pk_material" />
              <el-table-column label="厂商" align="center" prop="pk_supplier" />
              <el-table-column label="历史单价" align="center" prop="pk_price" />
              <el-table-column label="单位" align="center" prop="pk_unit" />
              <el-table-column label="更新时间" align="center" prop="updateTime" sortable />
              <el-table-column label="更新人员" align="center" prop="updatePersonnel" />
            </el-table>
          </el-collapse-item>
          <!-- 人工费 -->
          <el-collapse-item class="lc-collapse" title="工角" name="lc">
            <el-table
              :data="historyTableData.LaborCostTableData"
              :header-cell-style='{"backgroundColor": "rgb(225, 243, 216)"}'
              :row-style='{"backgroundColor": "rgb(240, 249, 235)"}'
              style="width: 100%"
              class="list-table"
              max-height="540"
              >
              <el-table-column label="#" type="index" align="center" width="50" />
              <el-table-column label="品名" align="left" prop="lc_name" />
              <el-table-column label="历史单价" align="center" prop="lc_price" />
              <el-table-column label="单位" align="center" prop="lc_unit" />
              <el-table-column label="更新时间" align="center" prop="updateTime" sortable />
              <el-table-column label="更新人员" align="center" prop="updatePersonnel" />
            </el-table>
          </el-collapse-item>
        </el-collapse>
      <div slot="footer">
        <el-button size="mini" @click="historyModal=false">关 闭</el-button>
      </div>
    </el-dialog>
    <!-- 价格对比 -->
    <el-dialog width="70%" :visible.sync="handleMutiplelModal" :close-on-click-modal="false" :show-close="false" @closed="handleMutiplelModalClose" top="10vh">
      <el-button type="primary" size="mini" icon="el-icon-download" plain round style="position:absolute;top: 80px; right:90px" @click="exportDBPNG">导出</el-button>
      <div ref="htmlDBDom" style="padding: 24px 0;">
        <div style="padding:0 0 24px 0;line-height: 24px;font-size: 18px;color: #303133;">价格对比</div>
          <div class="db-collapse">
            <el-row style="margin-bottom: 24px;"><span style="margin-left: 54px;color:rgb(25, 137, 250)">******请注意：此处外销包含3‰保险，内销包含13%税率******</span></el-row>
            <el-row style="margin-bottom: 24px;">
              <el-col :offset="1" :span="10">
                <span style="width: 80px">币种：</span>
                <el-select v-model="db_tt_currencyType" @change="handleDbCTchange" size="mini" style="width:70%;">
                  <el-option
                    v-for="item in ct_options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :offset="1" :span="10">
                <span style="width: 80px">汇率：</span>
                <!-- 输入框 -->
                <el-input-number
                  style="width:70%;"
                  size="mini"
                  :min="0"
                  controls-position="right"
                  v-model="db_totalRate"
                  @change="handleDbRateChange"
                />
              </el-col>
            </el-row>
          </div>
        <el-row>
          <el-col :span="2" :offset="1">
            <div class="col" style="font-weight: bold;">主品名称：</div>
            <div class="col" style="font-weight: bold;">包装信息：</div>
            <div class="col" style="font-weight: bold;">物料费用：</div>
            <div class="col" style="font-weight: bold;">包装费用：</div>
            <div class="col" style="font-weight: bold;">人工费用：</div>
            <div class="col" style="font-weight: bold;">运输综合费用：</div>
            <div class="col" style="font-weight: bold;">成本：</div>
            <div class="col" style="font-weight: bold;">综合成本：</div>
            <div class="col" style="font-weight: bold;">利润5%：</div>
            <div class="col" style="font-weight: bold;">利润10%：</div>
            <div class="col" style="font-weight: bold;">利润15%：</div>
            <div class="col" style="font-weight: bold;">利润20%：</div>
            <div class="col" style="font-weight: bold;">利润25%：</div>
            <div class="col" style="font-weight: bold;">利润30%：</div>
          </el-col>
          <el-col :span="dynamicSpan" v-for="(item, index) in contrastData" :key="index" :class="item.styleText">
            <div class="col" style="font-weight: bold; font-size: 16px; text-align: center;">{{ item.productName }}</div>
            <el-divider />
            <div class="col" style="text-align: center;">{{ item.packageMsg }}</div>
            <el-divider />
            <div class="col" style="text-align: center;">{{item.rm_sum}}</div>
            <el-divider />
            <div class="col" style="text-align: center;">{{item.pk_sum}}</div>
            <el-divider />
            <div class="col" style="text-align: center;">{{item.lc_sum}}</div>
            <el-divider />
            <div class="col" style="text-align: center;">{{item.fr_sum}}</div>
            <el-divider />
            <div class="col" style="text-align: center;font-weight: bold;color: #1989fa;">{{item.cost}}</div>
            <el-divider />
            <div class="col" style="text-align: center;font-weight: bold;">{{item.total}}</div>
            <el-divider />
            <div class="col" style="text-align: center;font-weight: bold;">{{(item.total + item.total*0.05).toFixed(5)}}</div>
            <el-divider />
            <div class="col" style="text-align: center;font-weight: bold;">{{(item.total + item.total*0.1).toFixed(5)}}</div>
            <el-divider />
            <div class="col" style="text-align: center;font-weight: bold;">{{(item.total + item.total*0.15).toFixed(5)}}</div>
            <el-divider />
            <div class="col" style="text-align: center;font-weight: bold;">{{(item.total + item.total*0.2).toFixed(5)}}</div>
            <el-divider />
            <div class="col" style="text-align: center;font-weight: bold;">{{(item.total + item.total*0.25).toFixed(5)}}</div>
            <el-divider />
            <div class="col" style="text-align: center;font-weight: bold;">{{(item.total + item.total*0.3).toFixed(5)}}</div>
          </el-col>
        </el-row>
      </div>
      <div slot="footer">
        <el-button size="mini" @click="handleMutiplelModalClose">关 闭</el-button>
      </div>
    </el-dialog>
    <!-- 报价单 -->
    <el-dialog width="90%" :visible.sync="handleDetailModal" :close-on-click-modal="false" :show-close="false" top="10vh" @closed="handleDetailModalClose">
      <el-button type="primary" size="mini" icon="el-icon-download" plain round style="position:absolute;top: 144px; right:80px" @click="exportPNG">导出</el-button>
      <span style="font-size: 12px;color: #409EFF;position:absolute;top: 286px; right:80px">******提示：勾选物料和包装可剔除管销******</span>
      <div id="pdfDom" style="padding: 0 20px 20px;" ref="htmlDom">
        <div style="padding: 24px 0;line-height: 24px;font-size: 18px;color: #303133;">《{{editTitle}}》报价单</div>
        <el-descriptions size="small" :column="2">
          <el-descriptions-item label="品号">{{detailObjData.postForm.uuid}}</el-descriptions-item>
          <el-descriptions-item label="品名">{{detailObjData.postForm.productName}}</el-descriptions-item>
          <el-descriptions-item label="创建人员">{{detailObjData.postForm.creator}}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{detailObjData.postForm.creationDate}}</el-descriptions-item>
          <el-descriptions-item label="生产工厂">{{detailObjData.postForm.productFactory}}</el-descriptions-item>
          <el-descriptions-item label="客户公司">{{detailObjData.postForm.productConnection}}</el-descriptions-item>
          <el-descriptions-item label="订单数量">{{detailObjData.freightTableData[0].goodsCount}}PCS</el-descriptions-item>
          <el-descriptions-item label="包装信息">{{'每袋' + detailObjData.postForm.pcsCount + 'pcs、每盒' +detailObjData.postForm.bagCount + '袋、每箱' +detailObjData.postForm.boxCount+ '盒'}}</el-descriptions-item>
          <el-descriptions-item label="销售类型">{{detailObjData.freightTableData[0].sales_type}}</el-descriptions-item>
          <el-descriptions-item label="产品说明">{{detailObjData.postForm.productDetail}}</el-descriptions-item>
        </el-descriptions>
        <el-collapse v-model="activeNames">
          <!-- 原材料 -->
          <el-collapse-item :title="`物料合计(RMB)：    ${rm_sum}`" name="rm">
            <el-table
              :data="detailObjData.rawMaterialTableData"
              :header-cell-style='{"backgroundColor": "rgb(217, 236, 255)"}'
              :row-style='{"backgroundColor": "rgb(236, 245, 255)"}'
              show-summary
              style="width: 100%"
              class="list-table"
              :summary-method="getSummariesRM"
              @selection-change="handleRMStatusChange"
              >
              <el-table-column label="#" type="index" align="center" width="50" />
              <el-table-column label="品名" align="left" prop="rm_material" />
              <el-table-column label="厂商" align="center" prop="rm_supplier" />
              <el-table-column label="单价(RMB)" align="center" prop="rm_price" />
              <el-table-column label="单位" align="center" prop="rm_unit" />
              <el-table-column label="用量" align="center" prop="rm_consumption" />
              <el-table-column label="金额(RMB)" align="center" prop="rm_amounts" sortable />
              <el-table-column type="selection" width="55" align="center" label="xxxx" />
            </el-table>
          </el-collapse-item>
          <!-- 包装费 -->
          <el-collapse-item :title="`包装费合计(RMB)：    ${pk_sum}`" name="pk">
              <el-table
                :data="detailObjData.packageTableData"
                :header-cell-style='{"backgroundColor": "rgb(250, 236, 216)"}'
                :row-style='{"backgroundColor": "rgb(253, 246, 236)"}'
                style="width: 100%"
                class="list-table"
                show-summary
                :summary-method="getSummariesPK"
                @selection-change="handlePKStatusChange"
              >
                <el-table-column label="#" type="index" align="center" width="50" />
                <el-table-column label="品名" align="left" prop="pk_material" />
                <el-table-column label="厂商" align="center" prop="pk_supplier" />
                <el-table-column label="单价(RMB)" align="center" prop="pk_price" />
                <!-- 价格变更会在此处显示 -->
                <el-table-column label="原价(RMB)" align="center">
                  <template slot-scope="scope">
                    <span v-for="(item, key) in FilterPackageArr" :key=key style="color:#F56C6C;">
                      {{ scope.row.pk_number === item.pk_number && scope.row.pk_price !== item.pk_price ? item.pk_price : '' }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="单位" align="center" prop="pk_unit" />
                <el-table-column label="用量" align="center" prop="pk_consumption" />
                <el-table-column label="金额(RMB)" align="center" prop="pk_amounts" />
                <el-table-column type="selection" width="55" align="center" />
            </el-table>
          </el-collapse-item>
          <!-- 工角费 -->
          <el-collapse-item class="lc-collapse" :title="`人工费合计(RMB)：    ${lc_sum}`" name="lc">
            <el-table
              :data="detailObjData.LaborCostTableData"
              :header-cell-style='{"backgroundColor": "rgb(225, 243, 216)"}'
              :row-style='{"backgroundColor": "rgb(240, 249, 235)"}'
              style="width: 100%"
              class="list-table"
              show-summary
              :summary-method="getSummariesLC"
              >
              <el-table-column label="#" type="index" align="center" width="50" />
              <el-table-column label="工角名称" align="left" prop="lc_name" />
              <el-table-column label="所属工厂" align="left" prop="lc_factory" />
              <el-table-column label="单价(RMB)" align="center" prop="lc_price" />
              <!-- <el-table-column label="单位" align="center" prop="lc_unit" /> -->
              <!-- <el-table-column label="用量" align="center" prop="lc_consumption" /> -->
              <!-- <el-table-column label="汇率" align="center" prop="lc_exchangeRate" /> -->
              <el-table-column label="金额(RMB)" align="center" prop="lc_amounts" />
            </el-table>
            <div class="lc-percent">
              <span>人工费百分比：</span>
              <!-- 输入框 -->
              <el-input-number
                style="width:50%;"
                size="mini"
                :min="0"
                :max="300"
                controls-position="right"
                v-model="lc_percentage"
                @change="getSummariesLC"
              />
              %
            </div>
          </el-collapse-item>
          <!-- 运费 -->
          <el-collapse-item id="freightCSS" :title="`运输综合费用(RMB)：    ${fr_sum}`" name="fr">
            <el-descriptions size="small" :column="3">
              <el-descriptions-item v-if="isDomesticSales=='外销'" label="港口">{{'从' + detailObjData.freightTableData[0].factory + '到' + detailObjData.freightTableData[0].port}}</el-descriptions-item>
              <el-descriptions-item v-if="isDomesticSales=='内销'" label="出货工厂">{{detailObjData.postForm.productFactory}}</el-descriptions-item>
              <el-descriptions-item label="订单数量">{{detailObjData.freightTableData[0].goodsCount}}PCS</el-descriptions-item>
              <el-descriptions-item label="贸易条件">{{detailObjData.freightTableData[0].terms}}</el-descriptions-item>
              <el-descriptions-item v-if="detailObjData.freightTableData[0].shippingType" label="出货类别">{{detailObjData.freightTableData[0].shippingType}}</el-descriptions-item>
              <el-descriptions-item v-if="detailObjData.freightTableData[0].cmb" label="货柜体积">{{detailObjData.freightTableData[0].cmb + '立方米'}}</el-descriptions-item>
              <el-descriptions-item v-if="detailObjData.freightTableData[0].ctn" label="CTN">{{detailObjData.freightTableData[0].ctn}}</el-descriptions-item>
              <el-descriptions-item label="运费(RMB)">
                <template>
                  <el-input style="width:160px;" v-model="detailObjData.freightTableData[0].expense" size="mini" @blur="calculateFR" />
                </template>
              </el-descriptions-item>
              <el-descriptions-item v-if="detailObjData.freightTableData[0].cif" label="CIF">{{detailObjData.freightTableData[0].cif}}</el-descriptions-item>
              <el-descriptions-item  label="剔除管销">
                <template>
                  <el-checkbox v-model="fr_boolean" @change="handleFRchange"></el-checkbox>
                </template>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
          <!-- 报价 -->
          <el-collapse-item title="最终报价" name="tt">
            <div class="tt-collapse">
              <div class="tt-left">
                <div>
                  <span style="width: 80px">销售类型：</span>
                  {{ isDomesticSales=='内销' ? '内销' : '外销（含3‰保险）' }}
                </div>
                <div v-if="isDomesticSales=='内销'">
                  <span style="width: 80px">内销税率：</span>
                  <!-- 输入框 -->
                  <el-input-number
                    style="width:70%;"
                    size="mini"
                    :min="0"
                    :max="300"
                    controls-position="right"
                    v-model="saleRate"
                  />
                  %
                </div>
                <div>
                  <span style="width: 80px">管销费：</span>
                  <!-- 输入框 -->
                  <el-input-number
                    style="width:70%;"
                    size="mini"
                    :min="0"
                    :max="300"
                    controls-position="right"
                    v-model="marketing"
                  />
                  %
                </div>
                <div>
                  <span style="width: 80px">币种：</span>
                  <el-select v-model="tt_currencyType" @change="handleCTchange" size="mini" style="width:70%;">
                    <el-option
                      v-for="item in ct_options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </div>
                <div>
                  <span style="width: 80px">汇率：</span>
                  <!-- 输入框 -->
                  <el-input-number
                    style="width:70%;"
                    size="mini"
                    :min="0"
                    controls-position="right"
                    v-model="totalRate"
                  />
                </div>
              </div>
              <el-table
                :data="ttTableData"
                class="tt-right"
                max-height="540"
                stripe>
                <el-table-column label="利润" align="center" prop="column">
                  <template slot-scope="scope">
                    <div v-if="scope.row.meta">
                      <el-input-number style="width:40%;" v-model="scope.row.column" controls-position="right" :min="0" :step="1" :precision="1" size="mini" clearable @change="handleRateChange(scope.row)" /> %
                    </div>
                    <span v-else>{{scope.row.column}}</span>
                  </template>
                </el-table-column>
                <el-table-column label="价格" align="center" prop="row"/>
              </el-table>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
      <div slot="footer">
        <el-button size="mini" @click="handleDetailModalClose">关 闭</el-button>
      </div>
    </el-dialog>
    <!-- 修改 -->
    <el-dialog :title="`修改《${editTitle}》配置`" width="90%" :visible.sync="handleEditModal" :close-on-click-modal="false" :show-close="false" @closed="handleEditModalClose">
      <el-descriptions size="small" :column="2">
        <el-descriptions-item label="品号">{{detailObjData.postForm.uuid}}</el-descriptions-item>
        <el-descriptions-item label="品名">{{detailObjData.postForm.productName}}</el-descriptions-item>
        <el-descriptions-item label="创建人员">{{detailObjData.postForm.creator}}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{detailObjData.postForm.creationDate}}</el-descriptions-item>
        <el-descriptions-item label="生产工厂">{{detailObjData.freightTableData[0].factory}}</el-descriptions-item>
        <el-descriptions-item label="客户公司">{{detailObjData.postForm.productConnection}}</el-descriptions-item>
        <el-descriptions-item label="订单数量">{{detailObjData.freightTableData[0].goodsCount}}PCS</el-descriptions-item>
        <el-descriptions-item label="包装信息">{{'每袋' + detailObjData.postForm.pcsCount + 'pcs、每盒' + detailObjData.postForm.bagCount + '袋、每箱' + detailObjData.postForm.boxCount + '盒'}}</el-descriptions-item>
        <el-descriptions-item label="销售类型">{{detailObjData.freightTableData[0].sales_type}}</el-descriptions-item>
        <el-descriptions-item label="产品说明">{{detailObjData.postForm.productDetail}}</el-descriptions-item>
      </el-descriptions>
      <el-collapse v-model="activeNames">
        <!-- 原材料 -->
        <el-collapse-item title="原材料" name="rm">
          <el-button :disabled="rm_btn" class="addBtn" type="primary" size="mini" icon="el-icon-plus" @click="addRawMaterialRow">新增</el-button>
          <el-form :model="detailObjData" ref="rmTableForm" :rules="rmRules" size="small">
            <el-table
              :data="detailObjData.rawMaterialTableData"
              :header-cell-style='{"backgroundColor": "rgb(217, 236, 255)"}'
              :row-style='{"backgroundColor": "rgb(236, 245, 255)"}'
              style="width: 100%"
              class="list-table"
              >
              <el-table-column
                label="#"
                type="index"
                align="center"
                width="50">
              </el-table-column>
              <el-table-column label="品号" align="left" prop="rm_number" min-width="160">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'rawMaterialTableData.' + scope.$index + '.rm_number'"
                  >
                    <el-autocomplete
                      v-model="scope.row.rm_number"
                      :popper-append-to-body="false"
                      :fetch-suggestions="queryRmNumberSearchAsync"
                      placeholder="请填品号"
                      @select="handleRmNumberSelect"
                      clearable
                    />
                  </el-form-item>
                  <span v-else>{{scope.row.rm_number}}</span>
                </template>
              </el-table-column>
              <el-table-column label="品名" align="left" prop="rm_material" min-width="160">
                <template #header>
                  <div>
                    <span>品名：</span>
                    <span class="nameTips">总称：颜色+材质+尺寸+特性(+厂商)</span>
                  </div>
                </template>
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'rawMaterialTableData.' + scope.$index + '.rm_material'"
                    :rules="rmRules.rm_material"
                  >
                    <el-autocomplete
                      v-model="scope.row.rm_material"
                      :fetch-suggestions="queryNameSearchAsync"
                      placeholder="请填写物料名称"
                      :popper-append-to-body="false"
                      @select="handleNameSelect"
                      clearable
                    />
                  </el-form-item>
                  <span v-else>{{scope.row.rm_material}}</span>
                </template>
              </el-table-column>
              <el-table-column label="单价" align="center" prop="rm_price">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'rawMaterialTableData.' + scope.$index + '.rm_price'"
                    :rules="rmRules.rm_price"
                  >
                    <el-input-number v-model="scope.row.rm_price" controls-position="right" placeholder="请填写" :min="0" clearable />
                  </el-form-item>
                  <span v-else>{{scope.row.rm_price}}</span>
                </template>
              </el-table-column>
              <el-table-column label="单位" align="center" prop="rm_unit">
                <template slot-scope="scope">
                  <span>{{scope.row.rm_unit}}</span>
                </template>
              </el-table-column>
              <el-table-column label="用量" align="center" prop="rm_consumption">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'rawMaterialTableData.' + scope.$index + '.rm_consumption'"
                    :rules="rmRules.rm_consumption"
                  >
                    <el-input-number v-model="scope.row.rm_consumption" controls-position="right" placeholder="请填写" :min="0" clearable />
                  </el-form-item>
                  <span v-else>{{scope.row.rm_consumption}}</span>
                </template>
              </el-table-column>
              <el-table-column label="币种" align="center" prop="rm_currencyType">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'rawMaterialTableData.' + scope.$index + '.rm_currencyType'"
                    :rules="rmRules.rm_currencyType"
                  >
                    <el-select v-model="scope.row.rm_currencyType" @change="handleCurrencyChange(scope.row, 'rm_exchangeRate', 'rm_currencyType')" size="mini">
                      <el-option
                        v-for="item in ct_options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <span v-else>{{CURRENCY_DIC[scope.row.rm_currencyType]}}</span>
                </template>
              </el-table-column>
              <el-table-column label="汇率" align="center" prop="rm_exchangeRate">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'rawMaterialTableData.' + scope.$index + '.rm_exchangeRate'"
                    :rules="rmRules.rm_exchangeRate"
                  >
                    <el-input-number v-model="scope.row.rm_exchangeRate" controls-position="right" placeholder="请填写" :min="0" clearable />
                  </el-form-item>
                  <span v-else>{{scope.row.rm_exchangeRate}}</span>
                </template>
              </el-table-column>
              <el-table-column label="损耗" align="center" prop="rm_loss">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'rawMaterialTableData.' + scope.$index + '.rm_loss'"
                    :rules="rmRules.rm_loss"
                  >
                    <el-input-number v-model="scope.row.rm_loss" controls-position="right" placeholder="请填写" :step="0.1" :min="0" clearable />
                  </el-form-item>
                  <span v-else>{{scope.row.rm_loss}}</span>
                </template>
              </el-table-column>
              <el-table-column label="金额" align="center" prop="rm_amounts">
                  <template slot-scope="scope">
                    <el-button v-if="scope.row.showInput" type="text" icon="el-icon-search" @click="calculateRM(scope.row)" />
                    <span>{{scope.row.rm_amounts}}</span>
                  </template>
              </el-table-column>
              <el-table-column label="操作" align="center" min-width="100">
                <template slot-scope="scope">
                  <el-button v-if="scope.row.showInput" type="text" icon="el-icon-check" @click.native.prevent="saveRawMaterialRow(scope.row)">提交</el-button>
                  <el-button v-else :disabled="rm_btn" type="text" icon="el-icon-edit" @click="updateRawMaterialRow(scope.row)">修改</el-button>
                  <el-button v-if="scope.row.showInput" type="text" icon="el-icon-close" @click.native.prevent="cancelRawMaterialRow(scope.row)">取消</el-button>
                  <el-button v-else :disabled="rm_btn && !scope.row.showInput" type="text" icon="el-icon-delete" @click.native.prevent="deleteRawMaterialRow(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </el-collapse-item>
        <!-- 包装费 -->
        <el-collapse-item title="包装费" name="pk">
          <el-button :disabled="pk_btn" class="addBtn" type="primary" size="mini" icon="el-icon-plus" @click="addPackageRow">新增</el-button>
          <el-form :model="detailObjData" ref="pkTableForm" :rules="pkRules" size="small">
            <el-table
              :data="detailObjData.packageTableData"
              :header-cell-style='{"backgroundColor": "rgb(250, 236, 216)"}'
              :row-style='{"backgroundColor": "rgb(253, 246, 236)"}'
              style="width: 100%"
              class="list-table"
              >
              <el-table-column
                label="#"
                type="index"
                align="center"
                width="50">
              </el-table-column>
              <el-table-column label="品号" align="left" prop="pk_number" min-width="160">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'packageTableData.' + scope.$index + '.pk_number'"
                  >
                    <el-autocomplete
                      v-model="scope.row.pk_number"
                      :fetch-suggestions="queryPkNumberSearchAsync"
                      placeholder="请填包装品号"
                      @select="handlePkNumberSelect"
                      clearable
                    />
                  </el-form-item>
                  <span v-else>{{scope.row.pk_number}}</span>
                </template>
              </el-table-column>
              <el-table-column label="品名" align="left" prop="pk_material" min-width="160">
                <template #header>
                  <div>
                    <span>品名：</span>
                    <span class="nameTips">总称：颜色+材质+尺寸+特性(+厂商)</span>
                  </div>
                </template>
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'packageTableData.' +scope.$index + '.pk_material'"
                    :rules="pkRules.pk_material"
                  >
                    <el-autocomplete
                      v-model="scope.row.pk_material"
                      :popper-append-to-body="false"
                      :fetch-suggestions="queryPkNameSearchAsync"
                      placeholder="请填写包装名称"
                      @select="handlePkNameSelect"
                      clearable
                    />
                  </el-form-item>
                  <span v-else>{{scope.row.pk_material}}</span>
                </template>
              </el-table-column>
              <el-table-column label="单价" align="center" prop="pk_price">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'packageTableData.' + scope.$index + '.pk_price'"
                    :rules="pkRules.pk_price"
                  >
                    <el-input-number v-model="scope.row.pk_price" controls-position="right" placeholder="请填写" :min="0" clearable />
                  </el-form-item>
                  <span v-else>{{scope.row.pk_price}}</span>
                </template>
              </el-table-column>
              <el-table-column label="单位" align="center" prop="pk_unit">
                <template slot-scope="scope">
                  <span>{{scope.row.pk_unit}}</span>
                </template>
              </el-table-column>
              <el-table-column label="用量" align="center" prop="pk_consumption">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'packageTableData.' + scope.$index + '.pk_consumption'"
                    :rules="pkRules.pk_consumption"
                  >
                    <el-input-number v-model="scope.row.pk_consumption" controls-position="right" placeholder="请填写" :min="0" :precision="5" clearable />
                  </el-form-item>
                  <span v-else>{{scope.row.pk_consumption}}</span>
                </template>
              </el-table-column>
              <el-table-column label="币种" align="center" prop="pk_currencyType">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'packageTableData.' + scope.$index + '.pk_currencyType'"
                    :rules="pkRules.pk_currencyType"
                  >
                    <el-select v-model="scope.row.pk_currencyType" @change="handleCurrencyChange(scope.row, 'pk_exchangeRate', 'pk_currencyType')" size="mini">
                      <el-option
                        v-for="item in ct_options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <span v-else>{{CURRENCY_DIC[scope.row.pk_currencyType]}}</span>
                </template>
              </el-table-column>
              <el-table-column label="汇率" align="center" prop="pk_exchangeRate">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'packageTableData.' + scope.$index + '.pk_exchangeRate'"
                    :rules="pkRules.pk_exchangeRate"
                  >
                    <el-input-number v-model="scope.row.pk_exchangeRate" controls-position="right" placeholder="请填写" :min="0" clearable />
                  </el-form-item>
                  <span v-else>{{scope.row.pk_exchangeRate}}</span>
                </template>
              </el-table-column>
              <el-table-column label="金额" align="center" prop="pk_amounts">
                  <template slot-scope="scope">
                    <el-button v-if="scope.row.showInput" type="text" icon="el-icon-search" @click="calculatePackage(scope.row)" />
                    <span>{{scope.row.pk_amounts}}</span>
                  </template>
              </el-table-column>
              <el-table-column label="操作" align="center" min-width="100">
                <template slot-scope="scope">
                  <el-button v-if="scope.row.showInput" type="text" icon="el-icon-check" @click.native.prevent="savePackageRow(scope.row)">提交</el-button>
                  <el-button v-else :disabled="pk_btn" type="text" icon="el-icon-edit" @click="updatePackageRow(scope.row)">修改</el-button>
                  <el-button v-if="scope.row.showInput" type="text" icon="el-icon-close" @click.native.prevent="cancelPackageRow(scope.row)">取消</el-button>
                  <el-button v-else :disabled="pk_btn && !scope.row.showInput" type="text" icon="el-icon-delete" @click.native.prevent="deletePackageRow(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </el-collapse-item>
        <!-- 人工费 -->
        <el-collapse-item title="人工费" name="lc">
          <el-button :disabled="lc_btn" class="addBtn" type="primary" size="mini" icon="el-icon-plus" @click="addLaborRow">新增</el-button>
          <el-form :model="detailObjData" ref="lcTableForm" :rules="lcRules" size="small">
            <el-table
              :data="detailObjData.LaborCostTableData"
              :header-cell-style='{"backgroundColor": "rgb(225, 243, 216)"}'
              :row-style='{"backgroundColor": "rgb(240, 249, 235)"}'
              style="width: 100%"
              class="list-table"
              >
              <el-table-column
                label="#"
                type="index"
                align="center"
                width="50">
              </el-table-column>
              <el-table-column label="工角名称" align="left" prop="lc_name">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'LaborCostTableData.' + scope.$index + '.lc_name'"
                    :rules="lcRules.lc_name"
                  >
                    <el-autocomplete
                      v-model="scope.row.lc_name"
                      :popper-append-to-body="false"
                      :fetch-suggestions="queryLCNameSearchAsync"
                      placeholder="请填写工角名称"
                      @select="handleLCNameSelect"
                      clearable
                    />
                  </el-form-item>
                  <span v-else>{{scope.row.lc_name}}</span>
                </template>
              </el-table-column>
              <el-table-column label="所属工厂" align="center" prop="lc_factory">
                <template slot-scope="scope">
                  <span>{{scope.row.lc_factory}}</span>
                </template>
              </el-table-column>
              <el-table-column label="单价" align="center" prop="lc_price">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'LaborCostTableData.' + scope.$index + '.lc_price'"
                    :rules="lcRules.lc_price"
                  >
                    <el-input-number v-model="scope.row.lc_price" controls-position="right" placeholder="请填写" :min="0" clearable />
                  </el-form-item>
                  <span v-else>{{scope.row.lc_price}}</span>
                </template>
              </el-table-column>
              <!-- <el-table-column label="单位" align="center" prop="lc_unit">
                <template slot-scope="scope">
                  <span>{{scope.row.lc_unit}}</span>
                </template>
              </el-table-column> -->
              <el-table-column label="用量" align="center" prop="lc_consumption">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'LaborCostTableData.' + scope.$index + '.lc_consumption'"
                    :rules="lcRules.lc_consumption"
                  >
                    <el-input-number v-model="scope.row.lc_consumption" controls-position="right" placeholder="请填写" :min="0" clearable />
                  </el-form-item>
                  <span v-else>{{scope.row.lc_consumption}}</span>
                </template>
              </el-table-column>
              <!-- <el-table-column label="汇率" align="center" prop="lc_exchangeRate">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.showInput"
                    :prop="'LaborCostTableData.' + scope.$index + '.lc_exchangeRate'"
                    :rules="lcRules.lc_exchangeRate"
                  >
                    <el-input-number v-model="scope.row.lc_exchangeRate" controls-position="right" placeholder="请填写" :min="0" clearable />
                  </el-form-item>
                  <span v-else>{{scope.row.lc_exchangeRate}}</span>
                </template>
              </el-table-column> -->
              <el-table-column label="金额" align="center" prop="lc_amounts">
                  <template slot-scope="scope">
                    <el-button v-if="scope.row.showInput" type="text" icon="el-icon-search" @click="calculateLC(scope.row)" />
                    <span>{{scope.row.lc_amounts}}</span>
                  </template>
              </el-table-column>
              <el-table-column label="操作" align="center" min-width="100">
                <template slot-scope="scope">
                  <el-button v-if="scope.row.showInput" type="text" icon="el-icon-check" @click="saveLaborCostRow(scope.row)">提交</el-button>
                  <el-button v-else :disabled="lc_btn" type="text" icon="el-icon-edit" @click="updateLaborRow(scope.row)">修改</el-button>
                  <el-button v-if="scope.row.showInput" type="text" icon="el-icon-close" @click="cancelLaborRow(scope.row)">取消</el-button>
                  <el-button v-else :disabled="lc_btn && !scope.row.showInput" type="text" icon="el-icon-delete" @click.native.prevent="deleteLaborRow(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </el-collapse-item>
        <!-- 运费
        <el-collapse-item title="运费" name="fr">
          <el-descriptions size="small" :column="3">
            <el-descriptions-item v-if="isDomesticSales=='外销'" label="港口">{{'从' + detailObjData.freightTableData[0].factory + '到' + detailObjData.freightTableData[0].port}}</el-descriptions-item>
            <el-descriptions-item v-if="isDomesticSales=='内销'" label="出货工厂">{{detailObjData.postForm.productFactory}}</el-descriptions-item>
            <el-descriptions-item label="订单数量">{{detailObjData.freightTableData[0].goodsCount}}PCS</el-descriptions-item>
            <el-descriptions-item label="贸易条件">{{detailObjData.freightTableData[0].terms}}</el-descriptions-item>
            <el-descriptions-item v-if="detailObjData.freightTableData[0].shippingType" label="出货类别">{{detailObjData.freightTableData[0].shippingType}}</el-descriptions-item>
            <el-descriptions-item v-if="detailObjData.freightTableData[0].cmb" label="货柜体积">{{detailObjData.freightTableData[0].cmb + '立方米'}}</el-descriptions-item>
            <el-descriptions-item v-if="detailObjData.freightTableData[0].ctn" label="CTN">{{detailObjData.freightTableData[0].ctn}}</el-descriptions-item>
            <el-descriptions-item v-if="detailObjData.freightTableData[0].cif" label="CIF">{{detailObjData.freightTableData[0].cif}}</el-descriptions-item>
            <el-descriptions-item label="运费(RMB)">{{ detailObjData.freightTableData[0].expense }}</el-descriptions-item>
          </el-descriptions>
        </el-collapse-item> -->
      </el-collapse>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleModifyClose">关 闭</el-button>
      </div>
    </el-dialog>
    <!-- 复制 -->
    <el-dialog title="复制主品" :visible.sync="handleCopyModal" @closed="callOfCopy" :close-on-click-modal="false" :show-close="false" top="12vh" width="80%">
      <el-form :model="copyProductFormData" :rules="pr_rules" ref="productForm">
        <el-form-item prop="productName" label="主品名称：" label-width="100px">
          <el-input v-model="copyProductFormData.productName" size="small" placeholder="请填写客户公司对应的主品名称" clearable>
          </el-input>
        </el-form-item>
        <el-form-item prop="productConnection" label="客户公司：" label-width="100px">
          <el-input v-model="copyProductFormData.productConnection" size="small" placeholder="请填写客户公司" clearable>
          </el-input>
        </el-form-item>
        <el-form-item prop="productFactory" label="生产工厂：" style="text-align: left;">
            <el-radio-group v-model="copyProductFormData.productFactory" size="medium">
              <el-radio label="迅安"></el-radio>
              <el-radio label="知腾"></el-radio>
            </el-radio-group>
          </el-form-item>
        <el-form-item prop="productDetail" label="主品描述：" label-width="100px">
          <el-input v-model="copyProductFormData.productDetail" size="small" placeholder="请填写产品详细信息" clearable>
          </el-input>
        </el-form-item>
      </el-form>
      <el-form inline style="text-align: left;white-space: nowrap;margin-left: 12px;">
        <el-form-item label="是否复制包装数据：">
          <el-switch
            v-model="isIncludePK"
            active-text="是"
            inactive-text="否"
          >
          </el-switch>
        </el-form-item>
      </el-form>
      <template v-if="!isIncludePK">
        <el-form inline style="text-align: left;white-space: nowrap;margin-left: 12px;">
          <el-form-item label="包装方式：">
            <el-radio-group v-model="packageType" @input="onTypeChange" size="mini">
              <el-radio-button label="彩盒"></el-radio-button>
              <el-radio-button label="泡壳"></el-radio-button>
              <el-radio-button label="袋装"></el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-form v-if="packageType === '彩盒'" :model="copyProductFormData" :rules="pk_rules" ref="packageForm" inline style="text-align: left;white-space: nowrap;">
          <el-form-item prop="pcsCount" label="包装规格：" style="width: 45%;">
            <el-input-number style="width: 80%;" v-model="copyProductFormData.pcsCount" size="mini" :min="1" controls-position="right" clearable/> PCS / 袋
          </el-form-item>
          <el-form-item prop="bagCount" style="width: 25%;">
            <el-input-number style="width: 80%;" v-model="copyProductFormData.bagCount" size="mini" :min="1" controls-position="right" clearable/> 袋 / 盒
          </el-form-item>
          <el-form-item prop="boxCount" style="width: 25%;">
            <el-input-number style="width: 80%;" v-model="copyProductFormData.boxCount" size="mini" :min="1" controls-position="right" clearable/> 盒 / 箱
          </el-form-item>
        </el-form>
        <el-form v-if="packageType === '泡壳'" :model="copyProductFormData" :rules="pk_rules" ref="packageForm" inline style="text-align: left;white-space: nowrap;">
          <el-form-item prop="pcsCount" label="包装规格：" style="width: 45%;">
            <el-input-number style="width: 80%;" v-model="copyProductFormData.pcsCount" size="mini" :min="1" controls-position="right" clearable/> PCS / 泡壳
          </el-form-item>
          <el-form-item prop="boxCount" style="width: 25%;">
            <el-input-number style="width: 80%;" v-model="copyProductFormData.boxCount" size="mini" :min="1" controls-position="right" clearable/> 泡壳 / 箱
          </el-form-item>
        </el-form>
        <el-form v-if="packageType === '袋装'" :model="copyProductFormData" :rules="pk_rules" ref="packageForm" inline style="text-align: left;white-space: nowrap;">
          <el-form-item prop="pcsCount" label="包装规格：" style="width: 45%;">
            <el-input-number style="width: 80%;" v-model="copyProductFormData.pcsCount" size="mini" :min="1" controls-position="right" clearable/> PCS / 袋
          </el-form-item>
          <el-form-item prop="boxCount" style="width: 25%;">
            <el-input-number style="width: 80%;" v-model="copyProductFormData.boxCount" size="mini" :min="1" controls-position="right" clearable/> 袋 / 箱
          </el-form-item>
        </el-form>
        <el-container v-show="packageType" style="height:500px;">
          <el-aside width="200px">
            <el-input
              prefix-icon="el-icon-search"
              placeholder="输入关键字查询"
              v-model="filterText"
              size="mini"
              clearable
            >
              <el-button slot="append" @click="drawer = true" disabled>新建</el-button>
            </el-input>
            <el-tree
              class="filter-tree"
              :data="pkTreeData"
              :props="defaultProps"
              :filter-node-method="filterNode"
              node-key="packid"
              ref="tree"
              show-checkbox
              :default-expand-all="false"
              @check="copySubRoleTreeCheck"
            />
          </el-aside>
          <el-main>
            <div style="text-align: left;">
              <p v-if="packageType==='彩盒'">========<b style="color: #1989fa;">彩盒</b>基本包装包含：<b style="color: #1989fa;">胶袋、彩盒、合格证、外箱、封箱胶带</b>等物料，提交前请确认是否完整========</p>
              <p v-if="packageType==='泡壳'">========<b style="color: #1989fa;">泡壳</b>基本包装包含：<b style="color: #1989fa;">泡壳、彩/吊卡、说明书、外箱、封箱胶带</b>等物料，提交前请确认是否完整========</p>
              <p v-if="packageType==='袋装'">========<b style="color: #1989fa;">袋装</b>基本包装包含：<b style="color: #1989fa;">胶袋、彩/吊卡、说明书、外箱、封箱胶带</b>等物料，提交前请确认是否完整========</p>
              <p v-for="(item, i) in copyPackageFormData" :key="item.packid">
                <span style="width: 25%;display: inline-block;">{{ i+1 +`、名称：`+ item.pk_material }}</span>
                <span style="width: 20%;display: inline-block;">尺寸：{{ item.pk_length ? item.pk_length + '" x ' + item.pk_width + '" x ' + item.pk_height + '"' :  '/'}}</span>
                <span style="width: 15%;display: inline-block;">单价：
                  <el-input-number v-model="item.pk_price" size="mini" controls-position="right" style="width:60%" @change="calculatePackage(item)" clearable/>
                </span>
                <!-- <span style="width: 11%;display: inline-block;">单价：{{ item.pk_price }}</span> -->
                <span style="width: 20%;display: inline-block;">用量：
                  <el-input-number v-model="item.pk_consumption" size="mini" controls-position="right" style="width:60%" @change="calculatePackage(item)" clearable/>
                </span>
                <span style="width: 14%;display: inline-block;">金额：{{ item.pk_amounts }}</span>
              </p>
            </div>
          </el-main>
        </el-container>
        <el-form :model="copyPKLCTableFormData" ref="PKLCTableForm" :rules="pklcRules" size="mini" inline style="text-align: left; margin-top: 24px;">
          <el-form-item prop="lc_name" label="包装工角：">
            <el-input v-model="copyPKLCTableFormData.lc_name" placeholder="请输入工角名称" clearable />
          </el-form-item>
          <el-form-item prop="lc_price" label="价格(RMB)：">
            <el-input-number v-model="copyPKLCTableFormData.lc_price" controls-position="right" placeholder="请填写" :step="1" :precision="5" clearable />
          </el-form-item>
          <el-form-item>
            (包装统一报价，含全部包装工角单价)
          </el-form-item>
        </el-form>
      </template>
      <el-form :model="copyLCTableFormData" ref="CPLCTableForm" :rules="cpLcRules" size="mini" inline style="text-align: left;">
        <el-form-item prop="lc_name" label="人工工角：">
          <el-autocomplete
            v-model="copyLCTableFormData.lc_name"
            :popper-append-to-body="false"
            :fetch-suggestions="queryLCSearchAsync"
            placeholder="请选择"
            @select="handleLCrSelect"
            clearable
          />
        </el-form-item>
        <el-form-item prop="factoryShow" label="所属工厂：" label-width="100px" style="text-align: left;">
          {{ copyProductFormData.productFactory }}
        </el-form-item>
        <el-form-item prop="lc_price" label="价格(RMB)：">
          <el-input-number v-model="copyLCTableFormData.lc_price" controls-position="right" placeholder="请填写" :step="1" :precision="5" clearable />
        </el-form-item>
        <el-form-item>
          (人工统一报价，除了包装以外的工角单价)
        </el-form-item>
      </el-form>
      <div style="overflow: hidden; margin: 36px 0 0 0;">
        <el-form id="copyShipping" :model="copyFreightFormData" :rules="fr_rules" ref="freightForm">
          <el-form-item prop="goodsCount" label="订单数量：" label-width="100px" style="position:relative">
            <el-input-number v-model="copyFreightFormData.goodsCount" size="small" placeholder="不确定订单数量可输入0" controls-position="right" @blur="handleCalculateCopyCTN(copyFreightFormData, copyProductFormData, copyPackageFormData)">
            </el-input-number>
            <span class="tips">不确定订单数量可输入"0"，整柜默认按950材积、散货按2000个彩盒进行计算</span>
          </el-form-item>
          <el-form-item prop="ctn" label="CTN：" label-width="100px">
            <el-input-number v-model="copyFreightFormData.ctn" size="small" placeholder="CTN如果不满一箱按一箱进行计算" controls-position="right">
            </el-input-number>
            <span class="tips">CTN数如果不满一箱按一箱进行计算</span>
          </el-form-item>
          <el-form-item prop="cmb" label="CBM：" label-width="100px">
            <el-input-number v-model="copyFreightFormData.cmb" size="small" placeholder="请输入货柜体积" controls-position="right">
            </el-input-number>
          </el-form-item>
          <el-form-item prop="sales_type" label="销售类型：" label-width="100px" style="text-align: left;">
            <el-radio-group v-model="copyFreightFormData.sales_type">
              <el-radio label="外销"/>
              <el-radio label="内销"/>
            </el-radio-group>
          </el-form-item>
          <!-- 外销 -->
          <template v-if="copyFreightFormData.sales_type && copyFreightFormData.sales_type == '外销'">
            <el-form-item prop="factoryShow" label="出货工厂：" label-width="100px" style="text-align: left;">
              {{ copyProductFormData.productFactory }}
            </el-form-item>
            <el-form-item prop="port" label="出货港口：" label-width="100px" style="text-align: left;">
              <el-radio-group v-model="copyFreightFormData.port">
                <el-radio label="上海"/>
                <el-radio label="武汉"/>
                <el-radio label="深圳"/>
              </el-radio-group>
            </el-form-item>
            <el-form-item prop="terms" label="贸易条件：" label-width="100px" style="text-align: left;">
              <el-radio-group v-model="copyFreightFormData.terms" @change="onTermsChange">
                <el-radio label="FOB"/>
                <el-radio label="Ex Works"/>
                <el-radio label="To Door"/>
                <el-radio label="CIF"/>
              </el-radio-group>
            </el-form-item>
              <!-- Exit Work无需运费 -->
            <template v-if="copyFreightFormData.terms && copyFreightFormData.terms === 'Ex Works'">
              <el-form-item prop="expense" label="运输综合费用(RMB)：" label-width="156px" style="text-align: left; width: 100%;">
                0.00 (Exit Work不产生运费)
              </el-form-item>
            </template>
            <!-- To Door -->
            <template v-if="copyFreightFormData.terms && copyFreightFormData.terms === 'To Door'">
              <el-form-item prop="shippingType" label="出货类别：" label-width="100px" style="text-align: left;">
                <el-radio-group v-model="copyFreightFormData.shippingType">
                  <el-radio label="整柜"/>
                  <el-radio v-if="copyFreightFormData.port !== '武汉'" label="散货"/>
                </el-radio-group>
              </el-form-item>
              <el-form-item prop="expense" label="运输综合费用(RMB)：" label-width="156px" style="text-align: left; width: 100%;">
                <el-input-number v-model="copyFreightFormData.expense" size="small" placeholder="请填写运费" controls-position="right">
                </el-input-number>
              </el-form-item>
            </template>
            <!-- CIF -->
            <template v-if="copyFreightFormData.terms && copyFreightFormData.terms === 'CIF'">
              <el-form-item prop="shippingType" label="出货类别：" label-width="100px" style="text-align: left;">
                <el-radio-group v-model="copyFreightFormData.shippingType">
                  <el-radio label="整柜"/>
                  <el-radio v-if="copyFreightFormData.port !== '武汉'" label="散货"/>
                </el-radio-group>
              </el-form-item>
              <el-form-item prop="expense" label="运输综合费用(RMB)：" label-width="156px" style="text-align: left; width: 100%;">
                <el-input-number v-model="copyFreightFormData.expense" size="small" placeholder="请填写运费" controls-position="right">
                </el-input-number>
                <span>(CIF包含运费及报关费)</span>
              </el-form-item>
            </template>
              <!-- FOB分整柜和散货 -->
            <template v-if="copyFreightFormData.terms && copyFreightFormData.terms === 'FOB'">
              <el-form-item prop="shippingType" label="出货类别：" label-width="100px" style="text-align: left;">
                <el-radio-group v-model="copyFreightFormData.shippingType">
                  <el-radio label="整柜"/>
                  <el-radio v-if="copyFreightFormData.port !== '武汉'" label="散货"/>
                </el-radio-group>
              </el-form-item>
              <el-form-item prop="expense" label="运输综合费用(RMB)：" label-width="156px" style="text-align: left; width: 100%;">
                <el-input-number v-model="copyFreightFormData.expense" size="small" placeholder="请输入金额" controls-position="right">
                </el-input-number>
              </el-form-item>
            </template>
          </template>
          <!-- 内销 -->
          <template v-if="copyFreightFormData.sales_type && copyFreightFormData.sales_type == '内销'">
            <el-form-item prop="factoryShow" label="出货工厂：" label-width="100px" style="text-align: left;">
              {{ copyProductFormData.productFactory }}
            </el-form-item>
            <el-form-item prop="terms" label="贸易条件：" label-width="100px" style="text-align: left;">
              <el-radio-group v-model="copyFreightFormData.terms" @change="onTermsChange">
                <el-radio label="Ex Works"/>
                <el-radio label="To Door"/>
              </el-radio-group>
            </el-form-item>
              <!-- Exit Work无需运费 -->
            <template v-if="copyFreightFormData.terms && copyFreightFormData.terms === 'Ex Works'">
              <el-form-item prop="expense" label="运费(RMB):" label-width="100px" style="text-align: left; width: 49%;">
                0.00 (Exit Work不产生运费)
              </el-form-item>
            </template>
            <!-- To Door -->
            <template v-if="copyFreightFormData.terms && copyFreightFormData.terms === 'To Door'">
              <el-form-item prop="shippingType" label="出货类别：" label-width="100px" style="text-align: left;">
                <el-radio-group v-model="copyFreightFormData.shippingType">
                  <el-radio label="整柜"/>
                  <el-radio label="散货"/>
                </el-radio-group>
              </el-form-item>
              <el-form-item prop="expense" label="运费(RMB):" label-width="100px" style="text-align: left; width: 49%;">
                <el-input-number v-model="copyFreightFormData.expense" size="small" placeholder="请填写运费" controls-position="right">
                </el-input-number>
              </el-form-item>
            </template>
          </template>
        </el-form>
        <div style="width: 40%;float: left;text-align: left;margin: 0 0 0 64px;">
          <h3 style="margin:0 0 36px 0;">货品信息</h3>
          <p style="margin:0 0 36px 0;">单箱材积(cuft)：{{ cuft }}</p>
          <p style="margin:0 0 36px 0;">货品总体积(CBM)：{{ CBM_ALL }}</p>
          <p style="margin:0 0 36px 0;">20尺货柜容量(PCS)：{{ CTN_20INCHI }}</p>
          <p style="margin:0 0 36px 0;">40尺货柜容量(PCS)：{{ CTN_40INCHI }}</p>
          <p v-if="packageType==='彩盒'" style="color: #409EFF; margin:0 0 36px 0;">{{ alertSTR }}</p>
          <p v-if="copyFreightFormData.goodsCount > CTN_20INCHI" style="color: #F56C6C;margin:0 0 36px 0;">注意：当前订单数量已超过20尺货柜最大容量，请选择更大尺寸的货柜！</p>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="callOfCopy">取 消</el-button>
        <el-button type="primary" size="mini" @click="postCopyMain">保 存</el-button>
      </div>
    </el-dialog>
    <!-- 新增包装 -->
    <el-dialog
      title="新增包装"
      :visible.sync="drawer"
      :before-close="handleClose">
      <el-form :model="pkFormData" class="form-wrap" :rules="rules" ref="ruleForm" size="mini">
        <el-form-item prop="pk_material" label="品名" label-width="100px">
          <el-input v-model="pkFormData.pk_material" placeholder="品名命名规则请尽量遵循：总称：颜色+材质+尺寸+特性(+厂商)" clearable>
          </el-input>
        </el-form-item>
        <el-form-item prop="pk_itemType" label="种类" label-width="100px">
          <el-select v-model="pkFormData.pk_itemType" placeholder="请选择种类">
            <el-option
              v-for="item in pk_itemType_options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <template v-if="pkFormData.pk_itemType === 'carton'">
          <el-form-item prop="pk_length" label="长" label-width="100px">
            <el-input-number v-model="pkFormData.pk_length" controls-position="right" placeholder="请输入（单位：英吋）" :min="0" :precision="2" clearable>
            </el-input-number>
          </el-form-item>
          <el-form-item prop="pk_width" label="宽" label-width="100px">
            <el-input-number v-model="pkFormData.pk_width" controls-position="right" placeholder="请输入（单位：英吋）单价" :min="0" :precision="2" clearable>
            </el-input-number>
          </el-form-item>
          <el-form-item prop="pk_height" label="高" label-width="100px">
            <el-input-number v-model="pkFormData.pk_height" controls-position="right" placeholder="请输入（单位：英吋）" :min="0" :precision="2" clearable>
            </el-input-number>
          </el-form-item>
        </template>
        <el-form-item prop="pk_supplier" label="厂商" label-width="100px">
          <el-input v-model="pkFormData.pk_supplier" placeholder="请输入厂商" clearable>
          </el-input>
        </el-form-item>
        <el-form-item prop="pk_price" label="单价" label-width="100px">
          <el-input-number v-model="pkFormData.pk_price" controls-position="right" placeholder="请输入单价" :min="0" :precision="5" clearable>
          </el-input-number>
        </el-form-item>
        <el-form-item prop="pk_unit" label="单位" label-width="100px">
          <el-select v-model="pkFormData.pk_unit" placeholder="注意成品用量变化，并通知主品配置人员" filterable allow-create default-first-optionclearable>
            <el-option
              v-for="item in unit_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="callOfdrawer">取 消</el-button>
        <el-button type="primary" size="mini" @click="handlePostForm">保 存</el-button>
      </div>
    </el-dialog>
	</div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import ajax from '../common/axiosHttp'
import { cookies, formatDate, findIntersectionByProperty } from '../common/utils'
import html2Canvas from 'html2canvas'
import JsPDF from 'jspdf'
import { ref } from 'vue'
const posterimg = ref()
// 绑定  需要把那个内容生成图片
// const poster = ref()
export default {
  name: 'Index',
  data () {
    return {
      LCFormData: {},
      ManualExchangeRate: {}, // 手动汇率
      FilterPackageArr: [],
      costa: 0,
      rm_selectData: 0,
      pk_selectData: 0,
      fr_boolean: false,
      isIncludePK: false,
      inCluedPKdata: [],
      dynamicSpan: 5,
      main_ids: [], // 主品id，最多4个
      standard_ids: [], // 标准id，最多4个
      selsection_ids: [], // 收集id，最多4个
      contrastData: [], // 显示对比数据
      rawContrastData: [], // 原始对比数据
      packageType: '',
      cuft: 0,
      CTN_20INCHI: 0,
      CTN_40INCHI: 0,
      CBM_ALL: 0,
      alertSTR: '',
      standardsDrawer: false,
      drawer: false,
      rm_btn: false,
      lc_btn: false,
      pk_btn: false,
      loading: false,
      stepIndex: 1,
      isDomesticSales: '',
      saleRate: 13,
      db_saleRate: 13,
      total_sum: 0,
      rp_total: 0,
      rm_sum: 0,
      lc_sum: 0,
      pk_sum: 0,
      fr_sum: 0,
      lc_percentage: 156,
      marketing: '',
      db_marketing: 20,
      tt_currencyType: '',
      db_tt_currencyType: 'USDP',
      successionList: [],
      typeList: [],
      totalRate: 1,
      db_totalRate: 6.9,
      tableHeight: 0,
      currentNum: 1,
      pageSize: 50,
      totalNum: 0,
      sizesList: ['20尺货柜', '40尺货柜'],
      power: cookies.getCookie('power'),
      user: cookies.getCookie('userName'),
      standardTableList: [],
      tableList: [],
      queryProductName: '',
      queryProductNo: '',
      productConnection: '',
      creator: '',
      handleMutiplelModal: false,
      handleAddModal: false,
      handleEditModal: false,
      handleDetailModal: false,
      copyObjData: {
        postForm: {},
        rawMaterialTableData: [],
        LaborCostTableData: [],
        packageTableData: [],
        freightTableData: [{}]
      },
      handleCopyModal: false,
      productFormData: {
        productStandard: '',
        productSuccession: '',
        productType: ''
      },
      copyProductFormData: {},
      pkFormData: {},
      itemDataForm: {
        out: [],
        mid: [],
        in: [],
        nasal: [],
        binding: [],
        foam: [],
        others: []
      },
      laborCostList: [],
      laborCostFilterList: [],
      packageFormData: [],
      package_options: [],
      freightFormData: {
        sales_type: '',
        factory: '',
        port: '',
        terms: '',
        boxSize: []
      },
      copyFreightFormData: {
        factory: '',
        port: '',
        terms: '',
        boxSize: []
      },
      copyPackageFormData: [],
      out_options: [],
      mid_options: [],
      in_options: [],
      nasal_options: [],
      binding_options: [],
      foam_options: [],
      others_options: [],
      price_options: [],
      editTitle: '',
      ttTableData: [],
      historyModal: false,
      activeNames: [
        'rm', 'lc', 'pk', 'fr', 'tt'
      ],
      detailObjData: {
        postForm: {},
        rawMaterialTableData: [],
        LaborCostTableData: [],
        packageTableData: [],
        freightTableData: [{}]
      },
      historyTableData: {
        rawMaterialTableData: [],
        LaborCostTableData: [],
        packageTableData: []
      },
      PKLCTableFormData: {
        lc_name: '包装统一工角'
      },
      copyPKLCTableFormData: {
        lc_name: '包装统一工角'
      },
      copyLCTableFormData: {},
      rules: {
        pk_number: [
          {
            required: true,
            message: '请填写品号',
            trigger: 'blur'
          }
        ],
        pk_material: [
          {
            required: true,
            message: '请填写品名',
            trigger: 'blur'
          }
        ],
        pk_itemType: [
          {
            required: true,
            message: '请选择种类',
            trigger: 'change'
          }
        ],
        pk_length: [
          {
            required: true,
            message: '请填写',
            trigger: 'blur'
          }
        ],
        pk_width: [
          {
            required: true,
            message: '请填写',
            trigger: 'blur'
          }
        ],
        pk_height: [
          {
            required: true,
            message: '请填写',
            trigger: 'blur'
          }
        ],
        pk_price: [
          {
            required: true,
            type: 'number',
            message: '请填写单价',
            trigger: 'blur'
          }
        ],
        pk_unit: [
          {
            required: true,
            message: '请填写单位',
            trigger: 'blur'
          }
        ],
        pk_currencyType: [
          {
            required: true,
            message: '请选择币种',
            trigger: 'change'
          }
        ]
      },
      cpLcRules: {
        lc_name: [
          {
            required: true,
            message: '请选择',
            trigger: 'change'
          }
        ],
        lc_factory: [
          {
            required: true,
            message: '请选择',
            trigger: 'change'
          }
        ],
        lc_price: [
          {
            required: true,
            type: 'number',
            message: '请填写单价',
            trigger: 'blur'
          }
        ]
      },
      pklcRules: {
        lc_price: [
          {
            required: true,
            type: 'number',
            message: '请填写单价',
            trigger: 'blur'
          }
        ],
        lc_consumption: [
          {
            required: true,
            type: 'number',
            message: '请填写用量',
            trigger: 'blur'
          }
        ],
        lc_name: [
          {
            required: true,
            message: '请填写名称',
            trigger: 'change'
          }
        ]
      },
      rmRules: {
        rm_exchangeRate: [
          {
            required: true,
            type: 'number',
            message: '请填写汇率',
            trigger: 'blur'
          }
        ],
        rm_price: [
          {
            required: true,
            type: 'number',
            message: '请填写单价',
            trigger: 'blur'
          }
        ],
        rm_consumption: [
          {
            required: true,
            type: 'number',
            message: '请填写用量',
            trigger: 'blur'
          }
        ],
        rm_loss: [
          {
            required: true,
            type: 'number',
            message: '请填写损耗',
            trigger: 'blur'
          }
        ],
        rm_material: [
          {
            required: true,
            message: '请填写名称',
            trigger: 'change'
          }
        ],
        // rm_number: [
        //   {
        //     required: true,
        //     message: '请填写物料品号',
        //     trigger: 'change'
        //   }
        // ],
        rm_unit: [
          {
            required: true,
            message: '请选择单位',
            trigger: 'change'
          }
        ],
        rm_currencyType: [
          {
            required: true,
            message: '请选择币种',
            trigger: 'change'
          }
        ]
      },
      lcFormRules: {
        lcData: [
          {
            required: true,
            message: '请选择工角',
            trigger: 'change'
          }
        ]
      },
      lcRules: {
        lc_unit: [
          {
            required: true,
            message: '请选择单位',
            trigger: 'change'
          }
        ],
        lc_exchangeRate: [
          {
            required: true,
            type: 'number',
            message: '请填写汇率',
            trigger: 'blur'
          }
        ],
        lc_price: [
          {
            required: true,
            type: 'number',
            message: '请填写单价',
            trigger: 'blur'
          }
        ],
        lc_consumption: [
          {
            required: true,
            type: 'number',
            message: '请填写用量',
            trigger: 'blur'
          }
        ],
        lc_name: [
          {
            required: true,
            message: '请填写名称',
            trigger: 'change'
          }
        ]
      },
      pkRules: {
        pk_exchangeRate: [
          {
            required: true,
            type: 'number',
            message: '请填写汇率',
            trigger: 'blur'
          }
        ],
        pk_price: [
          {
            required: true,
            type: 'number',
            message: '请填写单价',
            trigger: 'blur'
          }
        ],
        pk_consumption: [
          {
            required: true,
            type: 'number',
            message: '请填写用量',
            trigger: 'blur'
          }
        ],
        // pk_number: [
        //   {
        //     required: true,
        //     message: '请填写品号',
        //     trigger: 'change'
        //   }
        // ],
        pk_material: [
          {
            required: true,
            message: '请填写包装名称',
            trigger: 'change'
          }
        ],
        pk_unit: [
          {
            required: true,
            message: '请选择单位',
            trigger: 'change'
          }
        ],
        pk_currencyType: [
          {
            required: true,
            message: '请选择币种',
            trigger: 'change'
          }
        ]
      },
      pr_rules: {
        productName: [
          { required: true, message: '请填写产品名称', trigger: 'blur' }
        ],
        productConnection: [
          { required: true, message: '请填写产品客户信息', trigger: 'blur' }
        ],
        productFactory: [
          { required: true, message: '请选择生产加工工厂', trigger: 'blur' }
        ]
      },
      ma_rules: {
        productStandard: [
          { required: true, message: '请选择标准', trigger: 'change' }
        ],
        productSuccession: [
          { required: true, message: '请选择系列', trigger: 'change' }
        ],
        productType: [
          { required: true, message: '请选择型号', trigger: 'change' }
        ]
      },
      pk_rules: {
        pcsCount: [
          { required: true, type: 'number', message: '请填写', trigger: 'blur' }
        ],
        bagCount: [
          { required: true, message: '请填写', trigger: 'blur' }
        ],
        boxCount: [
          { required: true, message: '请填写', trigger: 'blur' }
        ]
      },
      fr_rules: {
        sales_type: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        goodsCount: [
          { required: true, type: 'number', message: '请选填写', trigger: 'blur' }
        ],
        factory: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        port: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        terms: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      },
      pkTreeData: [],
      filterText: '',
      defaultProps: {
        children: 'children',
        label: 'pk_material'
      }
    }
  },
  computed: {
    ...mapState(['FXRates', 'ct_options', 'CURRENCY_DIC', 'itemType_options', 'ITEM_DIC', 'standard_options', 'pk_itemType_options', 'unit_options'])
  },
  mounted () {
    // 挂载window.onresize事件(动态设置table高度)
    this.$nextTick(() => {
      this.tableHeight = window.innerHeight - 136
    })
    let _this = this
    window.onresize = () => {
      if (_this.resizeFlag) {
        clearTimeout(_this.resizeFlag)
      }
      _this.resizeFlag = setTimeout(() => {
        _this.getTableHeight()
        _this.resizeFlag = null
      }, 100)
    }
    // 异步获取最新汇率
    ajax({
      method: 'POST',
      url: '/api/selectMoney'
    }).then(res => {
      let obj = {}
      res.forEach(item => {
        obj[item.currency] = item.exchangerate
      })
      this.ManualExchangeRate = {...obj}
    })
  },
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val)
    }
  },
  activated () {
    // this.getAllList()
    this.seachAll()
    this.getLaborCostList()
  },
  methods: {
    ...mapActions(['getFXRates']),
    onTermsChange (val) {
      if (val === 'Ex Works') {
        this.freightFormData.expense = 0
      }
    },
    createFilter (queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0)
      }
    },
    // 内销汇率币种默认为人民币，外销默认美元
    handleDomesticSalesChange (val) {
      if (val) {
        this.tt_currencyType = 'CNY'
        this.totalRate = 1
      } else {
        this.tt_currencyType = 'USDP'
        this.totalRate = 6.9
      }
    },
    handleDBDomesticSalesChange (val) {
      if (val) {
        this.db_tt_currencyType = 'CNY'
        this.db_totalRate = 1
      } else {
        this.db_tt_currencyType = 'USDP'
        this.db_totalRate = 6.9
      }
      this.contrastCalculation()
    },
    // 包装类型赋值
    onTypeChange (v) {
      this.packageType = v
      this.packageFormData = []
      this.copyPackageFormData = []
      this.copyProductFormData.pcsCount = 1
      this.copyProductFormData.bagCount = 1
      this.copyProductFormData.boxCount = 1
      this.productFormData.pcsCount = 1
      this.productFormData.bagCount = 1
      this.productFormData.boxCount = 1
      this.$refs.tree.setCheckedKeys([])
    },
    // 主品列表多选
    handleCourseSelection (selection) {
      this.main_ids = selection.map(item => item.admin_id)
      this.selsection_ids = this.main_ids.concat(this.standard_ids)
      if (this.selsection_ids.length > 4) {
        this.$message.warning(`最多只能选择4条数据！`)
        let delRow = selection.pop()
        this.$refs.multipleTable.toggleRowSelection(delRow, false)
      }
      //  else {
      //   this.selsection_ids = selection.map(item => item.admin_id)
      // }
    },
    // 标准列表多选
    handleStandardsSelection (selection) {
      this.standard_ids = selection.map(item => item.admin_id)
      this.selsection_ids = this.standard_ids.concat(this.main_ids)
      if (this.selsection_ids.length > 4) {
        this.$message.warning(`最多只能选择4条数据！`)
        let delRow = selection.pop()
        this.$refs.multipleStandardsTable.toggleRowSelection(delRow, false)
      }
      //  else {
      //   this.selsection_ids = selection.map(item => item.admin_id)
      // }
    },
    // 对比数据处理
    handleMutiple () {
      if (this.selsection_ids.length <= 1) {
        this.$message.warning(`请至少选择2条数据，才能对比~`)
        return
      }
      // 对比列表自适应
      switch (this.selsection_ids.length) {
        case 2:
          this.dynamicSpan = 10
          break
        case 3:
          this.dynamicSpan = 7
          break
        case 4:
          this.dynamicSpan = 5
          break
      }
      ajax({
        method: 'POST',
        url: '/api/ByIdlist',
        data: {
          admin_id: this.selsection_ids
        }
      }).then(res => {
        if (!this.selsection_ids.length) return
        for (let i = 0; i < this.selsection_ids.length; i++) {
          this.rawContrastData.push(this.sortContrastData(res[i], i))
        }
        this.contrastData = JSON.parse(JSON.stringify(this.rawContrastData))
        this.contrastCalculation()
        this.handleMutiplelModal = true
      })
    },
    // 数据处理
    sortContrastData (attr, index) {
      let obj = {}
      obj.styleText = ''
      obj.productName = attr.postForm.productName + '(' + attr.postForm.productFactory.substring(0, 2) + '-' + attr.postForm.productFactory.substring(3, 5) + ')' // 品名
      // 包装信息
      obj.packageMsg = '每袋' + attr.postForm.pcsCount + '个，每盒' + attr.postForm.bagCount + '袋，每箱' + attr.postForm.boxCount + '盒'
      obj.fr_sum = attr.freightTableData[0].fr_price // 运费
      let lcTotal = 0
      attr.LaborCostTableData.forEach(item => {
        lcTotal += item.lc_amounts
      })
      obj.lc_sum = Number((lcTotal * 156 / 100).toFixed(5)) // 工角
      let rmTotal = 0
      attr.rawMaterialTableData.forEach(item => {
        rmTotal += item.rm_amounts
      })
      obj.rm_sum = Number(rmTotal.toFixed(5)) // 物料
      let pkTotal = 0
      attr.packageTableData.forEach(item => {
        pkTotal += item.pk_amounts
      })
      obj.pk_sum = Number(pkTotal.toFixed(5)) // 包装
      obj.cost = Number((obj.fr_sum + obj.lc_sum + obj.rm_sum + obj.pk_sum).toFixed(5)) // 成本
      obj.total = obj.cost // 合计
      switch (index) {
        case 0:
          obj.styleText = 'one'
          break
        case 1:
          obj.styleText = 'two'
          break
        case 2:
          obj.styleText = 'three'
          break
        case 3:
          obj.styleText = 'four'
          break
      }
      return obj
    },
    // 关闭修改对话框
    handleEditModalClose () {
      this.rm_btn = false
      this.pk_btn = false
      this.lc_btn = false
    },
    // 关闭对比框
    handleMutiplelModalClose () {
      this.handleMutiplelModal = false
      this.db_tt_currencyType = 'USDP'
      this.db_totalRate = 6.9
      this.db_marketing = 20
      this.db_saleRate = 13
      this.rawContrastData = []
      this.contrastData = []
      this.selsection_ids = []
      this.$refs.multipleTable.clearSelection()
      this.$refs.multipleStandardsTable.clearSelection()
    },
    // 币种切换
    handleDbCTchange (val) {
      this.db_tt_currencyType = val
      this.db_totalRate = this.ManualExchangeRate[val]
      this.contrastCalculation()
    },
    // 汇率变化
    handleDbRateChange (val) {
      this.db_totalRate = val
      this.contrastCalculation()
    },
    // 内销税率变化
    handleDBSaleRateChange (val) {
      this.db_saleRate = val
      this.contrastCalculation()
    },
    // 管销费变化
    handleDBMarketChange (val) {
      this.db_marketing = val
      this.contrastCalculation()
    },
    // 对比计算
    contrastCalculation () {
      this.contrastData.forEach((item, index) => {
        // 匹配内销字段
        let pattern = /内销/
        let exists = pattern.test(item.productName)
        // 各项价格汇率换算
        item.fr_sum = this.db_tt_currencyType === '100JPY' ? +(this.rawContrastData[index].fr_sum / this.db_totalRate * 100).toFixed(5) : +(this.rawContrastData[index].fr_sum / this.db_totalRate).toFixed(5)
        item.lc_sum = this.db_tt_currencyType === '100JPY' ? +(this.rawContrastData[index].lc_sum / this.db_totalRate * 100).toFixed(5) : +(this.rawContrastData[index].lc_sum / this.db_totalRate).toFixed(5)
        item.rm_sum = this.db_tt_currencyType === '100JPY' ? +(this.rawContrastData[index].rm_sum / this.db_totalRate * 100).toFixed(5) : +(this.rawContrastData[index].rm_sum / this.db_totalRate).toFixed(5)
        item.pk_sum = this.db_tt_currencyType === '100JPY' ? +(this.rawContrastData[index].pk_sum / this.db_totalRate * 100).toFixed(5) : +(this.rawContrastData[index].pk_sum / this.db_totalRate).toFixed(5)
        // 成本计算 (内外销分开计算)
        const RAW_TOTAL = exists ? +parseFloat((this.rawContrastData[index].cost / ((100 - this.db_marketing) / 100)) * (this.db_saleRate / 100 + 1)).toFixed(5) : +parseFloat((this.rawContrastData[index].cost / ((100 - this.db_marketing) / 100)) * 1.003).toFixed(5)
        item.cost = this.db_tt_currencyType === '100JPY' ? +parseFloat(this.rawContrastData[index].cost / this.db_totalRate * 100).toFixed(5) : +parseFloat(this.rawContrastData[index].cost / this.db_totalRate).toFixed(5)
        item.total = this.db_tt_currencyType === '100JPY' ? +parseFloat(RAW_TOTAL / this.db_totalRate * 100).toFixed(5) : +parseFloat(RAW_TOTAL / this.db_totalRate).toFixed(5)
      })
    },
    // 下一步
    handleNextStep () {
      if (this.stepIndex === 1) {
        this.$refs['productForm'].validate(valid => {
          if (valid) {
            this.stepIndex = 2
          }
        })
        return false
      }
      if (this.stepIndex === 2) {
        this.$refs['materialForm'].validate(valid => {
          if (valid) {
            if (!this.itemDataForm.out.length) {
              this.$confirm('当前没有选择"外层"用料，请问是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                this.stepIndex = 3
              }).catch(() => {})
            }
            if (!this.itemDataForm.mid.length) {
              this.$confirm('当前没有选择"中层"用料，请问是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                this.stepIndex = 3
              }).catch(() => {})
            }
            if (!this.itemDataForm.in.length) {
              this.$confirm('当前没有选择"内层"用料，请问是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                this.stepIndex = 3
              }).catch(() => {})
            }
            if (!this.itemDataForm.nasal.length) {
              this.$confirm('当前没有选择"鼻夹"用料，请问是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                this.stepIndex = 3
              }).catch(() => {})
            }
            if (!this.itemDataForm.binding.length) {
              this.$confirm('当前没有选择"松紧带"用料，请问是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                this.stepIndex = 3
              }).catch(() => {})
            }
            if (!this.itemDataForm.foam.length) {
              this.$confirm('当前没有选择"鼻垫"用料，请问是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                this.stepIndex = 3
              }).catch(() => {})
            }
            if (!this.itemDataForm.others.length) {
              this.$confirm('当前没有选择"其它"用料，请问是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                this.stepIndex = 3
              }).catch(() => {})
            }
            if (this.itemDataForm.out.length && this.itemDataForm.mid.length && this.itemDataForm.in.length && this.itemDataForm.nasal.length && this.itemDataForm.binding.length && this.itemDataForm.foam.length && this.itemDataForm.others.length) {
              this.stepIndex = 3
            }
          }
        })
        return false
      }
      if (this.stepIndex === 3) {
        const pack = new Promise((resolve, reject) => {
          this.$refs['packageForm'].validate(valid => {
            if (valid) {
              resolve()
            } else {
              reject(new Error('错误！'))
            }
          })
        })
        Promise.all([pack]).then(() => {
          // 包装用量非空判断
          let isPass = this.packageFormData.every(item => item.pk_consumption)
          if (isPass) {
            this.stepIndex = 4
          } else {
            this.$message.error('请填写用量！')
          }
          return false
        })
      }
      if (this.stepIndex === 4) {
        const lc = new Promise((resolve, reject) => {
          this.$refs['LCForm'].validate(valid => {
            if (valid) {
              resolve()
            } else {
              reject(new Error('错误！'))
            }
          })
        })
        const pklc = new Promise((resolve, reject) => {
          this.$refs['PKLCTableForm'].validate(valid => {
            if (valid) {
              resolve()
            } else {
              reject(new Error('错误！'))
            }
          })
        })
        Promise.all([lc, pklc]).then(() => {
          this.stepIndex = 5
          return false
        })
      }
    },
    // 获取包装数据
    getPackageList () {
      let form = new FormData()
      form.append('current', 1)
      form.append('size', 10000)
      form.append('pk_material', '')
      form.append('pk_number', '')
      ajax({
        method: 'POST',
        url: '/api/PackAll',
        data: form
      }).then(res => {
        let cloneData = JSON.parse(JSON.stringify(res.records))
        this.pkTreeData = [
          {
            packid: 'a',
            pk_material: '胶袋',
            children: cloneData.filter(item => {
              return item.pk_itemType === 'plastic'
            })
          },
          {
            packid: 'b',
            pk_material: '彩盒',
            children: cloneData.filter(item => {
              return item.pk_itemType === 'colorbox'
            })
          },
          {
            packid: 'c',
            pk_material: '彩/吊卡',
            children: cloneData.filter(item => {
              return item.pk_itemType === 'card'
            })
          },
          {
            packid: 'd',
            pk_material: '说明书',
            children: cloneData.filter(item => {
              return item.pk_itemType === 'instructions'
            })
          },
          {
            packid: 'e',
            pk_material: '合格证',
            children: cloneData.filter(item => {
              return item.pk_itemType === 'certificate'
            })
          },
          {
            packid: 'f',
            pk_material: '泡壳',
            children: cloneData.filter(item => {
              return item.pk_itemType === 'listershell'
            })
          },
          {
            packid: 'g',
            pk_material: '外箱',
            children: cloneData.filter(item => {
              return item.pk_itemType === 'carton'
            })
          },
          {
            packid: 'h',
            pk_material: '封箱胶带',
            children: cloneData.filter(item => {
              return item.pk_itemType === 'tape'
            })
          },
          {
            packid: 'i',
            pk_material: '内箱',
            children: cloneData.filter(item => {
              return item.pk_itemType === 'innerBox'
            })
          },
          {
            packid: 'j',
            pk_material: '纸盖/纸板',
            children: cloneData.filter(item => {
              return item.pk_itemType === 'paperboard'
            })
          },
          {
            packid: 'k',
            pk_material: '纸栈板',
            children: cloneData.filter(item => {
              return item.pk_itemType === 'paperpallet'
            })
          },
          {
            packid: 'l',
            pk_material: '卷料',
            children: cloneData.filter(item => {
              return item.pk_itemType === 'coil'
            })
          },
          {
            packid: 'm',
            pk_material: '白纸衬',
            children: cloneData.filter(item => {
              return item.pk_itemType === 'paperlining'
            })
          },
          {
            packid: 'n',
            pk_material: '绑封口',
            children: cloneData.filter(item => {
              return item.pk_itemType === 'seal'
            })
          },
          {
            packid: 'o',
            pk_material: '贴标',
            children: cloneData.filter(item => {
              return item.pk_itemType === 'labeling'
            })
          },
          {
            packid: 'p',
            pk_material: '其它',
            children: cloneData.filter(item => {
              return item.pk_itemType === 'others'
            })
          }
        ]
      }).catch((e) => {
        console.log(e)
      })
    },
    // 树形过滤
    filterNode (value, data) {
      if (!value) return true
      return data.pk_material.indexOf(value) !== -1
    },
    // 获取所有选中的节点id
    subRoleTreeCheck (keys, leafOnly) {
      // 获取所有选中的节点id
      this.packageFormData = leafOnly.checkedNodes.filter(node => {
        let COUNT = 1
        // 整箱数量
        if (this.packageType === '彩盒') {
          COUNT = this.productFormData.pcsCount * this.productFormData.boxCount * this.productFormData.bagCount
        } else {
          COUNT = this.productFormData.pcsCount * this.productFormData.boxCount
        }
        // 胶袋、泡壳、合格证、说明书、吊卡用量计算
        if (node.pk_itemType === 'plastic' || node.pk_itemType === 'listershell' || node.pk_itemType === 'certificate' || node.pk_itemType === 'instructions' || node.pk_itemType === 'card') {
          node.pk_consumption = this.productFormData.pcsCount
          node.pk_amounts = (node.pk_price / node.pk_consumption).toFixed(6)
        }
        // 彩盒用量计算
        if (node.pk_itemType === 'colorbox') {
          node.pk_consumption = this.productFormData.pcsCount * this.productFormData.bagCount
          node.pk_amounts = (node.pk_price / node.pk_consumption).toFixed(6)
        }
        // 外箱用量计算
        if (node.pk_itemType === 'carton') {
          node.pk_consumption = COUNT
          node.pk_amounts = (node.pk_price / node.pk_consumption).toFixed(6)
        }
        // 取外箱尺寸
        const TARGET = this.packageFormData.filter(item => {
          return item.pk_itemType === 'carton'
        })
        if (!TARGET.length) {
          return !!node.pk_price
        } else {
          if (node.pk_itemType === 'tape') {
            // 计算装箱胶带用量  {（长+6）*2+（宽+6）*4} /36/45/整箱个数
            node.pk_consumption = (((TARGET[0].pk_length + 6) * 2 + (TARGET[0].pk_width + 6) * 4) / 36 / 45 / COUNT).toFixed(6)
            node.pk_amounts = (node.pk_price * node.pk_consumption).toFixed(6)
          }
          return !!node.pk_price
        }
      })
    },
    // 获取所有选中的节点id
    copySubRoleTreeCheck (keys, leafOnly) {
      // 获取所有选中的节点id
      this.copyPackageFormData = leafOnly.checkedNodes.filter(node => {
        // 整箱数量
        let COUNT = 1
        // 整箱数量
        if (this.packageType === '彩盒') {
          COUNT = this.copyProductFormData.pcsCount * this.copyProductFormData.boxCount * this.copyProductFormData.bagCount
        } else {
          COUNT = this.copyProductFormData.pcsCount * this.copyProductFormData.boxCount
        }
        // 胶袋、泡壳、合格证、说明书、吊卡用量计算
        if (node.pk_itemType === 'plastic' || node.pk_itemType === 'listershell' || node.pk_itemType === 'certificate' || node.pk_itemType === 'instructions' || node.pk_itemType === 'card') {
          node.pk_consumption = this.copyProductFormData.pcsCount
          node.pk_amounts = (node.pk_price / node.pk_consumption).toFixed(6)
        }
        // 彩盒用量计算
        if (node.pk_itemType === 'colorbox') {
          node.pk_consumption = this.copyProductFormData.pcsCount * this.copyProductFormData.bagCount
          node.pk_amounts = (node.pk_price / node.pk_consumption).toFixed(6)
        }
        // 外箱用量计算
        if (node.pk_itemType === 'carton') {
          node.pk_consumption = COUNT
          node.pk_amounts = (node.pk_price / node.pk_consumption).toFixed(6)
        }
        // 取外箱尺寸
        const TARGET = this.copyPackageFormData.filter(item => {
          return item.pk_itemType === 'carton'
        })
        if (!TARGET.length) {
          return !!node.pk_price
        } else {
          if (node.pk_itemType === 'tape') {
            // 计算装箱胶带用量  {（长+6）*2+（宽+6）*4} /36/45/整箱个数
            node.pk_consumption = (((TARGET[0].pk_length + 6) * 2 + (TARGET[0].pk_width + 6) * 4) / 36 / 45 / COUNT).toFixed(6)
            node.pk_amounts = (node.pk_price * node.pk_consumption).toFixed(6)
          }
          return !!node.pk_price
        }
      })
    },
    // 获取系列列表
    getSuccessionList () {
      let form = new FormData()
      form.append('current', 1)
      form.append('size', 1000)
      form.append('sc_name', '')
      ajax({
        method: 'POST',
        url: '/api/selectSc',
        data: form
      }).then(res => {
        this.successionList = res.records
      })
    },
    // 选中材料后数据处理，添加rm_prop字段，区分种类
    handleBoxChange (val, prop) {
      val.forEach(item => {
        item.rm_prop = prop
      })
    },
    // 异步获取物料选项
    async getAsyncMaterialOptions (row) {
      const asyncFunctions = this.itemType_options.map(item => {
        return new Promise((resolve, reject) => {
          let form = new FormData()
          // form.append('rm_model', this.productFormData.productModel)
          // form.append('rm_standard', this.productFormData.productStandard)
          form.append('uuid', this.productFormData.productType)
          form.append('rm_itemType', item.label)
          ajax({
            method: 'POST',
            // url: '/api/SelectType_Model',
            url: '/api/selectRmType',
            data: form
          }).then(res => {
            this[item.value + '_options'] = res
            resolve()
          }).catch(() => {
            reject(new Error('请求失败, 请刷新重试'))
          })
        })
      })
      await Promise.all(asyncFunctions)
      this.optionsAssignments(row)
    },
    // 异步筛选物料
    filterMaterials () {
      this.itemDataForm = {
        out: [],
        mid: [],
        in: [],
        nasal: [],
        binding: [],
        foam: [],
        others: []
      }
      // 获取工角
      ajax({
        method: 'POST',
        url: '/api/selectLc',
        data: {
          uuid: this.productFormData.productType
        }
      }).then(res => {
        this.laborCostList = res
      })
      this.getAsyncMaterialOptions('', null)
    },
    // 计算table高度(动态设置table高度)
    getTableHeight () {
      let tableH = 180 // 距离页面下方的高度
      let tableHeightDetil = window.innerHeight - tableH
      if (tableHeightDetil <= 300) {
        this.tableHeight = 300
      } else {
        this.tableHeight = window.innerHeight - tableH
      }
    },
    // 表头筛选
    filterHeaders (value, row, colum) {
      const property = colum['property']
      return row[property] === value
    },
    // 查看全部
    seachAll () {
      this.creator = ''
      this.queryProductNo = ''
      this.queryProductName = ''
      this.productConnection = ''
      this.currentNum = 1
      this.pageSize = 50
      this.getAllList()
    },
    // 只看我的
    seachMine () {
      this.creator = cookies.getCookie('userName')
      this.queryProductNo = ''
      this.queryProductName = ''
      this.productConnection = ''
      this.currentNum = 1
      this.pageSize = 50
      this.getAllList()
    },
    // 搜索
    seach () {
      this.currentNum = 1
      this.pageSize = 50
      this.getAllList()
    },
    // 获取主品列表
    getAllList () {
      this.loading = true
      let form = new FormData()
      form.append('current', this.currentNum)
      form.append('size', this.pageSize)
      form.append('productName', this.queryProductName)
      form.append('number', this.queryProductNo)
      form.append('productConnection', this.productConnection)
      form.append('creator', this.creator)
      ajax({
        method: 'POST',
        url: '/api/PostAllTo',
        data: form
      }).then(res => {
        this.loading = false
        // 使用 filter 和 sort 方法来获取置顶和非置顶的数据，并合并它们以保持置顶数据的顺序。
        this.standardTableList = res.records.filter(item => item.isSticky === 'true')
        const normalItems = res.records.filter(item => !item.isSticky)
        // this.tableList = topItems.concat(normalItems) // 先置顶后普通，保持了置顶的顺序。
        this.tableList = normalItems
        this.totalNum = normalItems.length
      }).catch(() => {
        this.loading = false
      })
    },
    // 页码分页
    handleSizeChange (val) {
      this.pageSize = val
      this.getAllList()
    },
    // 分页
    handleCurrentChange (val) {
      this.currentNum = val
      this.getAllList()
    },
    // 标准选择
    selectStandard () {
      this.productFormData.productSuccession = 0
      this.productFormData.productType = 0
      this.typeList = []
      this.itemDataForm = {
        out: [],
        mid: [],
        in: [],
        nasal: [],
        binding: [],
        foam: [],
        others: []
      }
      this.out_options = []
      this.mid_options = []
      this.in_options = []
      this.nasal_options = []
      this.binding_options = []
      this.foam_options = []
      this.others_options = []
      let form = new FormData()
      form.append('sc_standard', this.productFormData.productStandard)
      form.append('sc_name', '')
      ajax({
        method: 'POST',
        url: '/api/SelectType_Model',
        data: form
      }).then(res => {
        const ARR = res.map(item => {
          return item.sc_name
        })
        this.successionList = Array.from(new Set(ARR))
      }).catch(e => {
        console.log(e)
      })
    },
    // 系列选择
    selectSuccession (val) {
      this.typeList = []
      this.itemDataForm = {
        out: [],
        mid: [],
        in: [],
        nasal: [],
        binding: [],
        foam: [],
        others: []
      }
      this.out_options = []
      this.mid_options = []
      this.in_options = []
      this.nasal_options = []
      this.binding_options = []
      this.foam_options = []
      this.others_options = []
      let form = new FormData()
      form.append('sc_standard', this.productFormData.productStandard)
      form.append('sc_name', val)
      ajax({
        method: 'POST',
        url: '/api/SelectType_Model',
        data: form
      }).then(res => {
        this.typeList = res
      }).catch(e => {
        console.log(e)
      })
    },
    // 型号选择
    selectType () {
      this.laborCostList = []
      this.filterMaterials()
    },
    // 新增主品
    createProductForm () {
      this.alertSTR = ''
      this.productFormData = {}
      this.packageFormData = []
      this.out_options = []
      this.mid_options = []
      this.in_options = []
      this.nasal_options = []
      this.binding_options = []
      this.foam_options = []
      this.others_options = []
      this.stepIndex = 1
      this.handleAddModal = true
      this.getPackageList()
    },
    // 复制提交
    postCopyMain () {
      const product = new Promise((resolve, reject) => {
        this.$refs['productForm'].validate(valid => {
          if (valid) {
            resolve()
          } else {
            reject(new Error('错误！'))
          }
        })
      })
      const freight = new Promise((resolve, reject) => {
        this.$refs['freightForm'].validate(valid => {
          if (valid) {
            resolve()
          } else {
            reject(new Error('错误！'))
          }
        })
      })
      const cplc = new Promise((resolve, reject) => {
        this.$refs['CPLCTableForm'].validate(valid => {
          if (valid) {
            resolve()
          } else {
            reject(new Error('错误！'))
          }
        })
      })
      Promise.all([product, freight, cplc]).then(() => {
      // Promise.all([product, freight]).then(() => {
        if (!cookies.getCookie('userName')) {
          this.$message.warning('登录超时，请重新登录！')
          return false
        }
        // 包装用量非空判断
        let isPass = this.copyPackageFormData.every(item => item.pk_consumption)
        if (!isPass) {
          this.$message.error('请填写用量！')
          return false
        }
        let itemObj = this.filterProps(this.copyObjData.rawMaterialTableData, 'rm_prop')
        this.copyFreightFormData.fr_price = 0
        const PCS_CONT = this.copyObjData.postForm.pcsCount
        const BAG_CONT = this.copyObjData.postForm.bagCount
        const BOX_CONT = this.copyObjData.postForm.boxCount
        // 有订单数量
        if (this.copyFreightFormData.goodsCount) {
          // Ex Works 不产生运费
          if (this.copyFreightFormData.terms === 'Ex Works') {
            this.copyFreightFormData.fr_price = 0
          } else {
            this.copyFreightFormData.fr_price = this.copyFreightFormData.expense / this.copyFreightFormData.goodsCount
          }
        // 没有订单数量
        } else {
          // Ex Works 不产生运费
          if (this.copyFreightFormData.terms === 'Ex Works') {
            this.copyFreightFormData.fr_price = 0
          } else if (this.copyFreightFormData.terms === 'FOB' && this.copyFreightFormData.shippingType === '散货') {
            const ONE_CTN_COUNT = PCS_CONT * BAG_CONT * BOX_CONT
            this.copyFreightFormData.fr_price = this.copyFreightFormData.expense / (ONE_CTN_COUNT * 2000)
          } else {
            this.copyFreightFormData.fr_price = this.copyFreightFormData.expense / this.CTN_20INCHI
          }
        }
        // 筛选出包装工角
        const FILTER_PK_COSTA = this.copyObjData.LaborCostTableData.filter(item => !item.lc_factory)
        const LC_COSTA = [{
          lc_amounts: this.copyLCTableFormData.lc_price,
          lc_consumption: 1,
          lc_exchangeRate: 1,
          lc_unit: '无',
          ...this.copyLCTableFormData,
          lc_factory: this.copyProductFormData.productFactory
        }]
        // 生成新的包装工角
        const PK_COSTA = [{
          lc_amounts: this.copyPKLCTableFormData.lc_price,
          lc_consumption: 1,
          lc_exchangeRate: 1,
          lc_name: this.copyPKLCTableFormData.lc_name,
          lc_unit: '无',
          lc_price: this.copyPKLCTableFormData.lc_price
        }]
        // 生成人工工角
        let LC_COSTA_DATA = []
        if (this.isIncludePK) {
          // 如果复制包装：筛选包装工角+人工工角
          LC_COSTA_DATA = FILTER_PK_COSTA.concat(LC_COSTA)
        } else {
          // 如果不复制包装：新的包装工角+人工工角
          LC_COSTA_DATA = PK_COSTA.concat(LC_COSTA)
        }
        // 主品描述备注销售类型
        this.copyProductFormData.productFactory = this.copyProductFormData.productFactory + '(' + this.copyFreightFormData.sales_type + ')'
        let postData = {
          uuid: '',
          materialData: [itemObj], // 物料配置数据
          laborCostData: LC_COSTA_DATA, // 工角配置数据
          packageFormData: this.isIncludePK ? this.inCluedPKdata : this.copyPackageFormData, // 包装配置数据
          freightFormData: {
            ...this.copyFreightFormData,
            factory: this.copyProductFormData.productFactory
          }, // 运输配置数据
          creator: cookies.getCookie('userName'),
          creationDate: formatDate(Date.now(), 'YYYY-MM-DD HH:mm:ss'),
          bagCount: BAG_CONT,
          boxCount: BOX_CONT,
          pcsCount: PCS_CONT,
          ...this.copyProductFormData
        }
        console.log('copyPostData', postData)
        ajax({
          method: 'POST',
          url: '/api/mainadd2',
          data: postData
        }).then(res => {
          this.$message({
            offset: 80,
            message: '保存成功！',
            type: 'success'
          })
          this.callOfCopy()
        })
      })
    },
    // copyCTN 计算
    handleCalculateCopyCTN (form, product, pack) {
      // 包装方式赋值
      this.inCluedPKdata.forEach(item => {
        switch (item.pk_itemType) {
          case 'colorbox':
            this.packageType = '彩盒'
            break
          case 'card':
            this.packageType = '吊卡'
            break
          case 'listershell':
            this.packageType = '泡壳'
            break
        }
      })
      // 判断是否复制包装数据：
      if (this.isIncludePK) {
        const PRODUCT = {
          pcsCount: this.copyObjData.postForm.pcsCount,
          bagCount: this.copyObjData.postForm.bagCount,
          boxCount: this.copyObjData.postForm.boxCount,
          ...product
        }
        // 组织数据后重新计算
        this.handleCalculateCTN(form, PRODUCT, this.inCluedPKdata)
      } else {
        this.handleCalculateCTN(form, product, pack)
      }
    },
    // CTN计算
    handleCalculateCTN (form, product, pack) {
      // 每箱个数
      let ONE_CTN_COUNT = 1
      if (this.packageType === '彩盒') {
        ONE_CTN_COUNT = product.pcsCount * product.boxCount * product.bagCount
      } else {
        ONE_CTN_COUNT = product.pcsCount * product.boxCount
      }
      // 此订单总箱数
      form.ctn = Math.ceil(form.goodsCount / ONE_CTN_COUNT)
      if (!pack.length) return false
      this.cuft = 0
      const TARGET = pack.filter(item => {
        return item.pk_itemType === 'carton'
      })
      if (!TARGET.length) {
        this.$message.error('未配置“外箱”包装，请在包装配置中增加！')
        return false
      }
      // 每箱的cuft
      this.cuft = ((TARGET[0].pk_length * 2.54 * TARGET[0].pk_width * 2.54 * TARGET[0].pk_height * 2.54) * 0.0000353).toFixed(6)
      // 20尺为950cuft, 可装个数
      this.CTN_20INCHI = Math.floor(950 / this.cuft * ONE_CTN_COUNT)
      // 40尺为2000cuft, 可装个数
      this.CTN_40INCHI = Math.floor(2000 / this.cuft * ONE_CTN_COUNT)
      // 一箱的体积CBM
      const CBM_ONE = (this.cuft / 35.315).toFixed(6)
      // 总体积CBM
      this.CBM_ALL = (CBM_ONE * form.ctn).toFixed(1)
      form.cmb = this.CBM_ALL
      this.alertSTR = '提示：散装2000个彩盒共有' + product.pcsCount * 2000 + '个口罩。'
      // this.alertSTR = '提示：单箱材积大约是: ' + this.cuft + 'cuft, 货品总体积大约是' + this.CBM_ALL + '立方米, 20尺货柜可装' + this.CTN_20INCHI + '个; 40尺货柜可装' + this.CTN_40INCHI + '个。散装2000个彩盒共有' + product.pcsCount * 2000 + '个口罩。'
    },
    // 报价单运费计算
    calculateFR () {
      this.detailObjData.freightTableData[0].fr_price = 0
      const PCS_CONT = this.detailObjData.postForm.pcsCount
      const BAG_CONT = this.detailObjData.postForm.bagCount
      const BOX_CONT = this.detailObjData.postForm.boxCount
      const ONE_CTN_COUNT = PCS_CONT * BAG_CONT * BOX_CONT
      // 有订单数量
      if (this.detailObjData.freightTableData[0].goodsCount) {
        this.detailObjData.freightTableData[0].fr_price = this.detailObjData.freightTableData[0].expense / this.detailObjData.freightTableData[0].goodsCount
      // 没有订单数量
      } else {
        if (this.detailObjData.freightTableData[0].terms === 'FOB' && this.detailObjData.freightTableData[0].shippingType === '散货') {
          this.detailObjData.freightTableData[0].fr_price = this.detailObjData.freightTableData[0].expense / (ONE_CTN_COUNT * 2000)
        } else {
          // 除数不能为0，所以不产生价格
          this.detailObjData.freightTableData[0].fr_price = 0
        }
      }
      this.getSummariesFR(this.detailObjData)
    },
    // 主品提交
    postAddMain (formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (!cookies.getCookie('userName')) {
            this.$message.warning('登录超时，请重新登录！')
            return
          }
          this.freightFormData.fr_price = 0
          const PCS_CONT = this.productFormData.pcsCount
          const BAG_CONT = this.productFormData.bagCount
          const BOX_CONT = this.productFormData.boxCount
          // 有订单数量
          if (this.freightFormData.goodsCount) {
            // Ex Works 不产生运费
            if (this.freightFormData.terms === 'Ex Works') {
              this.freightFormData.fr_price = 0
            } else {
              this.freightFormData.fr_price = this.freightFormData.expense / this.freightFormData.goodsCount
            }
          // 没有订单数量
          } else {
            // Ex Works 不产生运费
            if (this.freightFormData.terms === 'Ex Works') {
              this.freightFormData.fr_price = 0
            } else if (this.freightFormData.terms === 'FOB' && this.freightFormData.shippingType === '散货') {
              const ONE_CTN_COUNT = PCS_CONT * BAG_CONT * BOX_CONT
              this.freightFormData.fr_price = this.freightFormData.expense / (ONE_CTN_COUNT * 2000)
            } else {
              this.freightFormData.fr_price = this.freightFormData.expense / this.CTN_20INCHI
            }
          }
          // 区分内外销运输配置
          let FR_DATA = {...this.freightFormData}
          const POST_LC_DATA = [
            // 包装工角
            {
              lc_amounts: this.PKLCTableFormData.lc_price,
              lc_consumption: 1,
              lc_exchangeRate: 1,
              lc_name: this.PKLCTableFormData.lc_name,
              lc_unit: '无',
              lc_price: this.PKLCTableFormData.lc_price
            },
            // 人工工角
            {
              ...this.LCFormData.lcData,
              lc_amounts: this.LCFormData.lcData.lc_price
            }
          ]
          // 主品描述备注销售类型
          this.productFormData.productFactory = this.productFormData.productFactory + '(' + this.freightFormData.sales_type + ')'
          let postData = {
            uuid: '',
            isSticky: null,
            materialData: [this.itemDataForm], // 物料配置数据
            laborCostData: POST_LC_DATA, // 工角配置数据
            packageFormData: this.packageFormData, // 包装配置数据
            freightFormData: FR_DATA, // 运输配置数据
            creator: cookies.getCookie('userName'),
            creationDate: formatDate(Date.now(), 'YYYY-MM-DD HH:mm:ss'),
            ...this.productFormData // 主品数据
          }
          console.log('postData', postData)
          ajax({
            method: 'POST',
            url: '/api/mainadd2',
            data: postData
          }).then(res => {
            this.$message({
              offset: 80,
              message: '保存成功！',
              type: 'success'
            })
            this.callOf()
          })
        } else {
          return false
        }
      })
    },
    // 主品修改
    updateProductForm (row) {
      ajax({
        method: 'POST',
        url: '/api/getById',
        data: {
          admin_id: row.admin_id
        }
      }).then(res => {
        this.isDomesticSales = res.freightTableData[0].sales_type
        this.editTitle = row.productName
        this.detailObjData = res
        this.handleEditModal = true
      })
    },
    // 关闭主品修改对话框
    handleModifyClose () {
      this.handleEditModal = false
      this.getAllList()
    },
    // 主品复制
    copyProductForm (row) {
      this.alertSTR = ''
      ajax({
        method: 'POST',
        url: '/api/getById',
        data: {
          admin_id: row.admin_id
        }
      }).then(res => {
        this.copyObjData = res
        this.inCluedPKdata = res.packageTableData
        this.getPackageList()
        this.handleCopyModal = true
      })
    },
    // 复制人工工角远程搜索
    queryLCSearchAsync (queryString, cb) {
      let form = new FormData()
      form.append('current', 1)
      form.append('size', 9999)
      form.append('lc_name', queryString)
      ajax({
        method: 'POST',
        url: '/api/LaborCostAll',
        data: form
      }).then(res => {
        var restaurants = res.records.map((item) => {
          return {
            value: item.lc_name + ' (' + item.lc_factory + ')',
            ...item
          }
        })
        cb(restaurants)
      })
    },
    // 复制人工工角选择
    handleLCrSelect (val) {
      this.copyLCTableFormData.lc_name = val.lc_name
      this.copyLCTableFormData.lc_price = val.lc_price
    },
    // 币种选择
    handleCurrencyChange (row, attr, prop) {
      row[attr] = this.FXRates[row[prop]]
    },
    // 查询原材料
    queryRawMaterialList () {
      ajax({
        method: 'POST',
        url: '/api/selectRm',
        data: {
          uuid: this.detailObjData.postForm.uuid
        }
      }).then(res => {
        this.detailObjData.rawMaterialTableData = res
        this.rm_btn = false
      })
    },
    // 新增原材料
    addRawMaterialRow () {
      this.rm_btn = true
      this.detailObjData.rawMaterialTableData.unshift({
        showInput: true,
        uuid: this.detailObjData.postForm.uuid,
        rm_material: '',
        rm_price: 0,
        rm_unit: '',
        rm_currencyType: '',
        rm_amounts: '',
        rm_exchangeRate: '',
        rm_loss: 0.97,
        rm_consumption: '',
        rm_number: ''
      })
    },
    // 原料品号远程搜索
    queryRmNumberSearchAsync (queryString, cb) {
      let form = new FormData()
      form.append('current', 1)
      form.append('size', 9999)
      form.append('rm_material', '')
      form.append('rm_number', queryString)
      ajax({
        method: 'POST',
        url: '/api/GoodsAll',
        data: form
      }).then(res => {
        var restaurants = res.records.map((good) => {
          return {
            value: good.rm_number,
            ...good
          }
        })
        cb(restaurants)
      })
    },
    // 原料品号选择
    handleRmNumberSelect (item) {
      this.detailObjData.rawMaterialTableData[0].rm_material = item.rm_material
      this.detailObjData.rawMaterialTableData[0].rm_itemType = item.rm_itemType
      this.detailObjData.rawMaterialTableData[0].rm_price = item.rm_price
      this.detailObjData.rawMaterialTableData[0].rm_unit = item.rm_unit
      this.detailObjData.rawMaterialTableData[0].rm_currencyType = item.rm_currencyType
      this.detailObjData.rawMaterialTableData[0].rm_exchangeRate = item.rm_exchangeRate
    },
    // 原料名称远程搜索
    queryNameSearchAsync (queryString, cb) {
      let form = new FormData()
      form.append('current', 1)
      form.append('size', 9999)
      form.append('rm_material', queryString)
      form.append('rm_number', '')
      ajax({
        method: 'POST',
        url: '/api/GoodsAll',
        data: form
      }).then(res => {
        var restaurants = res.records.map((good) => {
          return {
            value: good.rm_material,
            ...good
          }
        })
        cb(restaurants)
      })
    },
    // 原料名称选择
    handleNameSelect (item) {
      this.detailObjData.rawMaterialTableData[0].rm_number = item.rm_number
      this.detailObjData.rawMaterialTableData[0].rm_itemType = item.rm_itemType
      this.detailObjData.rawMaterialTableData[0].rm_price = item.rm_price
      this.detailObjData.rawMaterialTableData[0].rm_unit = item.rm_unit
      this.detailObjData.rawMaterialTableData[0].rm_exchangeRate = item.rm_exchangeRate
      this.detailObjData.rawMaterialTableData[0].rm_currencyType = item.rm_currencyType
    },
    // 原材料金额计算  总价 = 单价 x 汇率 / 基本用量 / 0.97
    calculateRM (row) {
      if (row.rm_consumption === 0) {
        this.$message.error('用量不能为0！')
        return
      }
      if (!row.rm_price) return
      if (!row.rm_exchangeRate) return
      if (!row.rm_loss) return
      let AMOUNTS = 0
      if (!row.rm_consumption) {
        AMOUNTS = row.rm_price * row.rm_exchangeRate / row.rm_loss // 没有用量：单价 x 汇率 / 损耗
      } else if (row.rm_consumption > 0 && row.rm_consumption < 1) {
        AMOUNTS = row.rm_price * row.rm_exchangeRate * row.rm_consumption / row.rm_loss // 小用量：单价 x 汇率 x 单价 / 损耗
      } else if (row.rm_consumption >= 1) {
        AMOUNTS = row.rm_price * row.rm_exchangeRate / row.rm_consumption / row.rm_loss // 大用量： 单价 x 汇率 / 单价 / 损耗
      }
      row.rm_amounts = AMOUNTS.toFixed(5)
    },
    // 修改原材料
    updateRawMaterialRow (row) {
      row.showInput = true
      this.rm_btn = true
    },
    // 取消修改原材料
    cancelRawMaterialRow (row) {
      if (!row.rm_id) {
        this.detailObjData.rawMaterialTableData.splice(0, 1)
        this.rm_btn = false
      } else {
        this.queryRawMaterialList()
      }
    },
    // 提交原材料行
    saveRawMaterialRow (row) {
      if (row.rm_consumption === 0) {
        this.$message.error('用量不能为0！')
        return
      }
      this.calculateRM(row)
      this.$refs.rmTableForm.validate(valid => {
        if (valid) {
          if (!cookies.getCookie('userName')) {
            this.$message.warning('登录超时，请重新登录！')
            return
          }
          let postData = {
            updatePersonnel: cookies.getCookie('userName'),
            admin_id: this.detailObjData.postForm.admin_id,
            updateTime: formatDate(Date.now(), 'YYYY-MM-DD HH:mm:ss'),
            pcsCount: this.detailObjData.postForm.pcsCount,
            bagCount: this.detailObjData.postForm.bagCount,
            boxCount: this.detailObjData.postForm.boxCount
          }
          ajax({
            method: 'POST',
            url: '/api/updateadmin',
            data: postData
          }).then(res => {
            console.log(res)
          })
          ajax({
            method: 'POST',
            url: row.rm_id ? '/api/updaterm' : '/api/insertScRm',
            data: {
              ...row,
              ...postData
            }
          }).then(res => {
            this.$message({
              offset: 80,
              message: '保存成功！',
              type: 'success'
            })
            row.showInput = false
            this.queryRawMaterialList()
          })
        } else {
          return false
        }
      })
    },
    // 删除原材料
    deleteRawMaterialRow (row) {
      this.$refs.rmTableForm.clearValidate()
      if (row.showInput) {
        this.detailObjData.rawMaterialTableData.splice(0, 1)
        this.rm_btn = false
      } else {
        this.$confirm(`是否删除”${row.rm_material}“信息?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 刷新更新时间
          let postData = {
            updatePersonnel: cookies.getCookie('userName'),
            admin_id: this.detailObjData.postForm.admin_id,
            updateTime: formatDate(Date.now(), 'YYYY-MM-DD HH:mm:ss'),
            pcsCount: this.detailObjData.postForm.pcsCount,
            bagCount: this.detailObjData.postForm.bagCount,
            boxCount: this.detailObjData.postForm.boxCount
          }
          ajax({
            method: 'POST',
            url: '/api/updateadmin',
            data: postData
          }).then(res => {
            console.log(res)
          })
          ajax({
            method: 'POST',
            url: '/api/deleterm',
            data: {
              rm_id: row.rm_id
            }
          }).then(res => {
            this.$message({
              offset: 80,
              message: '删除成功！',
              type: 'success'
            })
            row.showInput = false
            this.queryRawMaterialList()
          })
        }).catch(() => {
          this.$message({
            offset: 80,
            type: 'info',
            message: '已取消删除！'
          })
        })
      }
    },
    // 查询工角
    queryLaborCostList () {
      ajax({
        method: 'POST',
        url: '/api/selectLc',
        data: {
          uuid: this.detailObjData.postForm.uuid
        }
      }).then(res => {
        this.detailObjData.LaborCostTableData = res
        this.lc_btn = false
      })
    },
    // 获取工角列表
    getLaborCostList () {
      let form = new FormData()
      form.append('current', 1)
      form.append('size', 9999)
      form.append('lc_name', '')
      ajax({
        method: 'POST',
        url: '/api/LaborCostAll',
        data: form
      }).then(res => {
        this.laborCostFilterList = res.records
      })
    },
    // 工角名称远程搜索
    queryLCNameSearchAsync (queryString, cb) {
      var restaurants = this.laborCostFilterList.map((item) => {
        return {
          value: item.lc_name + ' (' + item.lc_factory + ')',
          ...item
        }
      })
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    // 工角名称选择
    handleLCNameSelect (item) {
      this.detailObjData.LaborCostTableData[0].lc_name = item.lc_name + '人工'
      this.detailObjData.LaborCostTableData[0].lc_price = item.lc_price
      this.detailObjData.LaborCostTableData[0].lc_unit = item.lc_unit
      this.detailObjData.LaborCostTableData[0].lc_factory = item.lc_factory
    },
    // 新增工角
    addLaborRow () {
      this.lc_btn = true
      this.detailObjData.LaborCostTableData.unshift({
        showInput: true,
        uuid: this.detailObjData.postForm.uuid,
        lc_name: '',
        lc_price: 0,
        lc_unit: '',
        // lc_amounts: '',
        lc_exchangeRate: 1,
        lc_consumption: ''
      })
    },
    // 修改工角
    updateLaborRow (row) {
      row.showInput = true
      this.lc_btn = true
    },
    // 工角金额计算  总价 = 单价 x 汇率 / 基本用量
    calculateLC (row) {
      if (row.lc_consumption === 0) {
        this.$message.error('用量不能为0！')
        return
      }
      if (!row.lc_price) return
      if (!row.lc_exchangeRate) return
      if (!row.lc_consumption) return
      let AMOUNTS = 0
      if (!row.lc_consumption) {
        AMOUNTS = row.lc_price * row.lc_exchangeRate
      } else if (row.lc_consumption > 0 && row.lc_consumption < 1) {
        AMOUNTS = row.lc_price * row.lc_exchangeRate * row.lc_consumption
      } else if (row.lc_consumption >= 1) {
        AMOUNTS = row.lc_price * row.lc_exchangeRate / row.lc_consumption
      }
      row.lc_amounts = AMOUNTS.toFixed(5)
    },
    // 取消修改工角
    cancelLaborRow (row) {
      if (!row.lc_id) {
        this.detailObjData.LaborCostTableData.splice(0, 1)
        this.lc_btn = false
      } else {
        this.queryLaborCostList()
      }
    },
    // 删除工角
    deleteLaborRow (row) {
      this.$refs.lcTableForm.clearValidate()
      if (row.showInput) {
        this.detailObjData.LaborCostTableData.splice(0, 1)
        this.lc_btn = false
      } else {
        this.$confirm(`是否删除”${row.lc_name}“信息?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 刷新更新时间
          let postData = {
            updatePersonnel: cookies.getCookie('userName'),
            admin_id: this.detailObjData.postForm.admin_id,
            updateTime: formatDate(Date.now(), 'YYYY-MM-DD HH:mm:ss'),
            pcsCount: this.detailObjData.postForm.pcsCount,
            bagCount: this.detailObjData.postForm.bagCount,
            boxCount: this.detailObjData.postForm.boxCount
          }
          ajax({
            method: 'POST',
            url: '/api/updateadmin',
            data: postData
          }).then(res => {
            console.log(res)
          })
          ajax({
            method: 'POST',
            url: '/api/deletelc',
            data: {
              lc_id: row.lc_id
            }
          }).then(res => {
            this.$message({
              offset: 80,
              message: '删除成功！',
              type: 'success'
            })
            row.showInput = false
            this.queryLaborCostList()
          })
        }).catch(() => {
          this.$message({
            offset: 80,
            type: 'info',
            message: '已取消删除！'
          })
        })
      }
    },
    // 提交工角
    saveLaborCostRow (row) {
      if (row.lc_consumption === 0) {
        this.$message.error('用量不能为0！')
        return
      }
      this.calculateLC(row)
      this.$refs.lcTableForm.validate(valid => {
        if (valid) {
          if (!cookies.getCookie('userName')) {
            this.$message.warning('登录超时，请重新登录！')
            return
          }
          let postData = {
            updatePersonnel: cookies.getCookie('userName'),
            admin_id: this.detailObjData.postForm.admin_id,
            updateTime: formatDate(Date.now(), 'YYYY-MM-DD HH:mm:ss'),
            pcsCount: this.detailObjData.postForm.pcsCount,
            bagCount: this.detailObjData.postForm.bagCount,
            boxCount: this.detailObjData.postForm.boxCount
          }
          ajax({
            method: 'POST',
            url: '/api/updateadmin',
            data: postData
          }).then(res => {
            console.log(res)
          })
          row.lc_currencyType = 'CNY'
          row.lc_exchangeRate = 1
          ajax({
            method: 'POST',
            url: row.lc_id ? '/api/updatelc' : '/api/lcadd',
            data: {
              ...row,
              ...postData
            }
          }).then(res => {
            this.$message({
              offset: 80,
              message: '保存成功！',
              type: 'success'
            })
            row.showInput = false
            this.queryLaborCostList()
          })
        } else {
          return false
        }
      })
    },
    handleClose (done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => {})
    },
    // 包装名称远程搜索
    queryPkNameSearchAsync (queryString, cb) {
      let form = new FormData()
      form.append('current', 1)
      form.append('size', 9999)
      form.append('pk_material', queryString)
      form.append('pk_number', '')
      ajax({
        method: 'POST',
        url: '/api/PackAll',
        data: form
      }).then(res => {
        var restaurants = res.records.map((good) => {
          return {
            value: good.pk_material,
            ...good
          }
        })
        cb(restaurants)
      })
    },
    // 包装品号远程搜索
    queryPkNumberSearchAsync (queryString, cb) {
      let form = new FormData()
      form.append('current', 1)
      form.append('size', 9999)
      form.append('pk_material', '')
      form.append('pk_number', queryString)
      ajax({
        method: 'POST',
        url: '/api/PackAll',
        data: form
      }).then(res => {
        var restaurants = res.records.map((good) => {
          return {
            value: good.pk_number,
            ...good
          }
        })
        cb(restaurants)
      })
    },
    // 包装品号选择
    handlePkNumberSelect (item) {
      this.detailObjData.packageTableData[0].pk_material = item.pk_material
      this.detailObjData.packageTableData[0].pk_price = item.pk_price
      this.detailObjData.packageTableData[0].pk_unit = item.pk_unit
      this.detailObjData.packageTableData[0].pk_currencyType = item.pk_currencyType
      this.detailObjData.packageTableData[0].pk_exchangeRate = item.pk_exchangeRate
    },
    // 包装名称选择
    handlePkNameSelect (item) {
      this.detailObjData.packageTableData[0].pk_number = item.pk_number
      this.detailObjData.packageTableData[0].pk_price = item.pk_price
      this.detailObjData.packageTableData[0].pk_unit = item.pk_unit
      this.detailObjData.packageTableData[0].pk_currencyType = item.pk_currencyType
      this.detailObjData.packageTableData[0].pk_exchangeRate = item.pk_exchangeRate
    },
    // 新增包装
    addPackageRow () {
      this.pk_btn = true
      this.detailObjData.packageTableData.unshift({
        showInput: true,
        uuid: this.detailObjData.postForm.uuid,
        pk_material: '',
        pk_price: 0,
        pk_unit: '',
        pk_currencyType: 'CNY',
        pk_amounts: '',
        pk_exchangeRate: 1,
        pk_consumption: '',
        pk_number: ''
      })
    },
    // 修改包装
    updatePackageRow (row) {
      row.showInput = true
      this.pk_btn = true
    },
    // 取消修改包装
    cancelPackageRow (row) {
      if (!row.pk_id) {
        this.detailObjData.packageTableData.splice(0, 1)
        this.pk_btn = false
      } else {
        this.queryPackageList()
      }
    },
    // 提交包装
    savePackageRow (row) {
      if (row.pk_consumption === 0) {
        this.$message.error('用量不能为0！')
        return
      }
      this.calculatePackage(row)
      this.$refs.pkTableForm.validate(valid => {
        if (valid) {
          if (!cookies.getCookie('userName')) {
            this.$message.warning('登录超时，请重新登录！')
            return
          }
          let postData = {
            updatePersonnel: cookies.getCookie('userName'),
            admin_id: this.detailObjData.postForm.admin_id,
            updateTime: formatDate(Date.now(), 'YYYY-MM-DD HH:mm:ss'),
            pcsCount: this.detailObjData.postForm.pcsCount,
            bagCount: this.detailObjData.postForm.bagCount,
            boxCount: this.detailObjData.postForm.boxCount
          }
          ajax({
            method: 'POST',
            url: '/api/updateadmin',
            data: postData
          }).then(res => {
            console.log(res)
          })
          ajax({
            method: 'POST',
            url: row.pk_id ? '/api/updatepk' : '/api/pkadd',
            data: {
              ...row,
              ...postData
            }
          }).then(res => {
            this.$message({
              offset: 80,
              message: '保存成功！',
              type: 'success'
            })
            row.showInput = false
            this.queryPackageList()
          })
        } else {
          return false
        }
      })
    },
    // 删除包装
    deletePackageRow (row) {
      this.$refs.pkTableForm.clearValidate()
      if (row.showInput) {
        this.detailObjData.packageTableData.splice(0, 1)
        this.pk_btn = false
      } else {
        this.$confirm(`是否删除”${row.pk_material}“信息?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 刷新更新时间
          let postData = {
            updatePersonnel: cookies.getCookie('userName'),
            admin_id: this.detailObjData.postForm.admin_id,
            updateTime: formatDate(Date.now(), 'YYYY-MM-DD HH:mm:ss'),
            pcsCount: this.detailObjData.postForm.pcsCount,
            bagCount: this.detailObjData.postForm.bagCount,
            boxCount: this.detailObjData.postForm.boxCount
          }
          ajax({
            method: 'POST',
            url: '/api/updateadmin',
            data: postData
          }).then(res => {
            console.log(res)
          })
          ajax({
            method: 'POST',
            url: '/api/deletepk',
            data: {
              pk_id: row.pk_id
            }
          }).then(res => {
            this.$message({
              offset: 80,
              message: '删除成功！',
              type: 'success'
            })
            row.showInput = false
            this.queryPackageList()
          })
        }).catch(() => {
          this.$message({
            offset: 80,
            type: 'info',
            message: '已取消删除！'
          })
        })
      }
    },
    // 查询包装列表
    queryPackageList () {
      ajax({
        method: 'POST',
        url: '/api/selectPk',
        data: {
          uuid: this.detailObjData.postForm.uuid
        }
      }).then(res => {
        this.detailObjData.packageTableData = res
        this.pk_btn = false
      })
    },
    // 包装金额计算  总价 = 单价 x 汇率 / 基本用量
    calculatePackage (row) {
      if (row.pk_consumption === 0) {
        this.$message.error('用量不能为0！')
        return
      }
      if (!row.pk_price) return
      if (!row.pk_consumption) return
      if (!row.pk_exchangeRate) {
        row.pk_exchangeRate = 1
      }
      let AMOUNTS = 0
      if (!row.pk_consumption) {
        AMOUNTS = row.pk_price * row.pk_exchangeRate
      } else if (row.pk_consumption > 0 && row.pk_consumption < 1) {
        AMOUNTS = row.pk_price * row.pk_exchangeRate * row.pk_consumption
      } else if (row.pk_consumption >= 1) {
        AMOUNTS = row.pk_price * row.pk_exchangeRate / row.pk_consumption
      }
      row.pk_amounts = AMOUNTS.toFixed(5)
      // 强制更新DOM
      this.$forceUpdate()
    },
    // 修改选项赋值
    async optionsAssignments (row) {
      if (row) {
        ajax({
          method: 'POST',
          url: '/api/getById',
          data: {
            admin_id: row.admin_id
          }
        }).then(res => {
          this.editTitle = row.productName
          this.itemDataForm = this.filterProps(res.rawMaterialTableData, 'rm_prop')
          this.filterChecked('out', 'rm_material')
          this.filterChecked('mid', 'rm_material')
          this.filterChecked('in', 'rm_material')
          this.filterChecked('nasal', 'rm_material')
          this.filterChecked('binding', 'rm_material')
          this.filterChecked('foam', 'rm_material')
          this.filterChecked('others', 'rm_material')
        })
      }
    },
    // 筛选材料数据
    filterProps (arr, prop) {
      let obj = {
        out: [],
        mid: [],
        in: [],
        nasal: [],
        binding: [],
        foam: [],
        others: []
      }
      arr.forEach(item => {
        if (item[prop] === 'out') {
          obj.out.push(item)
        }
        if (item[prop] === 'in') {
          obj.in.push(item)
        }
        if (item[prop] === 'mid') {
          obj.mid.push(item)
        }
        if (item[prop] === 'nasal') {
          obj.nasal.push(item)
        }
        if (item[prop] === 'foam') {
          obj.foam.push(item)
        }
        if (item[prop] === 'binding') {
          obj.binding.push(item)
        }
        if (item[prop] === 'others') {
          obj.others.push(item)
        }
      })
      return obj
    },
    // 给复选框对象赋值
    filterChecked (type, prop) {
      this.itemDataForm[type].map((item) => {
        const idx = this[type + '_options'].findIndex(sla => sla[prop] === item[prop])
        this[type + '_options'][idx] = item
      })
    },
    // 主品报价单
    async handleDetail (row) {
      this.ttTableData = []
      this.lc_percentage = 156
      this.marketing = 20
      // 获取包装信息
      let form = new FormData()
      form.append('current', 1)
      form.append('size', 99999)
      form.append('pk_material', '')
      form.append('pk_number', '')
      ajax({
        method: 'POST',
        url: '/api/PackAll',
        data: form
      }).then(res => {
        this.changePackageList = res.records
      }).catch((e) => {
        console.log(e)
      })
      ajax({
        method: 'POST',
        url: '/api/getById',
        data: {
          admin_id: row.admin_id
        }
      }).then(res => {
        this.editTitle = row.productName
        this.isDomesticSales = res.freightTableData[0].sales_type
        this.saleRate = res.freightTableData[0].tax_domestic ? res.freightTableData[0].tax_domestic : 13
        this.marketing = res.freightTableData[0].sales ? res.freightTableData[0].sales : 20
        this.tt_currencyType = res.freightTableData[0].currency ? res.freightTableData[0].currency : this.isDomesticSales === '内销' ? 'CNY' : 'USDP'
        this.totalRate = res.freightTableData[0].exchange_rate ? res.freightTableData[0].exchange_rate : this.isDomesticSales === '内销' ? 1 : 6.9
        this.lc_percentage = res.freightTableData[0].lc_percentage ? res.freightTableData[0].lc_percentage : 156
        this.getSummariesFR(res)
        this.getFXRates()
        this.detailObjData = res
        // 找出变更过单价的包装，用于提醒
        this.FilterPackageArr = findIntersectionByProperty(this.changePackageList, res.packageTableData, 'pk_number', 'pk_price')
        this.handleDetailModal = true
      })
    },
    // 修改运费价格
    updateFreightPrice () {
      // 刷新更新时间
      let postData = {
        updatePersonnel: cookies.getCookie('userName'),
        admin_id: this.detailObjData.postForm.admin_id,
        updateTime: formatDate(Date.now(), 'YYYY-MM-DD HH:mm:ss'),
        pcsCount: this.detailObjData.postForm.pcsCount,
        bagCount: this.detailObjData.postForm.bagCount,
        boxCount: this.detailObjData.postForm.boxCount
      }
      ajax({
        method: 'POST',
        url: '/api/updateadmin',
        data: postData
      }).then(res => {
        console.log(res)
      })
      ajax({
        method: 'POST',
        url: '/api/updateadminFr',
        data: {
          uuid: this.detailObjData.postForm.uuid,
          expense: this.detailObjData.freightTableData[0].expense,
          fr_price: this.detailObjData.freightTableData[0].fr_price
        }
      }).then(res => {
        console.log(res)
      })
    },
    // 关闭保存报价单数据
    handleDetailModalClose () {
      this.fr_boolean = false
      ajax({
        method: 'POST',
        url: '/api/updateadminFr',
        data: {
          uuid: this.detailObjData.postForm.uuid,
          sales_type: this.isDomesticSales,
          tax_domestic: this.saleRate,
          sales: this.marketing,
          currency: this.tt_currencyType,
          exchange_rate: this.totalRate,
          lc_percentage: this.lc_percentage,
          expense: this.detailObjData.freightTableData[0].expense,
          fr_price: this.detailObjData.freightTableData[0].fr_price
        }
      }).then(res => {
        console.log(res)
        this.handleDetailModal = false
        this.getAllList()
      }).catch(e => {
        console.log(e)
      })
    },
    // 修改记录
    viewhHistory (row) {
      ajax({
        method: 'POST',
        url: '/api/selectRc',
        data: {
          uuid: row.uuid
        }
      }).then(res => {
        this.editTitle = row.productName
        this.historyTableData = res.data
        this.historyModal = true
      }).catch(e => {
        console.log(e)
      })
    },
    // 主品删除
    handleDelete (row) {
      this.$confirm(`此操作将永久删除”${row.productName}“产品信息, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ajax({
          method: 'POST',
          url: '/api/deleteadmin',
          data: {
            uuid: row.uuid
          }
        }).then(res => {
          this.$message({
            offset: 80,
            type: 'success',
            message: '操作成功！'
          })
          this.getAllList()
        })
      }).catch(() => {
        this.$message({
          offset: 80,
          type: 'info',
          message: '已取消删除！'
        })
      })
    },
    // 关闭主品对话框确认
    handleMainClose () {
      this.$refs['productForm'].resetFields()
      this.$refs['materialForm'].resetFields()
      this.$refs['packageForm'].resetFields()
      this.$refs['freightForm'].resetFields()
    },
    // 关闭主品模态框
    callOf () {
      this.cuft = 0
      this.CBM_ALL = 0
      this.CTN_20INCHI = 0
      this.CTN_40INCHI = 0
      this.$refs['productForm'].resetFields()
      this.$refs['materialForm'].resetFields()
      // this.$refs['packageForm'].resetFields()
      this.$refs['freightForm'].resetFields()
      this.$refs['itemDataForm'].resetFields()
      this.$refs['PKLCTableForm'].resetFields()
      this.$refs['LCForm'].resetFields()
      this.PKLCTableFormData = {
        lc_name: '包装统一工角报价'
      }
      this.productFormData = {}
      this.successionList = []
      this.typeList = []
      this.laborCostList = []
      this.packageFormData = []
      this.handleAddModal = false
      this.getAllList()
    },
    callOfdrawer () {
      this.drawer = false
      this.pkFormData = {}
      this.$refs['ruleForm'].resetFields()
      this.getPackageList()
    },
    handlePostForm () {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          this.pkFormData.pk_currencyType = 'CNY'
          this.pkFormData.pk_exchangeRate = 1
          ajax({
            method: 'POST',
            url: '/api/packadd',
            data: this.pkFormData
          }).then(res => {
            this.$message({
              offset: 80,
              message: '保存成功！',
              type: 'success'
            })
            this.callOfdrawer()
          })
        } else {
          return false
        }
      })
    },
    // 取消复制
    callOfCopy () {
      this.cuft = 0
      this.CBM_ALL = 0
      this.CTN_20INCHI = 0
      this.CTN_40INCHI = 0
      this.alertSTR = ''
      this.packageType = ''
      this.handleCopyModal = false
      this.isIncludePK = false
      this.inCluedPKdata = []
      this.$refs['productForm'].resetFields()
      this.$refs['freightForm'].resetFields()
      this.$refs['PKLCTableForm'].resetFields()
      this.$refs['CPLCTableForm'].resetFields()
      this.copyProductFormData = {}
      this.copyObjData = {
        postForm: {},
        rawMaterialTableData: [],
        LaborCostTableData: [],
        packageTableData: [],
        freightTableData: [{}]
      }
      this.copyPKLCTableFormData = {
        lc_name: '包装统一工角报价'
      }
      this.copyPackageFormData = []
      this.copyFreightFormData = {
        factory: '',
        port: '',
        terms: '',
        boxSize: []
      }
      this.getAllList()
    },
    // 关闭详情对话框
    handleDetailClose (done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
          this.getAllList()
        })
        .catch(_ => {})
    },
    // 币种汇率计算
    handleCTchange (val) {
      this.tt_currencyType = val
      this.totalRate = this.ManualExchangeRate[val]
      this.fr_boolean = false
    },
    // 原料表勾选
    handleRMStatusChange (selection) {
      this.rm_selectData = selection.reduce((sum, obj) => (sum += obj.rm_amounts), 0)
    },
    // 包装表勾选
    handlePKStatusChange (selection) {
      this.pk_selectData = selection.reduce((sum, obj) => (sum += obj.pk_amounts), 0)
    },
    // 原材料小计
    getSummariesRM (param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === columns.length - 3) {
          sums[index] = '合计'
          return
        }
        if (index === columns.length - 2) {
          const values = data.map(item => Number(item[column.property]))
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0)
            sums[index] = sums[index].toFixed(5)
          } else {
            sums[index] = ''
          }
        }
      })
      if (!sums[sums.length - 1]) {
        this.rm_sum = 0
      } else {
        this.rm_sum = sums[sums.length - 1]
      }
      this.totalMethod()
      return sums
    },
    // 人工小计
    getSummariesLC (param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === columns.length - 2) {
          sums[index] = '合计'
          return
        }
        if (index === columns.length - 1) {
          const values = data.map(item => Number(item[column.property]))
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0)
            sums[index] = (sums[index] * this.lc_percentage / 100).toFixed(5) // 合计加上损耗
          } else {
            sums[index] = ''
          }
        }
      })
      if (!sums[sums.length - 1]) {
        this.lc_sum = 0
      } else {
        this.lc_sum = sums[sums.length - 1]
      }
      this.totalMethod()
      return sums
    },
    // 包装费小计
    getSummariesPK (param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === columns.length - 3) {
          sums[index] = '合计'
          return
        }
        if (index === columns.length - 2) {
          const values = data.map(item => Number(item[column.property]))
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0)
            sums[index] = sums[index].toFixed(5)
          } else {
            sums[index] = ''
          }
        }
      })
      if (!sums[sums.length - 1]) {
        this.pk_sum = 0
      } else {
        this.pk_sum = sums[sums.length - 1]
      }
      this.totalMethod()
      return sums
    },
    // 运输合计
    getSummariesFR (data) {
      this.fr_sum = 0
      this.fr_sum = (data.freightTableData[0].fr_price).toFixed(5)
    },
    // 手KEY成本计算
    handleRateChange (row) {
      const MARKETING = this.isDomesticSales === '内销' ? +parseFloat(this.total_sum / ((100 - this.marketing) / 100) * (this.saleRate / 100 + 1)).toFixed(5) : +parseFloat(this.total_sum / ((100 - this.marketing) / 100) * 1.003).toFixed(5)
      row.row = +parseFloat(MARKETING + MARKETING * Number(row.column) / 100).toFixed(5)
    },
    // 合计
    totalMethod () {
      let MARKETING = 0
      let TOTAL_SUM = 0
      let RP_SUM = 0
      // 成本 =  (原料成本 + 人工成本 + 包装成本 + 运输成本 - 剔除成本) / 汇率
      // 判断是否为100日元，倍数不同，区分计算
      if (this.tt_currencyType === '100JPY') {
        this.total_sum = +((parseFloat(this.rm_sum) + parseFloat(this.lc_sum) + parseFloat(this.pk_sum) + parseFloat(this.fr_sum)) / this.totalRate * 100).toFixed(5)
      } else {
        this.total_sum = +((parseFloat(this.rm_sum) + parseFloat(this.lc_sum) + parseFloat(this.pk_sum) + parseFloat(this.fr_sum)) / this.totalRate).toFixed(5)
      }
      // console.log('初始合计——total_sum', this.total_sum)
      if (!this.rm_selectData && !this.pk_selectData) { // 正常合计，无剔除项
        // 内销综合成本 = 成本 / [(100 - 管销%) / 100] * (内销% / 100 + 1)   [成本 / 管销 * 内销税率]
        // 外销综合成本(含3‰保险) = 成本 / [(100 - 管销%) / 100] * 1.003  [成本 / 管销 * 保险]
        MARKETING = this.isDomesticSales === '内销' ? +parseFloat(this.total_sum / ((100 - this.marketing) / 100) * (this.saleRate / 100 + 1)).toFixed(5) : +parseFloat(this.total_sum / ((100 - this.marketing) / 100) * 1.003).toFixed(5)
      } else { // 有剔除项：剔除管销费用
        // 物料、包装剔除项的总金额
        this.rp_total = +parseFloat(parseFloat(this.rm_selectData) + parseFloat(this.pk_selectData)).toFixed(5)
        // console.log('物料、包装剔除金额——rp_total', this.rp_total)
        // 剔除总计 = 是否运费 ? 物料、包装、运费合计 : 物料、包装合计
        let romoveTotal = this.fr_boolean ? +parseFloat(Number(this.fr_sum) + this.rp_total).toFixed(5) : this.rp_total
        // console.log('剔除总金额——romoveTotal', romoveTotal)
        let NEW_TT = +parseFloat(this.total_sum - romoveTotal / this.totalRate).toFixed(5)
        // console.log('剩余金额——NEW_TT', NEW_TT)
        // 内销
        if (this.isDomesticSales === '内销') {
          // 剩余合计 = 剩余金额 / [(100 - 管销%) / 100] * (内销% / 100 + 1)
          TOTAL_SUM = +parseFloat(NEW_TT / ((100 - this.marketing) / 100) * (this.saleRate / 100 + 1)).toFixed(5)
          // 剔除合计 = 剔除金额 * (内销% / 100 + 1) / 汇率
          RP_SUM = +parseFloat(romoveTotal * (this.saleRate / 100 + 1) / this.totalRate).toFixed(5)
          // 总计
          MARKETING = +parseFloat(TOTAL_SUM + RP_SUM).toFixed(5)
        } else { // 外销
          // 剩余合计 = 成本 / (100 - 管销%) / 100
          TOTAL_SUM = +parseFloat(NEW_TT / ((100 - this.marketing) / 100)).toFixed(5)
          // 剔除合计 = 剔除金额 / 汇率
          RP_SUM = +parseFloat(romoveTotal / this.totalRate).toFixed(5)
          // 总计 = 正常合计 + 剔除合计 + 3‰保险
          MARKETING = +parseFloat((TOTAL_SUM + RP_SUM) * 1.003).toFixed(5)
        }
      }
      // console.log('剩余合计——TOTAL_SUM', TOTAL_SUM)
      // console.log('剔除合计——RP_SUM', RP_SUM)
      // console.log('总计——MARKETING', MARKETING)
      // 利润百分比
      const TAX5 = +parseFloat(MARKETING + MARKETING * 0.05).toFixed(5)
      const TAX10 = +parseFloat(MARKETING + MARKETING * 0.1).toFixed(5)
      const TAX15 = +parseFloat(MARKETING + MARKETING * 0.15).toFixed(5)
      const TAX20 = +parseFloat(MARKETING + MARKETING * 0.2).toFixed(5)
      const TAX25 = +parseFloat(MARKETING + MARKETING * 0.25).toFixed(5)
      const TAX30 = +parseFloat(MARKETING + MARKETING * 0.3).toFixed(5)
      this.costa = this.total_sum
      // console.log('成本——costa', this.costa)
      this.ttTableData = [
        {
          column: '成本',
          row: this.costa,
          meta: false
        },
        {
          column: '综合成本',
          row: MARKETING,
          meta: false
        },
        {
          column: '5%',
          row: TAX5,
          meta: false
        },
        {
          column: '10%',
          row: TAX10,
          meta: false
        },
        {
          column: '15%',
          row: TAX15,
          meta: false
        },
        {
          column: '20%',
          row: TAX20,
          meta: false
        },
        {
          column: '25%',
          row: TAX25,
          meta: false
        },
        {
          column: '30%',
          row: TAX30,
          meta: false
        },
        // 手动输入
        {
          column: 1,
          row: Number(MARKETING + MARKETING * 0.01).toFixed(5),
          meta: true
        }
      ]
    },
    // 运费剔除计算
    handleFRchange (v) {
      if (v) {
        let MARKETING = 0
        let TOTAL_SUM = 0
        let RP_SUM = 0
        let romoveTotal = +parseFloat(this.rp_total + Number(this.fr_sum)).toFixed(5)
        // console.log('剔除总金额——romoveTotal', romoveTotal)
        let NEW_TT = +parseFloat(this.total_sum - romoveTotal / this.totalRate).toFixed(5)
        // console.log('剩余金额——NEW_TT', NEW_TT)
        // 内销
        if (this.isDomesticSales === '内销') {
          // 剩余合计 = 剩余金额 / [(100 - 管销%) / 100] * (内销% / 100 + 1)
          TOTAL_SUM = +parseFloat(NEW_TT / ((100 - this.marketing) / 100) * (this.saleRate / 100 + 1)).toFixed(5)
          // 剔除合计 = 剔除金额 * (内销% / 100 + 1) / 汇率
          RP_SUM = +parseFloat(romoveTotal * (this.saleRate / 100 + 1) / this.totalRate).toFixed(5)
          // 总计
          MARKETING = +parseFloat(TOTAL_SUM + RP_SUM).toFixed(5)
        } else { // 外销
          // 剩余合计 = 成本 / (100 - 管销%) / 100
          TOTAL_SUM = +parseFloat(NEW_TT / ((100 - this.marketing) / 100)).toFixed(5)
          // 剔除合计 = 剔除金额 / 汇率
          RP_SUM = +parseFloat(romoveTotal / this.totalRate).toFixed(5)
          // 总计 = 正常合计 + 剔除合计 + 3‰保险
          MARKETING = +parseFloat((TOTAL_SUM + RP_SUM) * 1.003).toFixed(5)
        }
        // console.log('剩余合计——TOTAL_SUM', TOTAL_SUM)
        // console.log('剔除合计——RP_SUM', RP_SUM)
        // console.log('总计——MARKETING', MARKETING)
        // 利润百分比
        const TAX5 = +parseFloat(MARKETING + MARKETING * 0.05).toFixed(5)
        const TAX10 = +parseFloat(MARKETING + MARKETING * 0.1).toFixed(5)
        const TAX15 = +parseFloat(MARKETING + MARKETING * 0.15).toFixed(5)
        const TAX20 = +parseFloat(MARKETING + MARKETING * 0.2).toFixed(5)
        const TAX25 = +parseFloat(MARKETING + MARKETING * 0.25).toFixed(5)
        const TAX30 = +parseFloat(MARKETING + MARKETING * 0.3).toFixed(5)
        this.costa = this.total_sum
        // console.log('成本——costa', this.costa)
        this.ttTableData = [
          {
            column: '成本',
            row: this.costa,
            meta: false
          },
          {
            column: '综合成本',
            row: MARKETING,
            meta: false
          },
          {
            column: '5%',
            row: TAX5,
            meta: false
          },
          {
            column: '10%',
            row: TAX10,
            meta: false
          },
          {
            column: '15%',
            row: TAX15,
            meta: false
          },
          {
            column: '20%',
            row: TAX20,
            meta: false
          },
          {
            column: '25%',
            row: TAX25,
            meta: false
          },
          {
            column: '30%',
            row: TAX30,
            meta: false
          },
          // 手动输入
          {
            column: 1,
            row: Number(MARKETING + MARKETING * 0.01).toFixed(5),
            meta: true
          }
        ]
      } else {
        this.totalMethod()
      }
    },
    // 导出报价单PNG
    exportPNG () {
      html2Canvas(this.$refs.htmlDom).then(canvas => {
        let baseImg = canvas.toDataURL('image/png')
        posterimg.value = baseImg
        let save = document.createElement('a')
        // <a href=''></a>
        save.href = baseImg
        // 下载的名字
        save.download = '《' + this.editTitle + '》' + '-' + this.detailObjData.postForm.productConnection + '-' + formatDate(Date.now(), 'YYYYMMDD_HHmmss') + '.png'
        // 直接回调a的点击事件
        save.click()
      })
    },
    // 导出对比PNG
    exportDBPNG () {
      html2Canvas(this.$refs.htmlDBDom).then(canvas => {
        let baseImg = canvas.toDataURL('image/png')
        posterimg.value = baseImg
        let save = document.createElement('a')
        // <a href=''></a>
        save.href = baseImg
        // 下载的名字
        save.download = '价格对比' + '-' + formatDate(Date.now(), 'YYYYMMDD_HHmmss') + '.png'
        // 直接回调a的点击事件
        save.click()
      })
    },
    // 导出PDF(与Adobe不兼容)
    exportPDF () {
      const refDom = this.$refs.htmlDom.getBoundingClientRect()
      const pageWidth = 595.28 - 40 // A4纸的宽高 减去左右边距
      const pageHeight = 841.89
      // a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高
      html2Canvas(document.querySelector('#pdfDom'), {
        allowTaint: true,
        taintTest: false,
        useCORS: true,
        async: true,
        scale: '1', // 放大倍数
        dpi: '192', // 精度
        scrollY: refDom.top, // 关键代码
        height: refDom.height + 50 // 加高度，避免截取不全
      }).then(canvas => {
        const contentWidth = canvas.width
        const contentHeight = canvas.height
        const pageData = canvas.toDataURL('image/jpeg/png', 1)
        const PDF = new JsPDF('', 'pt', 'a4') // , true
        // canvas图片的高
        const imgHeight = pageWidth / contentWidth * contentHeight // canvas的宽与PDF的宽比列一样的时候，canvas的高缩放后的值
        let leftHeight = imgHeight
        let position = 0
        // 如果图片的高小于A4纸的高，不分页
        if (leftHeight < pageHeight) {
          PDF.addImage(pageData, 'JPEG', 20, 0, pageWidth, imgHeight)
        } else {
          // 分页
          // let index = 0
          while (leftHeight > 0) {
            // console.log(leftHeight, '每一页的高度', imgHeight, index)
            // index++
            // addImage(stream, startX, startY, width, height)
            // stream:图片流
            // (startX,startY)图片的放置的开始点
            PDF.addImage(pageData, 'JPEG', 20, position, pageWidth, imgHeight)
            leftHeight = leftHeight - pageHeight
            position -= pageHeight
            if (leftHeight > 0) {
              PDF.addPage()
            }
          }
        }
        PDF.save('《' + this.editTitle + '》' + '-' + this.detailObjData.postForm.productConnection + '-' + formatDate(Date.now(), 'YYYYMMDD_HHmmss') + '.pdf') // 保存PDF
        return PDF.output('datauristring')
      })
      // this.getPdf('《' + this.editTitle + '》.pdf', 'dispatchReportPage')
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .el-table__header-wrapper .el-checkbox {
  // display: none;//设置不成功，页面卡顿
  visibility: hidden;
}
/deep/ .el-autocomplete-suggestion li {
  text-align: left;
  white-space: normal;
  word-break: break-all;
  line-height: 24px;
}
#freightCSS {
  /deep/ .el-descriptions__body {
    background-color: rgb(253, 226, 226);
    padding: 16px;
  }
}
/deep/ .el-select, .el-input, .el-input-number, .el-input_inner {
  width: 100%;
}
/deep/ .el-input-number.is-controls-right .el-input__inner {
  text-align: left;
}
/deep/ .el-tree-node {
  white-space: normal; // 关键代码!!!!!!!!!
  .el-tree-node__content {
    height: 100%;
    align-items: start;
  }
}
.col {
  text-align: left;
  height: 36px;
  line-height: 56px;
  margin-bottom: 20px;
}
.one {
  background-color: rgb(236, 245, 255);
}
.two {
  background-color: rgb(253, 246, 236);
}
.three {
  background-color: rgb(240, 249, 235);
}
.four {
  background-color: rgb(254, 240, 240);
}
#shipping {
  width: 60%;
  float: left;
}
#copyShipping {
  width: 50%;
  float: left;
  .el-input-number--small {
    width: 100%;
  }
}
#configDlg{
  /deep/ .el-dialog__body {
    padding: 0 60px;
    height: 652px;
    overflow: scroll;
  }
  /deep/ .el-step.is-simple:not(:last-of-type) .el-step__title {
    max-width: 60%;
  }
}
/deep/ .el-divider--horizontal {
  margin: 0;
}
.tips {
  color: #409EFF;
  position: absolute;
  left: 50px;
  font-size: 12px;
  margin-top: 28px;
}
.nameTips {
  font-size: 11px;
  color: #409EFF;
}
.main {
  height:100vh;
  overflow:hidden;
  .topBtn {
    float: left;
    margin: 0 0 24px 0;
  }
  .addBtn {
    float: right;
    margin: 0 24px 32px;
  }
  .page {
    margin: 20px 0;
    text-align: center;
  }
  .total {
    color: #303133;
    text-align: right;
    padding: 16px 12px 0 0;
    font-size: 14px;
  }
  .lc-collapse {
    position: relative;
    .lc-percent {
      position: absolute;
      bottom: 36px;
      right: 38%;
    }
  }
  .db-collapse {
    text-align: left;
  }
  .tt-collapse {
    .tt-left {
      float: left;
      width: 30%;
      padding: 24px 0 0 120px;
      text-align: left;
      div {
        margin-bottom: 24px;
        span{
          display: inline-block;
          width: 72px;
          text-align: left;
        }
      }
    }
    .tt-right {
      float: right;
      padding: 0 36px 0 0;
      width: 40%;
    }
  }

  .query {
    // margin-bottom: 20px;
    &-wrapper {
      display: inline-block;
      width: 100%;
      margin: 0 0 16px 0;
      text-align: left;
      button {
        margin: 5px;
      }
    }
    &-info {
      display: inline-block;
      width: 16%;
      vertical-align: bottom;
      >span {
        font-size: 13px;
        float: left;
        width: 80px;
        line-height: 32px;
      }
      div {
        float: left;
        width: 66%;
      }
    }
  }
}
</style>
