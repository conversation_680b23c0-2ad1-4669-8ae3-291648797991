<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Title</title>
    <script src="js/jquery-1.12.4.js"></script>
    <style>
        div{
            position: relative;
            left: 40%;
            top: 30%;
            float: left;
        }
    </style>
</head>
<body>

<div>
    <h1>
        登录测试
    </h1>
    <span>用户：</span>
    <input type="text"  name="userName">
    <br>
    <span>密码：</span>
    <input type="password"  name="password">
    <br>
    <button >冲</button>
</div><br/>
<div id="data-container">
    <h1>我会变的</h1>
</div>
<script>
    $("button").click(function () {
        var Username=$("input[name='userName']").val();
        var Password=$("input[name='password']").val();
        // 获取用于显示数据的div元素
        const dataContainer = document.getElementById('data-container');
        $.ajax(
            {
                url:"/api/userdo",
                method:"post",
                data:
                    {
                        userName:Username,
                        password:Password
                    },
                dataType:"json",
                success:function (Result)
                {
                    if (Result.success)
                    {
                        data=Result.data;
                        //location.href="do.html";
                        console.log(Result.message)
                        console.log(Result.data.userName)

                    }
                    else alert(Result.message);
                }
            });
        alert(data);
    });
</script>
</body>
</html>
