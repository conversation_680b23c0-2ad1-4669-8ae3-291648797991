module.exports={A:{A:{"2":"J D E F A B 1B"},B:{"2":"C K L","194":"P Q R S T U V W X Y Z c d e f g h i j k l m n o a H","257":"G M N O"},C:{"2":"0 1 2 3 4 5 6 7 8 9 2B oB I p J D E F A B C K L G M N O q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB pB UB qB VB WB b XB YB ZB aB bB cB dB eB fB gB hB iB jB kB P Q R rB S T U V W X Y Z c d e f g h i j k l m n o a 3B 4B","16":"H sB"},D:{"2":"0 1 2 3 4 5 6 7 8 9 I p J D E F A B C K L G M N O q r s t u v w x y z","16":"AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB","194":"TB pB UB qB VB WB b XB YB ZB aB bB cB dB eB fB gB hB iB jB kB P Q R S T U V W X Y Z c d e f g h i j k l m n o a H sB 5B 6B"},E:{"2":"I p J D E 7B tB 8B 9B AC","16":"F A B C K L G BC uB lB mB CC DC EC vB wB xB yB nB FC"},F:{"2":"0 F B C G M N O q r s t u v w x y z GC HC IC JC lB zB KC mB","16":"1 2 3 4 5 6 7 8 9 AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB b XB YB ZB aB bB cB dB eB fB gB hB iB jB kB P Q R rB S T U V W X Y Z"},G:{"2":"E tB LC 0B MC NC OC PC","16":"QC RC SC TC UC VC WC XC YC ZC aC bC cC dC eC vB wB xB yB nB"},H:{"16":"fC"},I:{"2":"oB I gC hC iC jC 0B kC lC","16":"H"},J:{"2":"D A"},K:{"2":"A B C lB zB mB","16":"b"},L:{"16":"H"},M:{"16":"a"},N:{"2":"A","16":"B"},O:{"16":"mC"},P:{"16":"I nC oC pC qC rC uB sC tC uC vC wC nB xC yC"},Q:{"16":"zC"},R:{"16":"0C"},S:{"2":"1C"}},B:6,C:"Token Binding"};
