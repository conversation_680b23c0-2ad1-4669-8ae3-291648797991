<template>
  <div class="main">
    <div class="query">
     <div class="query-wrapper">
         <!-- <div class="query-info">
          <span>运费名称：</span>
          <el-input
            v-model="freightName"
            size="mini"
            placeholder="请输入运费名称查询"
            @keyup.enter.native="getFreight"
            :maxlength="256"
            clearable>
          </el-input>
        </div>
        <div class="query-info">
          <el-button size="mini" @click="getFreight" icon="el-icon-search">查询</el-button>
        </div> -->
        <div class="query-info" style="text-align:right">
          <el-button type="primary" size="mini" icon="el-icon-plus" @click="handleAdd">新增</el-button>
        </div>
      </div>
    </div>
    <el-table
      v-loading="loading"
      ref="table"
      class="mainTable"
      :data="freightTableData"
      :header-cell-style='{"backgroundColor": "rgb(244, 244, 245)"}'
      :height="tableHeight"
      stripe
    >
      <el-table-column
        label="#"
        type="index"
        align="center"
        width="60"
      />
      <el-table-column
        prop="name"
        label="港口"
      >
        <template slot-scope="scope">
          {{ scope.row.fr_factory + "到" + scope.row.fr_port }}
        </template>
      </el-table-column>
      <el-table-column
        prop="shippingType"
        label="出货类别"
        :filters="headerFilters['shippingType']"
        :filter-method="filterHeaders"
      >
        <template slot-scope="scope">
          {{ scope.row.fr_port === '深圳' && scope.row.shippingType==='整柜' ? scope.row.shippingType + '(' + scope.row.routes + ')' : scope.row.shippingType}}
        </template>
      </el-table-column>
      <el-table-column
        prop="fr_cbm"
        label="体积(CBM)"
      />
      <el-table-column
        prop="fr_price"
        label="价格(RMB)"
      />
      <el-table-column
        prop="others"
        label="其它费用(RMB)"
      />
      <el-table-column
        prop="remark"
        label="备注"
      />
      <el-table-column
        prop="updateTime"
        label="更新时间"
      />
      <el-table-column label="操作" align="center" min-width="130">
        <template slot-scope="scope">
          <el-button type="text" icon="el-icon-edit" @click="handleUpdateLabor(scope.row)">修改</el-button>
          <el-button type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="page">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="current"
        :page-sizes="[20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
    <!-- 新增/修改 -->
    <el-dialog :title="handleType==='create'?'新增':`修改《${editTitle}》内容`" width="40%" :visible.sync="handleModal" :before-close="handleModalClose">
      <el-form :model="formData" class="form-wrap" :rules="rules" ref="ruleForm" size="mini">
        <el-form-item prop="fr_factory" label="出货工厂: " label-width="100px" style="text-align: left;">
          <el-radio-group v-model="formData.fr_factory">
            <el-radio label="知腾"/>
            <el-radio label="迅安"/>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="fr_port" label="出货港口: " label-width="100px" style="text-align: left;">
          <el-radio-group v-model="formData.fr_port">
            <el-radio label="上海"/>
            <el-radio label="武汉"/>
            <el-radio label="深圳"/>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="shippingType" label="出货类别: " label-width="100px" style="text-align: left;">
          <el-radio-group v-model="formData.shippingType">
            <el-radio label="整柜"/>
            <el-radio label="散货"/>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="formData.fr_port === '深圳' && formData.shippingType === '整柜'" prop="routes" label="出货路线: " label-width="100px" style="text-align: left;">
          <el-radio-group v-model="formData.routes">
            <el-radio label="欧美线"/>
            <el-radio label="亚洲线"/>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="fr_cbm" label="CBM: " label-width="100px" style="text-align: left; width: 95%;">
          <el-input v-model="formData.fr_cbm" size="small" placeholder="请填写体积(CBM)">
          </el-input>
        </el-form-item>
        <el-form-item prop="fr_price" label="价格: " label-width="100px" style="text-align: left;; width: 95%">
          <el-input-number v-model="formData.fr_price" size="small" placeholder="请填写价格(RMB)" controls-position="right">
          </el-input-number>
        </el-form-item>
        <el-form-item prop="others" label="其它费用: " label-width="100px" style="text-align: left; width: 95%;">
          <el-input-number v-model="formData.others" size="small" placeholder="有无其它费用(RMB)" controls-position="right">
          </el-input-number>
        </el-form-item>
        <el-form-item prop="remark" label="备注: " label-width="100px" style="text-align: left; width: 95%;">
          <el-input v-model="formData.remark" size="small" placeholder="备注说明" type="textarea">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="callOf('ruleForm')">取 消</el-button>
        <el-button type="primary" size="mini" @click="handlePostForm('ruleForm')">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import ajax from '../common/axiosHttp'
import { formatDate, tableFilter } from '../common/utils'
export default {
  name: 'Freight',
  data () {
    return {
      headerFilters: {},
      loading: false,
      tableHeight: 0,
      freightTableData: [],
      current: 1,
      pageSize: 20,
      total: 0,
      handleType: '',
      handleModal: false,
      formData: {
        fr_factory: '',
        fr_price: 0,
        fr_cbm: 0,
        fr_port: ''
      },
      editTitle: '',
      rules: {
        fr_factory: [
          {
            required: true,
            message: '请选择',
            trigger: 'blur'
          }
        ],
        shippingType: [
          {
            required: true,
            message: '请选择',
            trigger: 'blur'
          }
        ],
        fr_price: [
          {
            required: true,
            type: 'number',
            message: '请填写',
            trigger: 'blur'
          }
        ],
        fr_port: [
          {
            required: true,
            message: '请选择',
            trigger: 'blur'
          }
        ],
        fr_cbm: [
          {
            required: true,
            message: '请填写',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  mounted () {
    // 挂载window.onresize事件(动态设置table高度)
    this.$nextTick(() => {
      this.tableHeight = window.innerHeight - 136
    })
    let _this = this
    window.onresize = () => {
      if (_this.resizeFlag) {
        clearTimeout(_this.resizeFlag)
      }
      _this.resizeFlag = setTimeout(() => {
        _this.getTableHeight()
        _this.resizeFlag = null
      }, 100)
    }
  },
  activated () {
    this.getFreight()
  },
  methods: {
    // 计算table高度(动态设置table高度)
    getTableHeight () {
      let tableH = 180 // 距离页面下方的高度
      let tableHeightDetil = window.innerHeight - tableH
      if (tableHeightDetil <= 300) {
        this.tableHeight = 300
      } else {
        this.tableHeight = window.innerHeight - tableH
      }
    },
    // 获取货品列表
    getFreight () {
      this.loading = true
      let form = new FormData()
      form.append('current', this.current)
      form.append('size', this.pageSize)
      form.append('fr_cbm', '')
      ajax({
        method: 'POST',
        url: '/api/FreightAll',
        data: form
      }).then(res => {
        this.loading = false
        this.total = res.total
        this.freightTableData = res.records
        this.headerFilters = tableFilter(this.freightTableData)
      }).catch(() => {
        this.loading = false
      })
    },
    // 页码分页
    handleSizeChange (val) {
      this.pageSize = val
      this.getFreight()
    },
    // 分页
    handleCurrentChange (val) {
      this.current = val
      this.getFreight()
    },
    // 表头筛选
    filterHeaders (value, row, colum) {
      const property = colum['property']
      return row[property] === value
    },
    // 新增
    handleAdd () {
      this.handleType = 'create'
      this.formData = {}
      this.handleModal = true
    },
    // 修改
    handleUpdateLabor (row) {
      this.editTitle = row.fr_name
      this.formData = row
      this.handleType = 'update'
      this.handleModal = true
    },
    // 关闭提醒
    handleModalClose (done) {
      this.$confirm('确认关闭吗？').then(_ => {
        done()
        this.formData = {}
        this.$refs['ruleForm'].resetFields()
      })
        .catch(_ => {})
    },
    // 关闭模态框
    callOf (formName) {
      this.handleModal = false
      this.formData = {}
      this.$refs[formName].resetFields()
    },
    // 提交保存
    handlePostForm (formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.formData.updateTime = formatDate(Date.now(), 'YYYY-MM-DD HH:mm:ss')
          if (this.handleType === 'create') {
            // let FLAG = false
            // this.freightTableData.forEach(item => {
            //   if (item.fr_cbm === '1' && item.fr_factory === this.formData.fr_factory && item.fr_port === this.formData.fr_port) {
            //     this.$message({
            //       offset: 100,
            //       message: '列表中已经存在该数据，请勿重复提交！',
            //       type: 'warning'
            //     })
            //     FLAG = true
            //   }
            // })
            // if (FLAG) return false
            ajax({
              method: 'POST',
              url: '/api/Freightadd',
              data: this.formData
            }).then(res => {
              this.$message({
                offset: 80,
                message: '保存成功！',
                type: 'success'
              })
              this.handleModal = false
              this.getFreight()
            })
          }
          if (this.handleType === 'update') {
            ajax({
              method: 'POST',
              url: '/api/updateFreight',
              data: this.formData
            }).then(res => {
              this.$message({
                offset: 80,
                message: '保存成功！',
                type: 'success'
              })
              this.handleModal = false
              this.getFreight()
            })
          }
        } else {
          return false
        }
      })
    },
    // 删除
    handleDelete (row) {
      this.$confirm(`您是否要删除当前数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ajax({
          method: 'POST',
          url: '/api/deleteFreight',
          data: {
            freightid: row.freightid
          }
        }).then(res => {
          this.$message({
            offset: 80,
            type: 'success',
            message: '操作成功！'
          })
          this.current = 1
          this.getFreight()
        })
      }).catch(() => {
        this.$message({
          offset: 80,
          type: 'info',
          message: '已取消删除！'
        })
      })
    }
  }
}
</script>
<style lang="less" scoped>
.main {
  height:100vh;
  overflow:hidden;
  /deep/ .el-select, .el-input, .el-input-number, .el-input_inner {
    width: 100%;
  }
  /deep/ .el-input__inner {
    text-align: left;
  }
  /deep/ .el-form-item--mini .el-form-item__content, .el-form-item--mini .el-form-item__label {
    text-align: left;
  }
  .query {
    // margin-bottom: 20px;
    &-wrapper {
      display: inline-block;
      width: 100%;
      margin: 0 0 16px 0;
      text-align: left;
      button {
        margin: 5px;
      }
    }
    &-info {
      display: inline-block;
      width: 100%;
      vertical-align: bottom;
      >span {
        font-size: 13px;
        float: left;
        width: 80px;
        line-height: 32px;
      }
      div {
        float: left;
        width: 70%;
      }
    }
  }
  .page {
    margin: 20px 0;
    text-align: center;
  }
  .topBtn {
    float: left;
    margin: 0 0 24px 0;
  }
  .addBtn {
    float: right;
    margin: 0 24px 32px;
  }
}
</style>
