package com.zt.yw.service.impl;

import com.zt.yw.dao.MoneyMapper;
import com.zt.yw.dao.ScMapper;
import com.zt.yw.entity.MoneyApi;
import com.zt.yw.service.MoneyService;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service
public class MoneyServiceImpl implements MoneyService {

    @Resource
    private MoneyMapper moneyMapper;

//查询全部税率
    @Override
    public List selectMoney(MoneyApi moneyApi) {

        return moneyMapper.selectList(null);
    }
//更新税率
    @Override
    public Integer updateMoney(@RequestBody MoneyApi moneyApi) {
        Date now1 = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        moneyApi.setMtime(format.format(now1));
        return moneyMapper.updateById(moneyApi);
    }
}
