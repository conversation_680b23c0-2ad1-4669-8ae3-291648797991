export const cookies = {
  setAuthorization: value => {
    cookies.setCookie('Authorization', value)
  },
  getAuthorization: () => {
    return cookies.getCookie('Authorization')
  },
  deleteAuthorization: () => {
    cookies.delCookie('Authorization')
  },
  setUserName: value => {
    cookies.setCookie('userName', value)
  },
  getUserName: () => {
    return cookies.getCookie('userName')
  },
  setCookie: (name, value) => {
    const Days = 0.5
    const exp = new Date()
    exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000)
    document.cookie = name + '=' + escape(value) + ';expires=' + exp.toGMTString()
  },
  getCookie: (name) => {
    const reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)')
    const arr = document.cookie.match(reg)
    if (arr) {
      return unescape(arr[2])
    } else {
      return null
    }
  },
  delCookie: (name) => {
    const exp = new Date()
    exp.setTime(exp.getTime() - 1)
    const cval = cookies.getCookie(name)

    if (cval != null) {
      document.cookie = name + '=' + cval + ';expires=' + exp.toGMTString()
    }
  }
}

export const isJSON = function (str) {
  if (typeof str === 'string') {
    try {
      JSON.parse(str)
      return true
    } catch (e) {
      return false
    }
  }
}

// 数组筛选：找出某个属性相同并且另一属性不同的数组（用于报价单中包装单价变更对比展示）
export const findIntersectionByProperty = function (arr1, arr2, prop, key) {
  return arr1.filter(item1 => arr2.some(item2 => item2[prop] === item1[prop] && item2[key] !== item1[key]))
}

export const tableFilter = function (list) {
  let filters = {}
  if (list.length) {
    Object.keys(list[0]).forEach(item => { // 拿到第一条数据，将key值组成数组，并将key给filters对象作为键名，值为空数组
      filters[item] = []
    })
    list.forEach(item => { // 遍历表格的数据数组
      for (let key in item) { // 遍历数据数组的每一项(对象)
        if (filters.hasOwnProperty(key) && !filters[key].find(i => i.text === item[key])) { // 如果filters对象中有当前键名（它的值是数组）,并且该数组中不含当前值的对象
          filters[key].push({text: item[key], value: item[key]}) // filters当前键名对应的值（数组），再push该值组成的对象（el-table筛选条件的格式）
        }
      }
    })
  }
  return filters
}

export const formatDate = function (date, format) {
  if (!date) {
    return ''
  }
  let d = new Date(date)
  // 年
  if (/YYYY/.test(format)) {
    format = format.replace(/YYYY/, d.getFullYear())
  }
  // 月份
  let month = d.getMonth() + 1
  if (/MM/.test(format)) {
    let monthStr = month < 10 ? '0' + month : month
    format = format.replace(/MM/, monthStr)
  } else if (/M/.test(format)) {
    format = format.replace(/M/, month)
  }
  // 日期
  let dates = d.getDate()
  if (/DD/.test(format)) {
    let dateStr = dates < 10 ? '0' + dates : dates
    format = format.replace(/DD/, dateStr)
  } else if (/D/.test(format)) {
    format = format.replace(/D/, dates)
  }
  // 小时
  let hours = d.getHours()
  if (/HH/.test(format)) {
    let hoursStr = hours < 10 ? '0' + hours : hours
    format = format.replace(/HH/, hoursStr)
  } else if (/H/.test(format)) {
    format = format.replace(/H/, hours)
  } else if (/hh/.test(format)) {
    let hoursMin = hours > 12 ? hours - 12 : hours
    let hoursStr = hoursMin < 10 ? '0' + hoursMin : hoursMin
    format = format.replace(/hh/, hoursStr)
  } else if (/h/.test(format)) {
    let hoursMin = hours > 12 ? hours - 12 : hours
    format = format.replace(/h/, hoursMin)
  }
  // 分
  let minutes = d.getMinutes()
  if (/mm/.test(format)) {
    let minutesStr = minutes < 10 ? '0' + minutes : minutes
    format = format.replace(/mm/, minutesStr)
  } else if (/m/.test(format)) {
    format = format.replace(/m/, minutes)
  }
  // 秒
  let seconds = d.getSeconds()
  if (/ss/.test(format)) {
    let secondsStr = seconds < 10 ? '0' + seconds : seconds
    format = format.replace(/ss/, secondsStr)
  } else if (/s/.test(format)) {
    format = format.replace(/s/, seconds)
  }
  return format
}
