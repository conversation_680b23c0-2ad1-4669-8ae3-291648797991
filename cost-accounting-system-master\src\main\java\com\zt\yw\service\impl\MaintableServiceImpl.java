package com.zt.yw.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.zt.yw.dao.*;
import com.zt.yw.entity.*;

import com.zt.yw.service.MaintableService;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class MaintableServiceImpl implements MaintableService {
    private static final Logger log = LoggerFactory.getLogger(MaintableServiceImpl.class);
    @Resource
    private MainMapper mainMapper;
    @Resource
    private EasyBaseMapper easyBaseMapper;
    @Resource
    private RmMapper rmMapper;
    @Resource
    private LcMapper lcMapper;
    @Resource
    private PkMapper pkMapper;
    @Resource
    private FrMapper frMapper;
    @Resource
    private RecordRmMapper recordRmMapper;
    @Resource
    private RecordLcMapper recordLcMapper;
    @Resource
    private RecordPkMapper recordPkMapper;





    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public boolean savamain(Maintable maintable){
        UserVo userVo=new UserVo();
        // RmTable rmTable=new RmTable();
        Date now1 = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        maintable.setUuid(format.format(now1));

        List<UserVo> materialData = maintable.getMaterialData();
        List<Lctable> lctableList =maintable.getLaborCostData();
        List<PkTable> pkTableList =maintable.getPackageFormData();
        Frtable frtable =maintable.getFreightFormData();
        if (CollectionUtils.isEmpty(materialData)) {
            throw new Exception("原材料数据为空");
        }

        materialData.forEach(materialDatas->{
            userVo.setOut(materialDatas.getOut());
            userVo.setBinding(materialDatas.getBinding());
            userVo.setFoam(materialDatas.getFoam());
            userVo.setIn(materialDatas.getIn());
            userVo.setMid(materialDatas.getMid());
            userVo.setOthers(materialDatas.getOthers());
            userVo.setNasal(materialDatas.getNasal());

        });
        mainMapper.insert(maintable);


        userVo.getOut().forEach(a->{
            RmTable rmTable=a;
            rmTable.setRm_id(null);//id重置
            rmTable.setUuid(format.format(now1));//写入统一uuid
            rmTable.setRm_prop("out");
            rmMapper.insert(rmTable);
        });

        userVo.getBinding().forEach(a->{
            RmTable rmTable=a;
            rmTable.setRm_id(null);
            rmTable.setUuid(format.format(now1));
            rmTable.setRm_prop("binding");
            rmMapper.insert(rmTable);
        });

        userVo.getNasal().forEach(a->{
            RmTable rmTable=a;
            rmTable.setRm_id(null);
            rmTable.setUuid(format.format(now1));
            rmTable.setRm_prop("nasal");
            rmMapper.insert(rmTable);
        });

        userVo.getFoam().forEach(a->{
            RmTable rmTable=a;
            rmTable.setRm_id(null);
            rmTable.setUuid(format.format(now1));
            rmTable.setRm_prop("foam");
            rmMapper.insert(rmTable);
        });

        userVo.getMid().forEach(a->{
            RmTable rmTable=a;
            rmTable.setRm_id(null);
            rmTable.setUuid(format.format(now1));
            rmTable.setRm_prop("mid");
            rmMapper.insert(rmTable);
        });

        userVo.getOthers().forEach(a->{
            RmTable rmTable=a;
            rmTable.setRm_id(null);
            rmTable.setUuid(format.format(now1));
            rmTable.setRm_prop("others");
            rmMapper.insert(rmTable);
        });

        userVo.getIn().forEach(a->{
            RmTable rmTable=a;
            rmTable.setRm_id(null);
            rmTable.setUuid(format.format(now1));
            rmTable.setRm_prop("in");
            rmMapper.insert(rmTable);
        });

        //保存工角
        lctableList.forEach(lctable->{
            lctable.setLc_id(null);
            lctable.setUuid(format.format(now1));
            lcMapper.insert(lctable);
        });

        //保存包装

        pkTableList.forEach(pkTable -> {
            pkTable.setPk_id(null);
            pkTable.setUuid(format.format(now1));
            pkMapper.insert(pkTable);
        });


        //保存运费
        frtable.setUuid(format.format(now1));
        frMapper.insert(frtable);
        return true;
    }

    @Override
    public R<Map<String, Object>> selectRc(Maintable maintable) {

        QueryWrapper queryWrapper=new QueryWrapper<>();
        // queryWrapper.lambda().orderByDesc(Maintable::getCreationDate);//进行排序查询
        queryWrapper.eq("uuid",maintable.getUuid());

        List<RecordRm> recordRmList = recordRmMapper.selectList(queryWrapper);//附表查询 原材料
        List<RecordLc> recordLcList = recordLcMapper.selectList(queryWrapper);//附表查询
        List<RecordPk> recordPkList = recordPkMapper.selectList(queryWrapper);//附表查询
        //多表查询结果list传到map赋值数组名
        Map<String,Object> map = new HashMap<>();
        map.put("rawMaterialTableData", recordRmList);
        map.put("LaborCostTableData", recordLcList);
        map.put("packageTableData", recordPkList);
        return R.ok(map);
    }

    //修改报价单页面运费
    @Override
    public int updateadminFr(Frtable frtable) {

        UpdateWrapper<Frtable> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("expense",frtable.getExpense()); // 设置要修改的字段及其值
        updateWrapper.eq("uuid", frtable.getUuid()); // 添加条件限制，只有符合条件的记录才会被更新

        return  frMapper.update(null,updateWrapper);
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public boolean updateadmin(Maintable maintable){


        mainMapper.updateById(maintable);

        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("uuid",maintable.getUuid());//相当于where uuid="xxxx"
        rmMapper.delete(wrapper);

        UserVo userVo=new UserVo();

        //   Date now1 = new Date();
        //   SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        //     maintable.setUuid(format.format(now1));

        List<UserVo> materialData = maintable.getMaterialData();
        List<RmTable> rmTables = maintable.getRmTableslist();
        //   maintable.setRmTableslist(materialDatas.getOut());

        if (CollectionUtils.isEmpty(materialData)) {
            throw new Exception("原材料数据为空");
        }
        materialData.forEach(materialDatas->{
            userVo.setFoam(materialDatas.getFoam());
            userVo.setIn(materialDatas.getIn());
            userVo.setMid(materialDatas.getMid());
            userVo.setOthers(materialDatas.getOthers());
            userVo.setNasal(materialDatas.getNasal());
            userVo.setBinding(materialDatas.getBinding());
            userVo.setOut(materialDatas.getOut());
            //   rmTables.addAll(materialDatas.getBinding());
            //  rmTables.addAll(materialDatas.getFoam());
        });

//        rmTables.addAll(userVo.getBinding());
        log.info("================================={}", rmTables);

        //mainMapper.insert(maintable);
        userVo.getOut().forEach(a-> {
            RmTable rmTable=a;
            rmTable.setUuid(maintable.getUuid());
            rmMapper.insert(rmTable);

        });


        userVo.getBinding().forEach(a->{
            RmTable rmTable=a;
            rmTable.setUuid(maintable.getUuid());
            rmMapper.insert(rmTable);
        });

        userVo.getNasal().forEach(a->{
            RmTable rmTable=a;
            rmTable.setUuid(maintable.getUuid());
            rmMapper.insert(rmTable);
        });

        userVo.getFoam().forEach(a->{
            RmTable rmTable=a;
            rmTable.setUuid(maintable.getUuid());
            rmMapper.insert(rmTable);
        });

        userVo.getMid().forEach(a->{
            RmTable rmTable=a;
            rmTable.setUuid(maintable.getUuid());
            rmMapper.insert(rmTable);
        });

        userVo.getOthers().forEach(a->{
            RmTable rmTable=a;
            rmTable.setUuid(maintable.getUuid());
            rmMapper.insert(rmTable);
        });

        userVo.getIn().forEach(a->{
            RmTable rmTable=a;
            rmTable.setUuid(maintable.getUuid());
            rmMapper.insert(rmTable);
        });
        return true;


    }



}

