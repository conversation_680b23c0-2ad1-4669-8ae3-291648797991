package com.zt.yw.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor //有参构造器
@NoArgsConstructor  //无参构造器
@TableName("fr_table")
public class Frtable implements Serializable {
  private static final long serialVersionUID = 1L;
  @TableId(value = "fr_id", type = IdType.AUTO)  //如数据库为自增id添加该行可解决数据库自增序号突然变大的问题，或者数据库id类型使用Long类型
  private Integer fr_id;
  @TableField("uuid")
  private String uuid;
  @TableField("counterSize")
  private String counterSize;
  @TableField("expense")
  private double expense;
  @TableField("factory")
  private String factory;
  @TableField("ctn")
  private Integer ctn;
  @TableField("port")
  private String port;
  @TableField("shippingType")
  private String shippingType;
  @TableField("cif")
  private String cif;
  @TableField("fr_amounts")
  private double fr_amounts;
  @TableField("fr_price")
  private double fr_price;
  @TableField("cmb")
  private String cmb;
  @TableField("terms")
  private String terms;
  @TableField("routes")
  private String routes;//出货线路，欧美，亚洲
  @TableField("others")
  private Integer others;//其他费用
  @TableField("sales_type")
  private String sales_type;//内销或者外销
 // private String sales_type;//内销或者外销
  @TableField("tax_domestic")
  private Integer tax_domestic;//内销税率
  @TableField("sales")
  private Integer sales;//管消税
  @TableField("currency")
  private String currency;//币别
  @TableField("exchange_rate")
  private double exchange_rate;//汇率
  @TableField("goodsCount")
  private Integer goodsCount;//口罩数量
    @TableField("lc_percentage")
    private double lc_percentage;//口罩数量


}

