@import "mixins/mixins";
@import "common/var";
@import "button";
@import "./popper";

@include b(dropdown) {
  display: inline-block;
  position: relative;
  color: $--color-text-regular;
  font-size: $--font-size-base;

  .el-button-group {
    display: block;
    .el-button {
      float: none;
    }
  }

  & .el-dropdown__caret-button {
    padding-left: 5px;
    padding-right: 5px;
    position: relative;
    border-left: none;

    &::before {
      $gap: 5px;

      content: '';
      position: absolute;
      display: block;
      width: 1px;
      top: $gap;
      bottom: $gap;
      left: 0;
      background: mix(white, transparent, 50%);
    }

    &.el-button--default::before {
      background: mix($--button-default-border-color, transparent, 50%);
    }

    &:hover {
      &:not(.is-disabled)::before {
        top: 0;
        bottom: 0;
      }
    }

    & .el-dropdown__icon {
      padding-left: 0;
    }
  }
  @include e(icon) {
    font-size: 12px;
    margin: 0 3px;
  }

  .el-dropdown-selfdefine { // 自定义
    &:focus:active, &:focus:not(.focusing) {
      outline-width: 0;
    }
  }

  [disabled] {
    cursor: not-allowed;
    color: $--font-color-disabled-base;
  }
}

@include b(dropdown-menu) {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  padding: 10px 0;
  margin: 5px 0;
  background-color: $--color-white;
  border: 1px solid $--border-color-lighter;
  border-radius: $--border-radius-base;
  box-shadow: $--dropdown-menu-box-shadow;

  @include e(item) {
    list-style: none;
    line-height: 36px;
    padding: 0 20px;
    margin: 0;
    font-size: $--font-size-base;
    color: $--color-text-regular;
    cursor: pointer;
    outline: none;
    &:not(.is-disabled):hover, &:focus {
      background-color: $--dropdown-menuItem-hover-fill;
      color: $--dropdown-menuItem-hover-color;
    }

    i {
      margin-right: 5px;
    }

    @include m(divided) {
      $divided-offset: 6px;

      position: relative;
      margin-top: $divided-offset;
      border-top: 1px solid $--border-color-lighter;

      &:before {
        content: '';
        height: $divided-offset;
        display: block;
        margin: 0 -20px;
        background-color: $--color-white;
      }
    }

    @include when(disabled) {
      cursor: default;
      color: $--font-color-disabled-base;
      pointer-events: none;
    }
  }

  @include m(medium) {
    padding: 6px 0;

    @include e(item) {
      line-height: 30px;
      padding: 0 17px;
      font-size: 14px;

      &.el-dropdown-menu__item--divided {
        $divided-offset: 6px;
        margin-top: $divided-offset;

        &:before {
          height: $divided-offset;
          margin: 0 -17px;
        }
      }
    }
  }

  @include m(small) {
    padding: 6px 0;

    @include e(item) {
      line-height: 27px;
      padding: 0 15px;
      font-size: 13px;

      &.el-dropdown-menu__item--divided {
        $divided-offset: 4px;
        margin-top: $divided-offset;

        &:before {
          height: $divided-offset;
          margin: 0 -15px;
        }
      }
    }
  }

  @include m(mini) {
    padding: 3px 0;

    @include e(item) {
      line-height: 24px;
      padding: 0 10px;
      font-size: 12px;

      &.el-dropdown-menu__item--divided {
        $divided-offset: 3px;
        margin-top: $divided-offset;

        &:before {
          height: $divided-offset;
          margin: 0 -10px;
        }
      }
    }
  }
}
