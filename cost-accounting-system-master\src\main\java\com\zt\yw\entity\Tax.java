package com.zt.yw.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor //有参构造器
@NoArgsConstructor  //无参构造器
/**
 * @TableName("user") 将当前的实体类和数据库的表建立联系
 * 注解参数：表名
 */
@TableName("user")
//测试用的


public class Tax implements Serializable  {
    private static final long serialVersionUID = 1L;
    /**
     * 主键属性  @TableId
     *
     * value 该属性对应的数据库表中的字段名
     * type 主键自增的类型 AUTO 代表自动递增
     */

    @TableId(value = "userid", type = IdType.AUTO)//如数据库为自增id添加该行可解决数据库自增序号突然变大的问题
    private Integer userid;
    /**
     * 非主键属性  @TableField
     *  @TableField("username")  参数为该属性对应的数据库表中的字段名
     *
     */
    @TableField("name")
    private String name;
    @TableField("password")
    private String password;
    @TableField("ghid")
    private String ghid;

}
