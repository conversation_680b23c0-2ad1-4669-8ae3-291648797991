package com.zt.yw.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor //有参构造器
@NoArgsConstructor  //无参构造器
@TableName("admin_lc")
public class AdminLc implements Serializable {
  private static final long serialVersionUID = 1L;
  @TableId(value = "admin_lc_id", type = IdType.AUTO)
  private Integer admin_lc_id;
  @TableField("lc_succession")
  private String lc_succession;
  @TableField("lc_standard")
  private String lc_standard;
  @TableField("lc_user")
  private String lc_user;
  @TableField("lc_data")
  private String lc_data;
  @TableField("uuid")
  private Integer uuid;

}
