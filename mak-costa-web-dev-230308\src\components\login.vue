<template>
  <div class="login-page">
    <div class="mask aside-login">
      <div class="formLogin">
        <p class="welcome">知勉工业股份有限公司</p>
        <p class="title" style="font-size: 38px;">成 本 核 算 系 统</p>
        <p class="title">欢迎登陆</p>
        <el-form :model="formInline" status-icon :rules="rules" ref="login" label-width="100px" class="demo-ruleForm">
          <el-form-item prop="userName">
            <el-input prefix-icon="el-icon-user" placeholder="请输入您的账号" v-model="formInline.userName"  @keyup.enter.native="submitForm('login')"></el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input prefix-icon="el-icon-lock" placeholder="请输入您的密码" type="password" v-model="formInline.password" auto-complete="off" @keyup.enter.native="submitForm('login')"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm('login')">登录</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script>
import { cookies, formatDate } from '../common/utils.js'
import ajax from '../common/axiosHttp'

export default {
  name: 'Login',
  data () {
    return {
      formInline: {
        userNName: '',
        password: ''
        // saveUsername: ''
      },
      rules: {
        userName: [
          { required: true, message: '用户名不能为空', trigger: 'blur' }
        ],
        password: [{ required: true, message: '密码不能为空', trigger: 'blur' }]
      }
    }
  },
  methods: {
    // ...mapActions(['login']),
    // ...mapMutations(['SET_USER_NAME']),
    /**
     * 表单提交的校验
     * @param {string} formName 表单的ref值
     */
    submitForm (formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.loginRequest(this.formInline.userName, this.formInline.password)
        } else {
          return false
        }
      })
    },
    loginRequest (userName, password) {
      let form = new FormData()
      form.append('userName', userName)
      form.append('password', password)
      form.append('last_login_tiem', formatDate(Date.now(), 'YYYY-MM-DD HH:mm:ss'))
      ajax({
        method: 'POST',
        url: '/api/userdo',
        data: form
      }).then(res => {
        if (res.data && res.data.code !== 200) {
          this.$message({
            type: 'error',
            message: res.data.message
          })
        } else {
          cookies.setCookie('userName', res.userName)
          cookies.setCookie('userid', res.userid)
          cookies.setCookie('power', res.power)
          this.$router.replace({path: '/'})
          this.$message({
            type: 'success',
            message: '登录成功！'
          })
        }
      }).catch((e) => {
        console.log('error', e)
      })
    }
  }
}
</script>
<style lang="less" scoped>
.login-page {
  /deep/.el-form-item__content {
    margin-left: 0 !important;
    position: relative;
    line-height: inherit;
  }
  .iconfont {
    position: absolute;
    z-index: 100;
    height: 34px;
    line-height: 34px;
    color: #ccc;
    font-size: 18px;
  }
  /deep/.el-form-item {
    margin: 32px auto 0;
    width: 260px;
  }
  /deep/.el-input__inner {
    height: 34px;
    line-height: 34px;
    color: #555;
    padding-left: 30px;
  }
  /deep/.el-input {
    height: 34px;
    line-height: 34px;
  }
  /deep/.el-button--primary {
    background-color: #0268b3;
    width: 260px;
    border-radius: 4px;
    box-shadow: 5px 0px 17px rgba(4, 84, 135, 0.5);
    height: 34px;
    border: 0;
    color: white;
    padding: 0;
    line-height: 34px;
  }
  /deep/.el-input__icon {
    height: 34px !important;
    line-height: 34px;
  }
  /deep/.el-form-item__error{
    padding-top: 2px;
  }

}

input::-webkit-input-placeholder {
  color: #555 !important;
}

input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #555;
}

input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: #555;
}

input:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #555;
}

.save {
  color: #000;
}
.save.is-checked {
  /deep/.el-checkbox__label {
    color: red;
  }
}
input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 1000px white inset;
  height: 30px !important;
  line-height: 30px !important;
  border-radius: 0 4px 4px 0;
}
.login-page {
  width: 100%;
  height: 100vh;
  background-image: url("../assets/bg_login.jpg");
  background-size: cover;
  background-position: center;
}
.aside-login {
  position: absolute;
  top: 0;
  right: 0;
  width: 540px;
  height: auto;
  bottom: 0;
  overflow: hidden;
  transition: width 0.3s ease-out;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
  background-position: right bottom;
}
.mask:before {
  content: " ";
  width: 100%;
  height: 100%;
  margin: auto 0;
  position: absolute;
  left: 0px;
  right: 0px;
  background: rgba(0, 0, 0, 0.5);
  background-size: cover;
}

.formLogin {
  width: 100%;
  position: absolute;
  top: 26%;
  margin: 0 auto;
  color: #3072b5;
  border-radius: 20px;
  z-index: 2;
}

.form-box {
  width: 280px;
  margin: 0 auto;
  height: auto;
  text-align: center;
  position: relative;
}

.title {
  font-size: 24px;
  color: #ffffff;
  margin: 0px auto 40px;
  text-align: center;
}

.form-box .title span {
  font-size: 16px;
  color: #ffffff;
}

.welcome {
  font-size: 18px;
  color: #ffffff;
  font-family: MicrosoftYaHei;
  letter-spacing: 1.2px;
  text-align: center;
}

.line-input {
  margin: 15px auto 0;
  border: none;
  outline: none;
  color: white;
  text-indent: 10px;
  background: #fff;
  width: 260px;
  height: 34px;
  border-radius: 4px;
}

.line-input .input-box {
  display: block;
  width: 85%;
  height: 100%;
  margin: 0 0 0 35px;
  position: relative;
}

.line-input .input-box input {
  outline: none;
  border: none;
  background: none;
  font-size: 14px;
  height: 100%;
  color: #555;
  position: absolute;
  left: 0;
  padding: 0;
}

.line-input label {
  float: left;
  width: 45px;
  height: 34px;
  line-height: 34px;
  margin-bottom: 0 !important;
  padding: 0 !important;
  margin: 0 10px 0 -12px;
}

.line-input label i {
  color: #ccc;
  font-size: 18px;
}

.iconhead:before {
  content: "\e610";
}

.msg {
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #82aed2;
  width: 260px;
  margin: 1px auto 14px;
  display: flex;
}

.msg span.firstSpan {
  color: #fff;
  text-align: left;
  width: 50%;
}

.msg span label {
  color: #fff;
  font-weight: normal !important;
}

.msg span.secondSpan {
  width: 50%;
  text-align: right;
  color: #ff0000;
}

.button-primary {
  width: 260px;
  background-color: #0268b3;
  border-radius: 4px;
  box-shadow: 5px 0px 17px rgba(4, 84, 135, 0.5);
  height: 34px;
  line-height: 34px;
  border: 0;
  color: white;
}

.tip-problem {
  width: 141px;
  font-size: 14px;
  color: #fff;
  margin: 10px;
}

.tip-problem a {
  color: #83bee4;
  text-decoration-line: underline;
  font-size: 14px;
}
</style>
