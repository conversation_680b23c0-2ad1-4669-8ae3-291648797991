/*
 Navicat Premium Dump SQL

 Source Server         : 本地数据库
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : yw_database

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 28/04/2025 15:44:01
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for admin_lc
-- ----------------------------
DROP TABLE IF EXISTS `admin_lc`;
CREATE TABLE `admin_lc`  (
  `admin_lc_id` int NOT NULL,
  `lc_succession` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '系列',
  `lc_standard` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '标准',
  `lc_user` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '创建人员',
  `lc_data` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '创建时间',
  `uuid` int NOT NULL COMMENT 'uuid唯一关联',
  PRIMARY KEY (`admin_lc_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of admin_lc
-- ----------------------------

-- ----------------------------
-- Table structure for fr_table
-- ----------------------------
DROP TABLE IF EXISTS `fr_table`;
CREATE TABLE `fr_table`  (
  `fr_id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `uuid` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `counterSize` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '货柜尺寸',
  `expense` double(255, 6) NULL DEFAULT NULL COMMENT '运费',
  `factory` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '出货港口',
  `ctn` int NULL DEFAULT NULL COMMENT '箱数',
  `port` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '港口',
  `shippingType` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '出货类型',
  `fr_amounts` double(255, 6) NULL DEFAULT NULL COMMENT '金额',
  `cif` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT 'cif',
  `fr_price` double(255, 6) NULL DEFAULT NULL COMMENT '金额',
  `cmb` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT 'cmb',
  `terms` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '贸易条件',
  `routes` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '线路，欧洲，亚洲',
  `others` int NULL DEFAULT NULL COMMENT '其他费用',
  `sales_type` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '内销或者外销',
  `tax_domestic` int NULL DEFAULT NULL COMMENT '内销税率',
  `sales` int NULL DEFAULT NULL COMMENT '管消费',
  `currency` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '币种',
  `exchange_rate` double NULL DEFAULT NULL COMMENT '汇率',
  `goodsCount` int NULL DEFAULT NULL COMMENT '口罩数量',
  `lc_percentage` double NULL DEFAULT NULL COMMENT '包装的百分比保存',
  PRIMARY KEY (`fr_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 113 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '主页保存后 运费关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of fr_table
-- ----------------------------
INSERT INTO `fr_table` VALUES (19, '20240111151047', NULL, 2000.000000, '知腾', 417, '武汉', '整柜', 0.000000, NULL, 0.020000, '18.6', 'FOB', NULL, NULL, NULL, 15, 20, 'USD', 7.19, 100000, 156);
INSERT INTO `fr_table` VALUES (20, '20240111151358', NULL, 2000.000000, '知腾', 834, '武汉', '整柜', 0.000000, NULL, 0.020000, '37.2', 'FOB', NULL, NULL, NULL, NULL, NULL, NULL, 0, 100000, 0);
INSERT INTO `fr_table` VALUES (21, '20240111151547', NULL, 2000.000000, '知腾', 834, '武汉', '整柜', 0.000000, NULL, 0.020000, '37.2', 'FOB', NULL, NULL, NULL, 13, 20, 'CNY', 1, 100000, 156);
INSERT INTO `fr_table` VALUES (22, '20240111151745', NULL, 2000.000000, '知腾', 834, '武汉', '整柜', 0.000000, NULL, 0.020000, '37.2', 'FOB', NULL, NULL, NULL, 13, 20, 'CNY', 1, 100000, 156);
INSERT INTO `fr_table` VALUES (99, '20241216151155', NULL, 5000.000000, '迅安', 500, '深圳', '整柜', 0.000000, NULL, 0.555556, '24.5', 'FOB', NULL, NULL, NULL, NULL, NULL, NULL, 0, 9000, 0);
INSERT INTO `fr_table` VALUES (100, '20241216152147', NULL, 3000.000000, '知腾', 542, '武汉', '整柜', 0.000000, NULL, 0.023077, '26.6', 'FOB', NULL, NULL, NULL, NULL, NULL, NULL, 0, 130000, 0);
INSERT INTO `fr_table` VALUES (109, '20250102090918', NULL, 0.000000, '知腾', 500, '', NULL, 0.000000, NULL, 0.000000, '25.4', 'Ex Works', NULL, NULL, '内销', NULL, NULL, NULL, 0, 30000, 0);
INSERT INTO `fr_table` VALUES (110, '20250102091841', NULL, 3000.000000, '迅安', 500, '深圳', '整柜', 0.000000, NULL, 0.050000, '24.5', 'FOB', NULL, NULL, '外销', NULL, NULL, NULL, 0, 60000, 0);
INSERT INTO `fr_table` VALUES (111, '20250102094044', NULL, 6000.000000, '迅安', 500, '深圳', '整柜', 0.000000, NULL, 0.200000, '25.4', 'FOB', NULL, NULL, '外销', NULL, NULL, NULL, 0, 30000, 0);
INSERT INTO `fr_table` VALUES (112, '20250102094205', NULL, 3000.000000, '知腾', 500, '', '整柜', 0.000000, NULL, 0.050000, '24.5', 'To Door', NULL, NULL, '内销', NULL, NULL, NULL, 0, 60000, 0);

-- ----------------------------
-- Table structure for freight
-- ----------------------------
DROP TABLE IF EXISTS `freight`;
CREATE TABLE `freight`  (
  `freightid` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `fr_factory` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '出货工厂',
  `fr_port` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '出货港口',
  `fr_cbm` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '体积',
  `fr_price` double NULL DEFAULT NULL COMMENT '价格',
  `cmb` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '货柜体积',
  `terms` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '贸易条件',
  `updateTime` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  `shippingType` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '出货类型',
  `routes` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '路线， 区分欧美亚洲 ',
  `others` int NULL DEFAULT NULL COMMENT '其他费用',
  PRIMARY KEY (`freightid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 56 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '运费资料库' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of freight
-- ----------------------------

-- ----------------------------
-- Table structure for goodslist
-- ----------------------------
DROP TABLE IF EXISTS `goodslist`;
CREATE TABLE `goodslist`  (
  `goodsid` int NOT NULL AUTO_INCREMENT,
  `rm_model` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '型号',
  `rm_itemType` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '种类',
  `rm_standard` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '标准',
  `rm_succession` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '系列',
  `rm_supplier` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '厂商',
  `rm_number` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '品号',
  `rm_material` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '品名',
  `rm_price` double(255, 6) NULL DEFAULT NULL COMMENT '单价',
  `rm_unit` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '单位',
  `rm_consumption` double(255, 6) NULL DEFAULT NULL COMMENT '用量',
  `rm_currencyType` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '币别',
  `rm_exchangeRate` double(255, 2) NULL DEFAULT NULL COMMENT '汇率',
  `rm_loss` double(255, 2) NULL DEFAULT NULL,
  `rm_amounts` double(255, 3) NULL DEFAULT NULL COMMENT '总金额',
  PRIMARY KEY (`goodsid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 30 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '原材料资料库' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of goodslist
-- ----------------------------
INSERT INTO `goodslist` VALUES (1, NULL, '鼻夹', NULL, NULL, '昊森', '20240108142707', '鼻夹:原色铝片0.8mm*5mm弯型(昊森)', 36.000000, 'KG', NULL, 'CNY', 1.00, NULL, NULL);
INSERT INTO `goodslist` VALUES (2, NULL, '头带', NULL, NULL, '唯宾', '20240108142727', '松紧带:黄色氨纶织带6.8mm扁型(唯宾)', 34.000000, 'KG', NULL, 'CNY', 1.00, NULL, NULL);
INSERT INTO `goodslist` VALUES (3, NULL, '内层', NULL, NULL, '日月', '20240108142800', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.100000, 'KG', NULL, 'CNY', 1.00, NULL, NULL);
INSERT INTO `goodslist` VALUES (4, NULL, '内层', NULL, NULL, '汇维仕', '20240108142832', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.600000, 'KG', NULL, 'CNY', 1.00, NULL, NULL);
INSERT INTO `goodslist` VALUES (5, NULL, '外层', NULL, NULL, '日月', '20240108142848', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.100000, 'KG', NULL, 'CNY', 1.00, NULL, NULL);
INSERT INTO `goodslist` VALUES (6, NULL, '外层', NULL, NULL, '汇维仕', '20240108142905', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.600000, 'KG', NULL, 'CNY', 1.00, NULL, NULL);
INSERT INTO `goodslist` VALUES (7, NULL, '其它', NULL, NULL, '阔润', '20240108142921', '气阀:白色ABS料39.4mm圆型自动(阔润)', 0.180000, 'PCS', NULL, 'CNY', 1.00, NULL, NULL);
INSERT INTO `goodslist` VALUES (8, NULL, '其它', NULL, NULL, '星成', '20240108143002', '气阀片:白色硅胶26.5mm圆型有孔(星成)', 0.080000, 'PCS', NULL, 'CNY', 1.00, NULL, NULL);
INSERT INTO `goodslist` VALUES (9, NULL, '中层', NULL, NULL, '森友', '20240108143025', '滤材:黑色活性碳布135g/m2*18.5cm(森友)', 89.000000, 'KG', NULL, 'CNY', 1.00, NULL, NULL);
INSERT INTO `goodslist` VALUES (10, NULL, '中层', NULL, NULL, '广能', '20240108143136', '滤材:白色熔喷布38g/m2*18.5cm(广能)', 3.800000, '㎡', NULL, 'CNY', 1.00, NULL, NULL);
INSERT INTO `goodslist` VALUES (11, NULL, '中层', NULL, NULL, '俊富', '20240108143158', '滤材:白色熔喷布50g/m2*18.5cm(俊富)', 2.050000, '㎡', NULL, 'CNY', 1.00, NULL, NULL);
INSERT INTO `goodslist` VALUES (12, NULL, '泡棉', NULL, NULL, '超联', '20240108143214', '鼻垫:黑色PU 90*12*3mm弯型全闭孔圆角(超联)', 0.034000, 'PCS', NULL, 'CNY', 1.00, NULL, NULL);
INSERT INTO `goodslist` VALUES (25, NULL, '其它', NULL, NULL, '忠和', '20241113102328', '油墨:黑色53188 PP用(忠和)', 395.000000, 'KG', NULL, 'CNY', 1.00, NULL, NULL);
INSERT INTO `goodslist` VALUES (26, NULL, '中层', NULL, NULL, '称道', '20241113102511', '滤材:白色熔喷布38g/m2*18.5cm(称道)', 1.824000, 'KG', NULL, 'CNY', 1.00, NULL, NULL);
INSERT INTO `goodslist` VALUES (27, NULL, '中层', NULL, NULL, '俊富', '20241113102535', '滤材:白色熔喷布38g/m2*18.5cm(俊富)', 1.370000, 'KG', NULL, 'CNY', 1.00, NULL, NULL);
INSERT INTO `goodslist` VALUES (29, NULL, '中层', NULL, NULL, '森友', '20241216141738', '滤材：黑色活性炭90g/m2*18.5cm(森友)', 63.000000, 'KG', NULL, 'CNY', 1.00, NULL, NULL);

-- ----------------------------
-- Table structure for laborcost
-- ----------------------------
DROP TABLE IF EXISTS `laborcost`;
CREATE TABLE `laborcost`  (
  `LaborCostid` int NOT NULL AUTO_INCREMENT,
  `lc_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '种类',
  `lc_price` double(255, 5) NULL DEFAULT NULL COMMENT '单价',
  `lc_unit` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '单位',
  `lc_consumption` double(255, 9) NULL DEFAULT NULL COMMENT '用量',
  `lc_exchangeRate` double(255, 2) NULL DEFAULT NULL COMMENT '汇率',
  `lc_amounts` double(255, 5) NULL DEFAULT NULL COMMENT '金额',
  `lc_currencyType` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '币种',
  `lc_factory` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '工角所属工厂',
  PRIMARY KEY (`LaborCostid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 42 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '人工资料表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of laborcost
-- ----------------------------
INSERT INTO `laborcost` VALUES (31, '人工统一工价（9500-N95）', 0.14717, NULL, 0.000000000, 1.00, 0.14717, 'CNY', NULL);
INSERT INTO `laborcost` VALUES (33, '人工统一工价（9500-N95OV）', 0.26185, NULL, 0.000000000, 1.00, 0.26185, 'CNY', NULL);
INSERT INTO `laborcost` VALUES (34, '人工统一工价（9500V-N95）', 0.19039, NULL, 0.000000000, 1.00, 0.19039, 'CNY', NULL);
INSERT INTO `laborcost` VALUES (35, '人工统一工价（9500VOV-N95）', 0.30941, NULL, 0.000000000, 1.00, 0.30941, 'CNY', NULL);
INSERT INTO `laborcost` VALUES (39, '人工统一工价（SEKURA-321)', 0.08626, NULL, 0.000000000, 1.00, 0.08626, 'CNY', NULL);
INSERT INTO `laborcost` VALUES (40, 'test(迅安)', 0.88888, NULL, 0.000000000, 1.00, 0.88888, 'CNY', '迅安');
INSERT INTO `laborcost` VALUES (41, 'test(知腾)', 0.66660, NULL, 0.000000000, 1.00, 0.66660, 'CNY', '知腾');

-- ----------------------------
-- Table structure for lc_table
-- ----------------------------
DROP TABLE IF EXISTS `lc_table`;
CREATE TABLE `lc_table`  (
  `lc_id` int NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主外键品号',
  `lc_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '种类',
  `lc_price` double(255, 5) NOT NULL COMMENT '单价',
  `lc_unit` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '单位',
  `lc_consumption` double(255, 9) NOT NULL COMMENT '用量',
  `lc_exchangeRate` double(255, 2) NOT NULL COMMENT '汇率',
  `lc_amounts` double(255, 5) NOT NULL COMMENT '金额',
  `lc_currencyType` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '币种',
  `lc_factory` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '工角所属工厂',
  PRIMARY KEY (`lc_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2763 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of lc_table
-- ----------------------------
INSERT INTO `lc_table` VALUES (270, '20240111151047', '包装统一工角报价', 0.04000, '无', 1.000000000, 1.00, 0.04000, NULL, NULL);
INSERT INTO `lc_table` VALUES (281, '20240111151358', '包装统一工角报价', 0.04000, '无', 1.000000000, 1.00, 0.04000, NULL, NULL);
INSERT INTO `lc_table` VALUES (296, '20240111151547', '包装统一工角报价', 0.04000, '无', 1.000000000, 1.00, 0.04000, NULL, NULL);
INSERT INTO `lc_table` VALUES (2699, '20240109084034', 'test(知腾)', 0.66660, NULL, 1.000000000, 1.00, 0.00000, NULL, '知腾');
INSERT INTO `lc_table` VALUES (2700, '20240108164858', '人工统一工价（9500-N95OV）', 0.27907, NULL, 1.000000000, 1.00, 0.00000, NULL, NULL);
INSERT INTO `lc_table` VALUES (2701, '20240109100334', '人工统一工价（9500VOV-N95）', 0.32041, NULL, 1.000000000, 1.00, 0.00000, NULL, NULL);
INSERT INTO `lc_table` VALUES (2720, '20240111151745', '包装统一工角报价', 0.04000, '', 1.000000000, 1.00, 0.04000, 'CNY', NULL);
INSERT INTO `lc_table` VALUES (2721, '20240111151745', '人工统一工价（9500VOV-N95）', 0.30941, NULL, 1.000000000, 1.00, 0.30941, 'CNY', NULL);
INSERT INTO `lc_table` VALUES (2722, '20240111151358', '人工统一工价（9500V-N95）', 0.19039, NULL, 1.000000000, 1.00, 0.19039, 'CNY', NULL);
INSERT INTO `lc_table` VALUES (2723, '20240111151547', '人工统一工价（9500-N95OV）', 0.26185, NULL, 1.000000000, 1.00, 0.26185, 'CNY', NULL);
INSERT INTO `lc_table` VALUES (2724, '20240111151047', '人工统一工价（9500-N95）', 0.14717, NULL, 1.000000000, 1.00, 0.14717, 'CNY', NULL);
INSERT INTO `lc_table` VALUES (2727, '20241216151155', '包装统一工角报价', 0.50000, '无', 1.000000000, 1.00, 0.50000, NULL, NULL);
INSERT INTO `lc_table` VALUES (2728, '20241216151155', '包装统一工角报价', 0.04000, '无', 1.000000000, 1.00, 0.04000, NULL, NULL);
INSERT INTO `lc_table` VALUES (2729, '20241216152147', '包装统一工角报价', 0.03570, '无', 1.000000000, 1.00, 0.03570, NULL, NULL);
INSERT INTO `lc_table` VALUES (2730, '20241216152147', '人工统一工价（9600-N95)', 0.08626, NULL, 1.000000000, 1.00, 0.08626, NULL, NULL);
INSERT INTO `lc_table` VALUES (2754, '20250102090918', '包装统一工角', 0.04000, '无', 1.000000000, 1.00, 0.04000, NULL, NULL);
INSERT INTO `lc_table` VALUES (2755, '20250102090918', '人工统一工价（9500-N95OV）', 0.27907, NULL, 1.000000000, 1.00, 0.27907, NULL, NULL);
INSERT INTO `lc_table` VALUES (2756, '20250102091841', '包装统一工角', 0.04000, '无', 1.000000000, 1.00, 0.04000, NULL, NULL);
INSERT INTO `lc_table` VALUES (2757, '20250102091841', 'test(知腾)', 0.66660, NULL, 1.000000000, 1.00, 0.66660, NULL, '知腾');
INSERT INTO `lc_table` VALUES (2758, '20250102094044', '人工统一工价（9500-N95OV）', 0.27907, NULL, 1.000000000, 1.00, 0.27907, NULL, NULL);
INSERT INTO `lc_table` VALUES (2759, '20250102094044', '包装统一工角', 0.04000, '无', 1.000000000, 1.00, 0.04000, NULL, NULL);
INSERT INTO `lc_table` VALUES (2760, '20250102094044', 'test(迅安)', 0.88888, '无', 1.000000000, 1.00, 0.88888, NULL, '迅安');
INSERT INTO `lc_table` VALUES (2761, '20250102094205', '包装统一工角报价', 0.40000, '无', 1.000000000, 1.00, 0.40000, NULL, NULL);
INSERT INTO `lc_table` VALUES (2762, '20250102094205', 'test(知腾)', 0.66660, '无', 1.000000000, 1.00, 0.66660, NULL, '知腾');

-- ----------------------------
-- Table structure for main_table
-- ----------------------------
DROP TABLE IF EXISTS `main_table`;
CREATE TABLE `main_table`  (
  `admin_id` int NOT NULL AUTO_INCREMENT COMMENT '序号id',
  `uuid` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '品号主外关联',
  `productName` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '品名',
  `updatePersonnel` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '最新更新人员',
  `updateTime` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最新修改时间',
  `LastTime` datetime NULL DEFAULT NULL COMMENT '上次修改时间',
  `LastModifiedBy` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '上次修改人员',
  `CreationDate` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `Creator` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '创建人员',
  `productModel` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '型号',
  `productStandard` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '标准',
  `productSuccession` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '规格',
  `productDetail` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '文本描述',
  `productConnection` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '客户公司',
  `productFactory` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '所属工厂',
  `productPackage` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '产品包装',
  `pcsCount` int NULL DEFAULT NULL COMMENT '包装pcs',
  `bagCount` int NULL DEFAULT NULL COMMENT '包装盒',
  `boxCount` int NULL DEFAULT NULL COMMENT '包装箱',
  PRIMARY KEY (`admin_id`, `uuid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 127 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '主页主品信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of main_table
-- ----------------------------
INSERT INTO `main_table` VALUES (19, '20240111151047', '9500-N95 标准版', '李龙凯', '2024-12-17 14:14:13', NULL, NULL, '2024-01-11 15:10:51', '续愿', NULL, '通用', '9500-N95系列', '基础模版，用于数据建设，请勿删除', 'Makrite', '迅安', '', 20, 1, 12);
INSERT INTO `main_table` VALUES (20, '20240111151358', '9500V-N95 标准版', '李龙凯', '2024-12-16 08:10:42', NULL, NULL, '2024-01-11 15:14:01', '续愿', NULL, '通用', '9500-N95系列', '基础模版，用于数据建设，请勿删除', 'Makrite', NULL, NULL, 10, 1, 12);
INSERT INTO `main_table` VALUES (21, '20240111151547', '9500OV-N95 标准版', '李龙凯', '2024-12-16 08:11:01', NULL, NULL, '2024-01-11 15:15:51', '续愿', NULL, '通用', '9500-N95系列', '基础模版，用于数据建设，请勿删除', 'Makrite', NULL, NULL, 10, 1, 12);
INSERT INTO `main_table` VALUES (22, '20240111151745', '9500VOV-N95 标准版', '李龙凯', '2024-12-16 08:10:28', NULL, NULL, '2024-01-11 15:17:49', '续愿', NULL, '通用', '9500-N95系列', '基础模版，用于数据建设，请勿删除', 'Makrite', NULL, NULL, 10, 1, 12);
INSERT INTO `main_table` VALUES (112, '20241216151155', '9500-N95  COPY', NULL, NULL, NULL, NULL, '2024-12-16 15:12:01', 'admin', NULL, NULL, NULL, NULL, 'A', NULL, NULL, 3, 1, 6);
INSERT INTO `main_table` VALUES (113, '20241216152147', '9600-N95', NULL, NULL, NULL, NULL, '2024-12-16 15:21:53', 'admin', NULL, '美规', '9600系列', 'test', 'Makrite', NULL, NULL, 20, 1, 12);
INSERT INTO `main_table` VALUES (123, '20250102090918', '内销test-ZT', NULL, NULL, NULL, NULL, '2025-01-02 09:09:26', '续愿', NULL, '美规', '9500-N95系列', NULL, 'aa', '知腾', NULL, 5, 1, 12);
INSERT INTO `main_table` VALUES (124, '20250102091841', '外销test-XA', NULL, NULL, NULL, NULL, '2025-01-02 09:18:49', '续愿', NULL, '美规', '9500-N95系列', 'TEST(外销)', 'BB', '迅安', NULL, 10, 1, 12);
INSERT INTO `main_table` VALUES (125, '20250102094044', 'copy外销-XA', NULL, NULL, NULL, NULL, '2025-01-02 09:40:52', '续愿', NULL, NULL, NULL, '(外销)abcd', 'cc', '迅安', NULL, 5, 1, 12);
INSERT INTO `main_table` VALUES (126, '20250102094205', 'copy内销-ZT', NULL, NULL, NULL, NULL, '2025-01-02 09:42:13', '续愿', NULL, NULL, NULL, '(内销)undefined', 'ddd', '知腾', NULL, 20, 1, 6);

-- ----------------------------
-- Table structure for moneyapi
-- ----------------------------
DROP TABLE IF EXISTS `moneyapi`;
CREATE TABLE `moneyapi`  (
  `moneyid` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '中文',
  `currency` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '币种',
  `exchangerate` double(255, 6) NOT NULL COMMENT '汇率',
  `modifyuser` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '修改人员',
  `mtime` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`moneyid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of moneyapi
-- ----------------------------
INSERT INTO `moneyapi` VALUES (1, '美元', 'USD', 7.170300, '系统自动更新', '2025-01-21 10:40:12');
INSERT INTO `moneyapi` VALUES (2, '人民币', 'CNY', 1.000000, '系统自动更新', '2025-01-02 10:39:31');
INSERT INTO `moneyapi` VALUES (3, '欧元', 'EUR', 7.500200, '系统自动更新', '2025-01-21 10:40:12');
INSERT INTO `moneyapi` VALUES (4, '美元（利润）', 'USDP', 6.900000, '管理员', '2024-11-27 10:31:12');

-- ----------------------------
-- Table structure for packinglist
-- ----------------------------
DROP TABLE IF EXISTS `packinglist`;
CREATE TABLE `packinglist`  (
  `packid` int NOT NULL AUTO_INCREMENT,
  `pk_itemType` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '具体型号',
  `pk_supplier` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '厂商',
  `pk_number` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '品号',
  `pk_material` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '品名',
  `pk_price` double(255, 5) NULL DEFAULT NULL COMMENT '单价',
  `pk_unit` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '单位',
  `pk_consumption` double(255, 9) NULL DEFAULT NULL COMMENT '用量',
  `pk_currencyType` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '币别',
  `pk_exchangeRate` double(255, 2) NULL DEFAULT NULL COMMENT '汇率',
  `pk_amounts` double(255, 3) NULL DEFAULT NULL COMMENT '总金额',
  `pk_length` double(255, 2) NULL DEFAULT NULL COMMENT '长',
  `pk_width` double(255, 2) NULL DEFAULT NULL COMMENT '宽',
  `pk_height` double(255, 2) NULL DEFAULT NULL COMMENT '高',
  PRIMARY KEY (`packid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '包装材料表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of packinglist
-- ----------------------------
INSERT INTO `packinglist` VALUES (17, 'plastic', NULL, '20241114082603', '胶袋:空白PE平口袋 0.04mm*9.5\"*15\"', 0.11000, 'PCS', NULL, 'CNY', 1.00, NULL, 0.00, 0.00, 0.00);
INSERT INTO `packinglist` VALUES (18, 'labeling', NULL, '20241114082631', '贴标:白底不干胶 50mm*20mm', 0.04000, 'PCS', NULL, 'CNY', 1.00, NULL, 0.00, 0.00, 0.00);
INSERT INTO `packinglist` VALUES (19, 'labeling', NULL, '20241114082650', '贴标:黄底不干胶 150mm*80mm', 0.04000, 'PCS', NULL, 'CNY', 1.00, NULL, 0.00, 0.00, 0.00);
INSERT INTO `packinglist` VALUES (20, 'certificate', NULL, '20241114082717', '合格证:双色70g纸 60mm*80mm', 0.06000, 'PCS', NULL, 'CNY', 1.00, NULL, 0.00, 0.00, 0.00);
INSERT INTO `packinglist` VALUES (21, 'instructions', NULL, '20241114082738', '说明书:黑白70g纸 210mm*297mm', 0.12000, 'PCS', NULL, 'CNY', 1.00, NULL, 0.00, 0.00, 0.00);
INSERT INTO `packinglist` VALUES (22, 'colorbox', NULL, '20241114082757', '彩盒:4C 400g灰卡 140*130*200mm', 1.28000, 'PCS', NULL, 'CNY', 1.00, NULL, 0.00, 0.00, 0.00);
INSERT INTO `packinglist` VALUES (23, 'colorbox', NULL, '20241114082813', '彩盒:4C1专 400g灰卡 140*130*175mm', 1.14000, 'PCS', NULL, 'CNY', 1.00, NULL, 0.00, 0.00, 0.00);
INSERT INTO `packinglist` VALUES (24, 'carton', NULL, '20241114082904', '外箱:黄色A=B 16\"*11.75\"*16.5\"', 12.00000, 'PCS', NULL, 'CNY', 1.00, NULL, 16.00, 11.75, 16.50);
INSERT INTO `packinglist` VALUES (25, 'carton', NULL, '20241114082941', '外箱:黄色A=B 16\"*11.75\"*14.5\"', 4.80000, 'PCS', NULL, 'CNY', 1.00, NULL, 16.00, 11.75, 14.50);
INSERT INTO `packinglist` VALUES (26, 'tape', NULL, '20241114083023', '裝箱胶带(通用)', 2.09000, '卷', NULL, 'CNY', 1.00, NULL, 0.00, 0.00, 0.00);
INSERT INTO `packinglist` VALUES (28, 'listershell', NULL, '20241209143335', '泡壳test', 1.00000, 'PCS', NULL, 'CNY', 1.00, NULL, 0.00, 0.00, 0.00);
INSERT INTO `packinglist` VALUES (29, 'carton', '金点', '20241216144102', '外箱：黄色A=B 16.5“*12.5”*14.5“', 5.10692, 'PCS', NULL, 'CNY', 1.00, NULL, 16.50, 12.50, 14.50);
INSERT INTO `packinglist` VALUES (30, 'plastic', '利丰', '20241216144329', '胶袋：空白透明PE0.04mmx9.5”x12“', 14.50000, 'KG', NULL, 'CNY', 1.00, NULL, 0.00, 0.00, 0.00);

-- ----------------------------
-- Table structure for pk_table
-- ----------------------------
DROP TABLE IF EXISTS `pk_table`;
CREATE TABLE `pk_table`  (
  `pk_id` int NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主外键',
  `pk_number` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '品号唯一',
  `pk_material` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '材料名称',
  `pk_price` double(20, 5) NOT NULL COMMENT '材料单价',
  `pk_unit` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '材料单位',
  `pk_consumption` double(20, 9) NOT NULL COMMENT '基本用量',
  `pk_currencyType` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '币别',
  `pk_exchangeRate` double(255, 2) NOT NULL COMMENT '税率',
  `pk_amounts` double(255, 5) NULL DEFAULT NULL COMMENT '金额',
  `pk_supplier` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '厂商',
  `pk_length` double(255, 2) NULL DEFAULT NULL COMMENT '长度',
  `pk_width` double(255, 2) NULL DEFAULT NULL COMMENT '宽度',
  `pk_height` double(255, 2) NULL DEFAULT NULL COMMENT '高度',
  `pk_itemType` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '具体型号',
  PRIMARY KEY (`pk_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 431 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '主页保存后 运费包装表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of pk_table
-- ----------------------------
INSERT INTO `pk_table` VALUES (49, '20240111151047', '20240108143424', '膠袋 0.04MMX9 1/2\"x15\"', 0.11000, 'pcs', 20.000000000, 'CNY', 1.00, 0.00550, NULL, 0.00, 0.00, 0.00, 'plastic');
INSERT INTO `pk_table` VALUES (50, '20240111151047', '20240108143629', '彩盒 140x130x200mm', 1.28000, 'pcs', 20.000000000, 'CNY', 1.00, 0.06400, NULL, 0.00, 0.00, 0.00, 'colorbox');
INSERT INTO `pk_table` VALUES (51, '20240111151047', '20240108144405', '外箱 16 \" x 11 3/4 \" x 14 1/2 \"', 4.61000, 'pcs', 240.000000000, 'CNY', 1.00, 0.01921, NULL, 16.00, 11.75, 14.50, 'carton');
INSERT INTO `pk_table` VALUES (52, '20240111151047', '20240108144459', '裝箱膠帶-20PCS', 2.09000, '卷', 0.000600000, 'CNY', 1.00, 0.00125, NULL, 0.00, 0.00, 0.00, 'tape');
INSERT INTO `pk_table` VALUES (53, '20240111151358', '20240108143607', '膠袋 0.04MMX9 1/2\"x12\"', 0.09000, 'pcs', 10.000000000, 'CNY', 1.00, 0.00900, NULL, 0.00, 0.00, 0.00, 'plastic');
INSERT INTO `pk_table` VALUES (54, '20240111151358', '20240108143701', '彩盒 140x130x175mm', 1.28000, 'pcs', 10.000000000, 'CNY', 1.00, 0.12800, NULL, 0.00, 0.00, 0.00, 'colorbox');
INSERT INTO `pk_table` VALUES (55, '20240111151358', '20240108144405', '外箱 16 \" x 11 3/4 \" x 14 1/2 \"', 4.61000, 'pcs', 120.000000000, 'CNY', 1.00, 0.03842, NULL, 16.00, 11.75, 14.50, 'carton');
INSERT INTO `pk_table` VALUES (56, '20240111151358', '20240108144440', '裝箱膠帶', 2.09000, '卷', 0.000600000, 'CNY', 1.00, 0.00125, NULL, 0.00, 0.00, 0.00, 'tape');
INSERT INTO `pk_table` VALUES (57, '20240111151547', '20240108143607', '膠袋 0.04MMX9 1/2\"x12\"', 0.09000, 'pcs', 10.000000000, 'CNY', 1.00, 0.00900, NULL, 0.00, 0.00, 0.00, 'plastic');
INSERT INTO `pk_table` VALUES (58, '20240111151547', '20240108143701', '彩盒 140x130x175mm', 1.28000, 'pcs', 10.000000000, 'CNY', 1.00, 0.12800, NULL, 0.00, 0.00, 0.00, 'colorbox');
INSERT INTO `pk_table` VALUES (59, '20240111151547', '20240108144405', '外箱 16 \" x 11 3/4 \" x 14 1/2 \"', 4.61000, 'pcs', 120.000000000, 'CNY', 1.00, 0.03842, NULL, 16.00, 11.75, 14.50, 'carton');
INSERT INTO `pk_table` VALUES (60, '20240111151547', '20240108144440', '裝箱膠帶', 2.09000, '卷', 0.000600000, 'CNY', 1.00, 0.00125, NULL, 0.00, 0.00, 0.00, 'tape');
INSERT INTO `pk_table` VALUES (61, '20240111151745', '20240108143607', '膠袋 0.04MMX9 1/2\"x12\"', 0.09000, 'pcs', 10.000000000, 'CNY', 1.00, 0.00900, NULL, 0.00, 0.00, 0.00, 'plastic');
INSERT INTO `pk_table` VALUES (62, '20240111151745', '20240108143701', '彩盒 140x130x175mm', 1.28000, 'pcs', 10.000000000, 'CNY', 1.00, 0.12800, NULL, 0.00, 0.00, 0.00, 'colorbox');
INSERT INTO `pk_table` VALUES (63, '20240111151745', '20240108144405', '外箱 16 \" x 11 3/4 \" x 14 1/2 \"', 4.61000, 'pcs', 120.000000000, 'CNY', 1.00, 0.03842, NULL, 16.00, 11.75, 14.50, 'carton');
INSERT INTO `pk_table` VALUES (64, '20240111151745', '20240108144440', '裝箱膠帶', 2.09000, '卷', 0.000600000, 'CNY', 1.00, 0.00125, NULL, 0.00, 0.00, 0.00, 'tape');
INSERT INTO `pk_table` VALUES (390, '20241216151155', '20241216144329', '胶袋：空白透明PE0.04mmx9.5”x12“', 14.50000, 'KG', 3.000000000, 'CNY', 1.00, 4.83333, '利丰', 0.00, 0.00, 0.00, 'plastic');
INSERT INTO `pk_table` VALUES (391, '20241216151155', '20241114082738', '说明书:黑白70g纸 210mm*297mm', 0.12000, 'PCS', 3.000000000, 'CNY', 1.00, 0.04000, NULL, 0.00, 0.00, 0.00, 'instructions');
INSERT INTO `pk_table` VALUES (392, '20241216151155', '20241114082717', '合格证:双色70g纸 60mm*80mm', 0.06000, 'PCS', 3.000000000, 'CNY', 1.00, 0.02000, NULL, 0.00, 0.00, 0.00, 'certificate');
INSERT INTO `pk_table` VALUES (393, '20241216151155', '20241209143335', '泡壳test', 1.00000, 'PCS', 3.000000000, 'CNY', 1.00, 0.33333, NULL, 0.00, 0.00, 0.00, 'listershell');
INSERT INTO `pk_table` VALUES (394, '20241216151155', '20241216144102', '外箱：黄色A=B 16.5“*12.5”*14.5“', 5.10692, 'PCS', 18.000000000, 'CNY', 1.00, 0.28372, '金点', 16.50, 12.50, 14.50, 'carton');
INSERT INTO `pk_table` VALUES (395, '20241216151155', '20241114083023', '裝箱胶带(通用)', 2.09000, '卷', 0.004081000, 'CNY', 1.00, 0.00853, NULL, 0.00, 0.00, 0.00, 'tape');
INSERT INTO `pk_table` VALUES (396, '20241216152147', '20241114082603', '胶袋:空白PE平口袋 0.04mm*9.5\"*15\"', 0.11000, 'PCS', 20.000000000, 'CNY', 1.00, 0.00550, NULL, 0.00, 0.00, 0.00, 'plastic');
INSERT INTO `pk_table` VALUES (397, '20241216152147', '20241114082757', '彩盒:4C 400g灰卡 140*130*200mm', 1.28000, 'PCS', 20.000000000, 'CNY', 1.00, 0.06400, NULL, 0.00, 0.00, 0.00, 'colorbox');
INSERT INTO `pk_table` VALUES (398, '20241216152147', '20241216144102', '外箱：黄色A=B 16.5“*12.5”*14.5“', 5.10692, 'PCS', 240.000000000, 'CNY', 1.00, 0.02128, '金点', 16.50, 12.50, 14.50, 'carton');
INSERT INTO `pk_table` VALUES (399, '20241216152147', '20241114083023', '裝箱胶带(通用)', 2.09000, '卷', 0.000306000, 'CNY', 1.00, 0.00064, NULL, 0.00, 0.00, 0.00, 'tape');
INSERT INTO `pk_table` VALUES (413, '20250102090918', '20241216144329', '胶袋：空白透明PE0.04mmx9.5”x12“', 14.50000, 'KG', 5.000000000, 'CNY', 1.00, 2.90000, '利丰', 0.00, 0.00, 0.00, 'plastic');
INSERT INTO `pk_table` VALUES (414, '20250102090918', '20241114082738', '说明书:黑白70g纸 210mm*297mm', 0.12000, 'PCS', 5.000000000, 'CNY', 1.00, 0.02400, NULL, 0.00, 0.00, 0.00, 'instructions');
INSERT INTO `pk_table` VALUES (415, '20250102090918', '20241209143335', '泡壳test', 1.00000, 'PCS', 5.000000000, 'CNY', 1.00, 0.20000, NULL, 0.00, 0.00, 0.00, 'listershell');
INSERT INTO `pk_table` VALUES (416, '20250102090918', '20241114082904', '外箱:黄色A=B 16\"*11.75\"*16.5\"', 12.00000, 'PCS', 60.000000000, 'CNY', 1.00, 0.20000, NULL, 16.00, 11.75, 16.50, 'carton');
INSERT INTO `pk_table` VALUES (417, '20250102090918', '20241114083023', '裝箱胶带(通用)', 2.09000, '卷', 0.001183000, 'CNY', 1.00, 0.00247, NULL, 0.00, 0.00, 0.00, 'tape');
INSERT INTO `pk_table` VALUES (418, '20250102091841', '20241114082603', '胶袋:空白PE平口袋 0.04mm*9.5\"*15\"', 0.11000, 'PCS', 10.000000000, 'CNY', 1.00, 0.01100, NULL, 0.00, 0.00, 0.00, 'plastic');
INSERT INTO `pk_table` VALUES (419, '20250102091841', '20241114082757', '彩盒:4C 400g灰卡 140*130*200mm', 1.28000, 'PCS', 10.000000000, 'CNY', 1.00, 0.12800, NULL, 0.00, 0.00, 0.00, 'colorbox');
INSERT INTO `pk_table` VALUES (420, '20250102091841', '20241216144102', '外箱：黄色A=B 16.5“*12.5”*14.5“', 5.10692, 'PCS', 120.000000000, 'CNY', 1.00, 0.04256, '金点', 16.50, 12.50, 14.50, 'carton');
INSERT INTO `pk_table` VALUES (421, '20250102091841', '20241114083023', '裝箱胶带(通用)', 2.09000, '卷', 0.000612000, 'CNY', 1.00, 0.00128, NULL, 0.00, 0.00, 0.00, 'tape');
INSERT INTO `pk_table` VALUES (422, '20250102094044', '20241114083023', '裝箱胶带(通用)', 2.09000, '卷', 0.001183000, 'CNY', 1.00, 0.00247, NULL, 0.00, 0.00, 0.00, 'tape');
INSERT INTO `pk_table` VALUES (423, '20250102094044', '20241114082904', '外箱:黄色A=B 16\"*11.75\"*16.5\"', 12.00000, 'PCS', 60.000000000, 'CNY', 1.00, 0.20000, NULL, 16.00, 11.75, 16.50, 'carton');
INSERT INTO `pk_table` VALUES (424, '20250102094044', '20241209143335', '泡壳test', 1.00000, 'PCS', 5.000000000, 'CNY', 1.00, 0.20000, NULL, 0.00, 0.00, 0.00, 'listershell');
INSERT INTO `pk_table` VALUES (425, '20250102094044', '20241114082738', '说明书:黑白70g纸 210mm*297mm', 0.12000, 'PCS', 5.000000000, 'CNY', 1.00, 0.02400, NULL, 0.00, 0.00, 0.00, 'instructions');
INSERT INTO `pk_table` VALUES (426, '20250102094044', '20241216144329', '胶袋：空白透明PE0.04mmx9.5”x12“', 14.50000, 'KG', 5.000000000, 'CNY', 1.00, 2.90000, '利丰', 0.00, 0.00, 0.00, 'plastic');
INSERT INTO `pk_table` VALUES (427, '20250102094205', '20241114082603', '胶袋:空白PE平口袋 0.04mm*9.5\"*15\"', 0.11000, 'PCS', 20.000000000, 'CNY', 1.00, 0.00550, NULL, 0.00, 0.00, 0.00, 'plastic');
INSERT INTO `pk_table` VALUES (428, '20250102094205', '20241114082813', '彩盒:4C1专 400g灰卡 140*130*175mm', 1.14000, 'PCS', 20.000000000, 'CNY', 1.00, 0.05700, NULL, 0.00, 0.00, 0.00, 'colorbox');
INSERT INTO `pk_table` VALUES (429, '20250102094205', '20241216144102', '外箱：黄色A=B 16.5“*12.5”*14.5“', 5.10692, 'PCS', 120.000000000, 'CNY', 1.00, 0.04256, '金点', 16.50, 12.50, 14.50, 'carton');
INSERT INTO `pk_table` VALUES (430, '20250102094205', '20241114083023', '裝箱胶带(通用)', 2.09000, '卷', 0.000612000, 'CNY', 1.00, 0.00128, NULL, 0.00, 0.00, 0.00, 'tape');

-- ----------------------------
-- Table structure for record_lc
-- ----------------------------
DROP TABLE IF EXISTS `record_lc`;
CREATE TABLE `record_lc`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '厂商',
  `uuid` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '关联uuid',
  `updatePersonnel` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '修改人员',
  `updateTime` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `lc_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '人工名字',
  `lc_price` double NULL DEFAULT NULL COMMENT '人工单价',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 47 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '修改历史记录表-' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of record_lc
-- ----------------------------
INSERT INTO `record_lc` VALUES (1, '20240108144546', NULL, '2024-01-08 16:33:17', '噴碼', 0.0005);
INSERT INTO `record_lc` VALUES (2, '20240109090950', '续愿', '2024-01-09 09:22:26', '軋針梳棉', 0.0016);
INSERT INTO `record_lc` VALUES (13, '20240109100334', NULL, '2024-01-09 11:01:11', '軋針梳棉', 0.0016);
INSERT INTO `record_lc` VALUES (14, '20240109100334', NULL, '2024-01-09 11:50:14', '軋針梳棉', 0.0016);
INSERT INTO `record_lc` VALUES (45, '20240108144546', NULL, '2024-11-14 09:14:50', '人工统一工价', 0.19488);

-- ----------------------------
-- Table structure for record_pk
-- ----------------------------
DROP TABLE IF EXISTS `record_pk`;
CREATE TABLE `record_pk`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '厂商',
  `uuid` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '关联uuid',
  `updatePersonnel` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '修改人员',
  `updateTime` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `pk_number` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '包装品号',
  `pk_material` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '包装品名',
  `pk_price` double NULL DEFAULT NULL COMMENT '包装单价',
  `pk_unit` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '包装单位',
  `pk_consumption` double NULL DEFAULT NULL COMMENT '包装用量',
  `pk_supplier` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '包装厂商',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 37 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of record_pk
-- ----------------------------
INSERT INTO `record_pk` VALUES (1, '20240109100524', '李龙凯', '2024-01-09 10:05:37', '20240108143701', '彩盒 140x130x175mm', 1.28, 'pcs', 1, NULL);
INSERT INTO `record_pk` VALUES (2, '20240109100524', '李龙凯', '2024-01-09 10:12:55', '20240108143701', '彩盒 140x130x175mm', 1.28, 'pcs', 123, NULL);
INSERT INTO `record_pk` VALUES (3, '20240109100524', '李龙凯', '2024-01-09 10:34:07', '20240108143701', '彩盒 140x130x175mm', 1.28, 'pcs', 123, NULL);
INSERT INTO `record_pk` VALUES (5, '20240109100524', '李龙凯', '2024-01-09 10:52:50', '20240108143701', '彩盒 140x130x175mm', 1.28, 'pcs', 123, NULL);
INSERT INTO `record_pk` VALUES (6, '20240109105414', '李龙凯', '2024-01-09 10:54:23', '20240108143701', '彩盒 140x130x175mm', 1.28, 'pcs', 123, NULL);
INSERT INTO `record_pk` VALUES (7, '20240109105414', '李龙凯', '2024-01-09 10:54:37', '20240108143701', '彩盒 140x130x175mm', 1.28, 'pcs', 123, NULL);
INSERT INTO `record_pk` VALUES (8, '20240109105610', '李龙凯', '2024-01-09 10:56:21', '20240108143701', '彩盒 140x130x175mm', 1.28, 'pcs', 1, NULL);
INSERT INTO `record_pk` VALUES (9, '20240109105610', '李龙凯', '2024-01-09 10:57:19', '20240108143701', '彩盒 140x130x175mm', 1.28, 'pcs', 1, NULL);
INSERT INTO `record_pk` VALUES (10, '20240109105610', 'admin', '2024-01-09 11:50:02', '20240108143629', '彩盒 140x130x200mm', 1.28, 'pcs', 1, NULL);

-- ----------------------------
-- Table structure for record_rm
-- ----------------------------
DROP TABLE IF EXISTS `record_rm`;
CREATE TABLE `record_rm`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '厂商',
  `uuid` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '关联uuid',
  `rm_number` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '品号',
  `rm_material` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '品名',
  `rm_price` double NULL DEFAULT NULL COMMENT '单价',
  `rm_consumption` double NULL DEFAULT NULL COMMENT '用量',
  `rm_unit` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '单位',
  `rm_currencyType` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '币别',
  `rm_supplier` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '厂商',
  `updatePersonnel` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '修改人员',
  `updateTime` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of record_rm
-- ----------------------------
INSERT INTO `record_rm` VALUES (1, '20240109085513', '20240108143229', '油墨', 535, 200000, 'kg', 'CNY', NULL, 'admin', '2024-01-09 09:00:10');
INSERT INTO `record_rm` VALUES (3, '20240109100334', '20240108143214', '閉孔泡棉', 0.034, 1, 'pcs', 'CNY', NULL, NULL, '2024-01-09 10:32:45');
INSERT INTO `record_rm` VALUES (4, '20240109100334', '20240108143214', '閉孔泡棉', 0.034, 1, 'pcs', 'CNY', NULL, NULL, '2024-01-09 10:32:51');
INSERT INTO `record_rm` VALUES (5, '20240109100334', '20240108143214', '閉孔泡棉', 0.034, 1, 'pcs', 'CNY', NULL, NULL, '2024-01-09 10:33:16');
INSERT INTO `record_rm` VALUES (6, '20240109100334', '20240108142707', '鋁鼻夾', 36.5, 1, 'kg', 'CNY', NULL, NULL, '2024-01-09 10:43:54');
INSERT INTO `record_rm` VALUES (7, '20240109100334', '20240108142707', '鋁鼻夾', 36.5, 36.5, 'kg', 'CNY', NULL, NULL, '2024-01-09 10:44:13');
INSERT INTO `record_rm` VALUES (8, '20240109105610', '20240108142848', '白棉 外層 6D', 7.8, 335.56757, 'kg', 'CNY', NULL, '李龙凯', '2024-01-09 10:56:17');
INSERT INTO `record_rm` VALUES (9, '20240109100334', '20240108143214', '閉孔泡棉', 0.034, 1, 'pcs', 'CNY', NULL, NULL, '2024-01-09 11:01:07');
INSERT INTO `record_rm` VALUES (12, '20240111151047', '20240108142848', '白棉 外層 6D', 7.8, 335.56757, 'kg', 'CNY', NULL, '续愿', '2024-01-26 14:18:00');
INSERT INTO `record_rm` VALUES (18, '20240109084034', '20240108143229', '油墨', 535, 200000, 'kg', 'CNY', NULL, NULL, '2024-11-13 10:43:08');
INSERT INTO `record_rm` VALUES (19, '20240108164858', '20240108143229', '油墨', 535, 200000, 'kg', 'CNY', NULL, NULL, '2024-11-13 10:43:38');
INSERT INTO `record_rm` VALUES (20, '20240109100334', '20240108143229', '油墨', 535, 200000, 'kg', 'CNY', NULL, NULL, '2024-11-13 10:43:46');
INSERT INTO `record_rm` VALUES (21, '20240108144546', '20241113102535', '滤材:白色熔喷布38g/m2*18.5cm(俊富)', 1.37, 22, 'm2', 'CNY', NULL, NULL, '2024-11-14 09:18:06');
INSERT INTO `record_rm` VALUES (29, '20241216145426', '20240108143214', '鼻垫:黑色PU 90*12*3mm弯型全闭孔圆角(超联)', 0.034, 1, 'PCS', 'CNY', NULL, NULL, '2024-12-16 15:02:26');

-- ----------------------------
-- Table structure for rm_table
-- ----------------------------
DROP TABLE IF EXISTS `rm_table`;
CREATE TABLE `rm_table`  (
  `rm_id` int NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主外键',
  `rm_number` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '品号唯一',
  `rm_material` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '材料名称',
  `rm_price` double(20, 5) NOT NULL COMMENT '材料单价',
  `rm_unit` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '材料单位',
  `rm_consumption` double(20, 9) NOT NULL COMMENT '基本用量',
  `rm_currencyType` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '币别',
  `rm_exchangeRate` double(255, 2) NOT NULL COMMENT '税率',
  `rm_loss` double(255, 2) NULL DEFAULT NULL COMMENT '损耗',
  `rm_amounts` double(255, 5) NOT NULL COMMENT '金额',
  `rm_prop` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用来区分查询的时候种类',
  `rm_itemType` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '种类',
  `rm_supplier` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '厂商',
  PRIMARY KEY (`rm_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1187 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of rm_table
-- ----------------------------
INSERT INTO `rm_table` VALUES (10, '20240108164858', '20240108142707', '鼻夹:原色铝片0.8mm*5mm弯型(昊森)', 36.00000, 'KG', 950.000000000, 'CNY', 1.00, 0.97, 0.03907, NULL, '鼻夹', '昊森');
INSERT INTO `rm_table` VALUES (11, '20240108164858', '20240108142727', '松紧带:黄色氨纶织带6.8mm扁型(唯宾)', 34.00000, 'KG', 470.000000000, 'CNY', 1.00, 0.97, 0.07458, NULL, '头带', '唯宾');
INSERT INTO `rm_table` VALUES (12, '20240108164858', '20240108142800', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 196.523360000, 'CNY', 1.00, 0.97, 0.04249, NULL, '内层', '日月');
INSERT INTO `rm_table` VALUES (13, '20240108164858', '20240108142832', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 393.636880000, 'CNY', 1.00, 0.97, 0.02514, NULL, '内层', '汇维仕');
INSERT INTO `rm_table` VALUES (14, '20240108164858', '20240108142848', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 335.567570000, 'CNY', 1.00, 0.97, 0.02488, NULL, '外层', '日月');
INSERT INTO `rm_table` VALUES (15, '20240108164858', '20240108142905', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 559.279280000, 'CNY', 1.00, 0.97, 0.01770, NULL, '外层', '汇维仕');
INSERT INTO `rm_table` VALUES (16, '20240108164858', '20240108143025', '滤材:黑色活性碳布135g/m2*18.5cm(森友)', 89.00000, 'KG', 168.589640000, 'CNY', 1.00, 0.97, 0.54424, NULL, '中层', '森友');
INSERT INTO `rm_table` VALUES (17, '20240108164858', '20240108143158', '滤材:白色熔喷布50g/m2*18.5cm(俊富)', 2.05000, '㎡', 22.000000000, 'CNY', 1.00, 0.97, 0.09606, NULL, '中层', '俊富');
INSERT INTO `rm_table` VALUES (18, '20240108164858', '20240108143229', '油墨:黑色53188 PP用(忠和)', 535.00000, 'kg', 200000.000000000, 'CNY', 1.00, 0.97, 0.00276, NULL, '其它', NULL);
INSERT INTO `rm_table` VALUES (19, '20240108164858', '20240108143214', '鼻垫:黑色PU 90*12*3mm弯型全闭孔圆角(超联)', 0.03400, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.03505, NULL, '泡棉', '超联');
INSERT INTO `rm_table` VALUES (20, '20240109084034', '20240108142707', '鼻夹:原色铝片0.8mm*5mm弯型(昊森)', 36.00000, 'KG', 950.000000000, 'CNY', 1.00, 0.97, 0.03907, NULL, '鼻夹', '昊森');
INSERT INTO `rm_table` VALUES (21, '20240109084034', '20240108142727', '松紧带:黄色氨纶织带6.8mm扁型(唯宾)', 34.00000, 'KG', 470.000000000, 'CNY', 1.00, 0.97, 0.07458, NULL, '头带', '唯宾');
INSERT INTO `rm_table` VALUES (22, '20240109084034', '20240108142848', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 196.523360000, 'CNY', 1.00, 0.97, 0.04249, NULL, '外层', '日月');
INSERT INTO `rm_table` VALUES (32, '20240109084034', '20240108142832', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 393.636880000, 'CNY', 1.00, 0.97, 0.02514, NULL, '内层', '汇维仕');
INSERT INTO `rm_table` VALUES (33, '20240109084034', '20240108142848', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 335.567570000, 'CNY', 1.00, 0.97, 0.02488, NULL, '外层', '日月');
INSERT INTO `rm_table` VALUES (34, '20240109084034', '20240108142905', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 559.279280000, 'CNY', 1.00, 0.97, 0.01770, NULL, '外层', '汇维仕');
INSERT INTO `rm_table` VALUES (35, '20240109084034', '20240108142921', '气阀:白色ABS料39.4mm圆型自动(阔润)', 0.18000, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.18557, NULL, '其它', '阔润');
INSERT INTO `rm_table` VALUES (36, '20240109084034', '20240108143002', '气阀片:白色硅胶26.5mm圆型有孔(星成)', 0.08000, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.08247, NULL, '其它', '星成');
INSERT INTO `rm_table` VALUES (37, '20240109084034', '20240108143136', '滤材:白色熔喷布38g/m2*18.5cm(广能)', 3.80000, '㎡', 22.000000000, 'CNY', 1.00, 0.97, 0.17807, NULL, '中层', '广能');
INSERT INTO `rm_table` VALUES (38, '20240109084034', '20240108143214', '鼻垫:黑色PU 90*12*3mm弯型全闭孔圆角(超联)', 0.03400, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.03505, NULL, '泡棉', '超联');
INSERT INTO `rm_table` VALUES (39, '20240109084034', '20240108143229', '油墨:黑色53188 PP用(忠和)', 535.00000, 'kg', 200000.000000000, 'CNY', 1.00, 0.97, 0.00276, NULL, '其它', NULL);
INSERT INTO `rm_table` VALUES (136, '20240109100334', '20240108142707', '鼻夹:原色铝片0.8mm*5mm弯型(昊森)', 36.00000, 'KG', 950.000000000, 'CNY', 1.00, 0.97, 0.03907, NULL, '鼻夹', '昊森');
INSERT INTO `rm_table` VALUES (137, '20240109100334', '20240108142727', '松紧带:黄色氨纶织带6.8mm扁型(唯宾)', 34.00000, 'KG', 470.000000000, 'CNY', 1.00, 0.97, 0.07458, NULL, '头带', '唯宾');
INSERT INTO `rm_table` VALUES (138, '20240109100334', '20240108142800', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 196.523360000, 'CNY', 1.00, 0.97, 0.04249, NULL, '内层', '日月');
INSERT INTO `rm_table` VALUES (139, '20240109100334', '20240108142832', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 393.636880000, 'CNY', 1.00, 0.97, 0.02514, NULL, '内层', '汇维仕');
INSERT INTO `rm_table` VALUES (140, '20240109100334', '20240108142848', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 335.567570000, 'CNY', 1.00, 0.97, 0.02488, NULL, '外层', '日月');
INSERT INTO `rm_table` VALUES (141, '20240109100334', '20240108142921', '气阀:白色ABS料39.4mm圆型自动(阔润)', 0.18000, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.18557, NULL, '其它', '阔润');
INSERT INTO `rm_table` VALUES (142, '20240109100334', '20240108143002', '气阀片:白色硅胶26.5mm圆型有孔(星成)', 0.08000, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.08247, NULL, '其它', '星成');
INSERT INTO `rm_table` VALUES (143, '20240109100334', '20240108143025', '滤材:黑色活性碳布135g/m2*18.5cm(森友)', 89.00000, 'KG', 168.589640000, 'CNY', 1.00, 0.97, 0.54424, NULL, '中层', '森友');
INSERT INTO `rm_table` VALUES (144, '20240109100334', '20240108143136', '滤材:白色熔喷布38g/m2*18.5cm(广能)', 3.80000, '㎡', 22.000000000, 'CNY', 1.00, 0.97, 0.17807, NULL, '中层', '广能');
INSERT INTO `rm_table` VALUES (145, '20240109100334', '20240108143214', '鼻垫:黑色PU 90*12*3mm弯型全闭孔圆角(超联)', 0.03400, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.03505, NULL, '泡棉', '超联');
INSERT INTO `rm_table` VALUES (146, '20240109100334', '20240108143229', '油墨:黑色53188 PP用(忠和)', 535.00000, 'kg', 200000.000000000, 'CNY', 1.00, 0.97, 0.00276, NULL, '其它', NULL);
INSERT INTO `rm_table` VALUES (187, '20240111151047', '20240108142848', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 335.567570000, 'CNY', 1.00, 0.97, 0.02488, 'out', '外层', '日月');
INSERT INTO `rm_table` VALUES (188, '20240111151047', '20240108142905', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 559.279280000, 'CNY', 1.00, 0.97, 0.01770, 'out', '外层', '汇维仕');
INSERT INTO `rm_table` VALUES (189, '20240111151047', '20240108142727', '松紧带:黄色氨纶织带6.8mm扁型(唯宾)', 34.00000, 'KG', 470.000000000, 'CNY', 1.00, 0.97, 0.07458, 'binding', '头带', '唯宾');
INSERT INTO `rm_table` VALUES (190, '20240111151047', '20240108142707', '鼻夹:原色铝片0.8mm*5mm弯型(昊森)', 36.00000, 'KG', 950.000000000, 'CNY', 1.00, 0.97, 0.03907, 'nasal', '鼻夹', '昊森');
INSERT INTO `rm_table` VALUES (191, '20240111151047', '20240108143214', '鼻垫:黑色PU 90*12*3mm弯型全闭孔圆角(超联)', 0.03400, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.03505, 'foam', '泡棉', '超联');
INSERT INTO `rm_table` VALUES (192, '20240111151047', '20240108143136', '滤材:白色熔喷布38g/m2*18.5cm(广能)', 3.80000, '㎡', 22.000000000, 'CNY', 1.00, 0.97, 0.17807, 'mid', '中层', '广能');
INSERT INTO `rm_table` VALUES (193, '20240111151047', '20240108143229', '油墨', 535.00000, 'kg', 200000.000000000, 'CNY', 1.00, 0.97, 0.00276, 'others', '其它', NULL);
INSERT INTO `rm_table` VALUES (194, '20240111151047', '20240108142800', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 196.523360000, 'CNY', 1.00, 0.97, 0.04249, 'in', '内层', '日月');
INSERT INTO `rm_table` VALUES (195, '20240111151047', '20240108142832', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 393.636880000, 'CNY', 1.00, 0.97, 0.02514, 'in', '内层', '汇维仕');
INSERT INTO `rm_table` VALUES (196, '20240111151358', '20240108142848', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 196.523360000, 'CNY', 1.00, 0.97, 0.04249, 'out', '外层', '日月');
INSERT INTO `rm_table` VALUES (197, '20240111151358', '20240108142848', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 335.567570000, 'CNY', 1.00, 0.97, 0.02488, 'out', '外层', '日月');
INSERT INTO `rm_table` VALUES (198, '20240111151358', '20240108142905', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 559.279280000, 'CNY', 1.00, 0.97, 0.01770, 'out', '外层', '汇维仕');
INSERT INTO `rm_table` VALUES (199, '20240111151358', '20240108142727', '松紧带:黄色氨纶织带6.8mm扁型(唯宾)', 34.00000, 'KG', 470.000000000, 'CNY', 1.00, 0.97, 0.07458, 'binding', '头带', '唯宾');
INSERT INTO `rm_table` VALUES (200, '20240111151358', '20240108142707', '鼻夹:原色铝片0.8mm*5mm弯型(昊森)', 36.00000, 'KG', 950.000000000, 'CNY', 1.00, 0.97, 0.03907, 'nasal', '鼻夹', '昊森');
INSERT INTO `rm_table` VALUES (201, '20240111151358', '20240108143214', '鼻垫:黑色PU 90*12*3mm弯型全闭孔圆角(超联)', 0.03400, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.03505, 'foam', '泡棉', '超联');
INSERT INTO `rm_table` VALUES (202, '20240111151358', '20240108143136', '滤材:白色熔喷布38g/m2*18.5cm(广能)', 3.80000, '㎡', 22.000000000, 'CNY', 1.00, 0.97, 0.17807, 'mid', '中层', '广能');
INSERT INTO `rm_table` VALUES (203, '20240111151358', '20240108142921', '气阀:白色ABS料39.4mm圆型自动(阔润)', 0.18000, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.18557, 'others', '其它', '阔润');
INSERT INTO `rm_table` VALUES (204, '20240111151358', '20240108143002', '气阀片:白色硅胶26.5mm圆型有孔(星成)', 0.08000, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.08247, 'others', '其它', '星成');
INSERT INTO `rm_table` VALUES (205, '20240111151358', '20240108143229', '油墨', 535.00000, 'kg', 200000.000000000, 'CNY', 1.00, 0.97, 0.00276, 'others', '其它', NULL);
INSERT INTO `rm_table` VALUES (206, '20240111151358', '20240108142832', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 393.636880000, 'CNY', 1.00, 0.97, 0.02514, 'in', '内层', '汇维仕');
INSERT INTO `rm_table` VALUES (207, '20240111151547', '20240108142848', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 335.567570000, 'CNY', 1.00, 0.97, 0.02488, 'out', '外层', '日月');
INSERT INTO `rm_table` VALUES (208, '20240111151547', '20240108142905', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 559.279280000, 'CNY', 1.00, 0.97, 0.01770, 'out', '外层', '汇维仕');
INSERT INTO `rm_table` VALUES (209, '20240111151547', '20240108142727', '松紧带:黄色氨纶织带6.8mm扁型(唯宾)', 34.00000, 'KG', 470.000000000, 'CNY', 1.00, 0.97, 0.07458, 'binding', '头带', '唯宾');
INSERT INTO `rm_table` VALUES (210, '20240111151547', '20240108142707', '鼻夹:原色铝片0.8mm*5mm弯型(昊森)', 36.00000, 'KG', 950.000000000, 'CNY', 1.00, 0.97, 0.03907, 'nasal', '鼻夹', '昊森');
INSERT INTO `rm_table` VALUES (211, '20240111151547', '20240108143214', '鼻垫:黑色PU 90*12*3mm弯型全闭孔圆角(超联)', 0.03400, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.03505, 'foam', '泡棉', '超联');
INSERT INTO `rm_table` VALUES (212, '20240111151547', '20240108143025', '滤材:黑色活性碳布135g/m2*18.5cm(森友)', 89.00000, 'KG', 168.589640000, 'CNY', 1.00, 0.97, 0.54424, 'mid', '中层', '森友');
INSERT INTO `rm_table` VALUES (213, '20240111151547', '20240108143158', '滤材:白色熔喷布50g/m2*18.5cm(俊富)', 2.05000, '㎡', 22.000000000, 'CNY', 1.00, 0.97, 0.09606, 'mid', '中层', '俊富');
INSERT INTO `rm_table` VALUES (214, '20240111151547', '20240108143229', '油墨', 535.00000, 'kg', 200000.000000000, 'CNY', 1.00, 0.97, 0.00276, 'others', '其它', NULL);
INSERT INTO `rm_table` VALUES (215, '20240111151547', '20240108142832', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 393.636880000, 'CNY', 1.00, 0.97, 0.02514, 'in', '内层', '汇维仕');
INSERT INTO `rm_table` VALUES (216, '20240111151547', '20240108142800', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 196.523360000, 'CNY', 1.00, 0.97, 0.04249, 'in', '内层', '日月');
INSERT INTO `rm_table` VALUES (217, '20240111151745', '20240108142848', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 335.567570000, 'CNY', 1.00, 0.97, 0.02488, 'out', '外层', '日月');
INSERT INTO `rm_table` VALUES (218, '20240111151745', '20240108142727', '松紧带:黄色氨纶织带6.8mm扁型(唯宾)', 34.00000, 'KG', 470.000000000, 'CNY', 1.00, 0.97, 0.07458, 'binding', '头带', '唯宾');
INSERT INTO `rm_table` VALUES (219, '20240111151745', '20240108142707', '鼻夹:原色铝片0.8mm*5mm弯型(昊森)', 36.00000, 'KG', 950.000000000, 'CNY', 1.00, 0.97, 0.03907, 'nasal', '鼻夹', '昊森');
INSERT INTO `rm_table` VALUES (220, '20240111151745', '20240108143214', '鼻垫:黑色PU 90*12*3mm弯型全闭孔圆角(超联)', 0.03400, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.03505, 'foam', '泡棉', '超联');
INSERT INTO `rm_table` VALUES (221, '20240111151745', '20240108143025', '滤材:黑色活性碳布135g/m2*18.5cm(森友)', 89.00000, 'KG', 168.589640000, 'CNY', 1.00, 0.97, 0.54424, 'mid', '中层', '森友');
INSERT INTO `rm_table` VALUES (222, '20240111151745', '20240108143136', '滤材:白色熔喷布38g/m2*18.5cm(广能)', 3.80000, '㎡', 22.000000000, 'CNY', 1.00, 0.97, 0.17807, 'mid', '中层', '广能');
INSERT INTO `rm_table` VALUES (223, '20240111151745', '20240108142921', '气阀:白色ABS料39.4mm圆型自动(阔润)', 0.18000, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.18557, 'others', '其它', '阔润');
INSERT INTO `rm_table` VALUES (224, '20240111151745', '20240108143002', '气阀片:白色硅胶26.5mm圆型有孔(星成)', 0.08000, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.08247, 'others', '其它', '星成');
INSERT INTO `rm_table` VALUES (225, '20240111151745', '20240108143229', '油墨', 535.00000, 'kg', 200000.000000000, 'CNY', 1.00, 0.97, 0.00276, 'others', '其它', NULL);
INSERT INTO `rm_table` VALUES (226, '20240111151745', '20240108142800', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 196.523360000, 'CNY', 1.00, 0.97, 0.04249, 'in', '内层', '日月');
INSERT INTO `rm_table` VALUES (227, '20240111151745', '20240108142832', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 393.636880000, 'CNY', 1.00, 0.97, 0.02514, 'in', '内层', '汇维仕');
INSERT INTO `rm_table` VALUES (1042, '20241216151155', '20240108142905', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 559.279280000, 'CNY', 1.00, 0.97, 0.01770, 'out', '外层', '汇维仕');
INSERT INTO `rm_table` VALUES (1043, '20241216151155', '20240108142848', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 335.567570000, 'CNY', 1.00, 0.97, 0.02488, 'out', '外层', '日月');
INSERT INTO `rm_table` VALUES (1044, '20241216151155', '20240108142727', '松紧带:黄色氨纶织带6.8mm扁型(唯宾)', 34.00000, 'KG', 470.000000000, 'CNY', 1.00, 0.97, 0.07458, 'binding', '头带', '唯宾');
INSERT INTO `rm_table` VALUES (1045, '20241216151155', '20240108142707', '鼻夹:原色铝片0.8mm*5mm弯型(昊森)', 36.00000, 'KG', 950.000000000, 'CNY', 1.00, 0.97, 0.03907, 'nasal', '鼻夹', '昊森');
INSERT INTO `rm_table` VALUES (1046, '20241216151155', '20240108143214', '鼻垫:黑色PU 90*12*3mm弯型全闭孔圆角(超联)', 0.03400, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.03505, 'foam', '泡棉', '超联');
INSERT INTO `rm_table` VALUES (1047, '20241216151155', '20240108143136', '滤材:白色熔喷布38g/m2*18.5cm(广能)', 3.80000, '㎡', 22.000000000, 'CNY', 1.00, 0.97, 0.17807, 'mid', '中层', '广能');
INSERT INTO `rm_table` VALUES (1048, '20241216151155', '20240108143229', '油墨', 535.00000, 'kg', 200000.000000000, 'CNY', 1.00, 0.97, 0.00276, 'others', '其它', NULL);
INSERT INTO `rm_table` VALUES (1049, '20241216151155', '20240108142832', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 393.636880000, 'CNY', 1.00, 0.97, 0.02514, 'in', '内层', '汇维仕');
INSERT INTO `rm_table` VALUES (1050, '20241216151155', '20240108142800', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 196.523360000, 'CNY', 1.00, 0.97, 0.04249, 'in', '内层', '日月');
INSERT INTO `rm_table` VALUES (1051, '20241216152147', '20240108142727', '松紧带:黄色氨纶织带6.8mm扁型(唯宾)', 34.00000, 'KG', 850.000000000, 'CNY', 1.00, 0.97, 0.04124, 'binding', '头带', NULL);
INSERT INTO `rm_table` VALUES (1052, '20241216152147', '20240108143214', '鼻垫:黑色PU 90*12*3mm弯型全闭孔圆角(超联)', 0.03400, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.03505, 'foam', '泡棉', NULL);
INSERT INTO `rm_table` VALUES (1053, '20241216152147', '20241216141738', '滤材：黑色活性炭90g/m2*18.5cm(森友)', 63.00000, 'KG', 163.000000000, 'CNY', 1.00, 0.97, 0.39846, 'mid', '中层', NULL);
INSERT INTO `rm_table` VALUES (1054, '20241216152147', '20241113102535', '滤材:白色熔喷布38g/m2*18.5cm(俊富)', 1.37000, 'KG', 262.000000000, 'CNY', 1.00, 0.97, 0.00539, 'mid', '中层', NULL);
INSERT INTO `rm_table` VALUES (1055, '20241216152147', '20241113102328', '油墨:黑色53188 PP用(忠和)', 395.00000, 'KG', 20000.000000000, 'CNY', 1.00, 0.97, 0.02036, 'others', '其它', NULL);
INSERT INTO `rm_table` VALUES (1145, '20250102090918', '20240108142848', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 335.567570000, 'CNY', 1.00, 0.97, 0.02488, 'out', '外层', '日月');
INSERT INTO `rm_table` VALUES (1146, '20250102090918', '20240108142905', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 559.279280000, 'CNY', 1.00, 0.97, 0.01770, 'out', '外层', '汇维仕');
INSERT INTO `rm_table` VALUES (1147, '20250102090918', '20240108142727', '松紧带:黄色氨纶织带6.8mm扁型(唯宾)', 34.00000, 'KG', 470.000000000, 'CNY', 1.00, 0.97, 0.07458, 'binding', '头带', '唯宾');
INSERT INTO `rm_table` VALUES (1148, '20250102090918', '20240108142707', '鼻夹:原色铝片0.8mm*5mm弯型(昊森)', 36.00000, 'KG', 950.000000000, 'CNY', 1.00, 0.97, 0.03907, 'nasal', '鼻夹', '昊森');
INSERT INTO `rm_table` VALUES (1149, '20250102090918', '20240108143214', '鼻垫:黑色PU 90*12*3mm弯型全闭孔圆角(超联)', 0.03400, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.03505, 'foam', '泡棉', '超联');
INSERT INTO `rm_table` VALUES (1150, '20250102090918', '20240108143025', '滤材:黑色活性碳布135g/m2*18.5cm(森友)', 89.00000, 'KG', 168.589640000, 'CNY', 1.00, 0.97, 0.54424, 'mid', '中层', '森友');
INSERT INTO `rm_table` VALUES (1151, '20250102090918', '20240108143158', '滤材:白色熔喷布50g/m2*18.5cm(俊富)', 2.05000, '㎡', 22.000000000, 'CNY', 1.00, 0.97, 0.09606, 'mid', '中层', '俊富');
INSERT INTO `rm_table` VALUES (1152, '20250102090918', '20240108143229', '油墨:黑色53188 PP用(忠和)', 535.00000, 'kg', 200000.000000000, 'CNY', 1.00, 0.97, 0.00276, 'others', '其它', NULL);
INSERT INTO `rm_table` VALUES (1153, '20250102090918', '20240108142800', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 196.523360000, 'CNY', 1.00, 0.97, 0.04249, 'in', '内层', '日月');
INSERT INTO `rm_table` VALUES (1154, '20250102090918', '20240108142832', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 393.636880000, 'CNY', 1.00, 0.97, 0.02514, 'in', '内层', '汇维仕');
INSERT INTO `rm_table` VALUES (1155, '20250102091841', '20240108142848', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 196.523360000, 'CNY', 1.00, 0.97, 0.04249, 'out', '外层', '日月');
INSERT INTO `rm_table` VALUES (1156, '20250102091841', '20240108142848', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 335.567570000, 'CNY', 1.00, 0.97, 0.02488, 'out', '外层', '日月');
INSERT INTO `rm_table` VALUES (1157, '20250102091841', '20240108142905', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 559.279280000, 'CNY', 1.00, 0.97, 0.01770, 'out', '外层', '汇维仕');
INSERT INTO `rm_table` VALUES (1158, '20250102091841', '20240108142727', '松紧带:黄色氨纶织带6.8mm扁型(唯宾)', 34.00000, 'KG', 470.000000000, 'CNY', 1.00, 0.97, 0.07458, 'binding', '头带', '唯宾');
INSERT INTO `rm_table` VALUES (1159, '20250102091841', '20240108142707', '鼻夹:原色铝片0.8mm*5mm弯型(昊森)', 36.00000, 'KG', 950.000000000, 'CNY', 1.00, 0.97, 0.03907, 'nasal', '鼻夹', '昊森');
INSERT INTO `rm_table` VALUES (1160, '20250102091841', '20240108143214', '鼻垫:黑色PU 90*12*3mm弯型全闭孔圆角(超联)', 0.03400, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.03505, 'foam', '泡棉', '超联');
INSERT INTO `rm_table` VALUES (1161, '20250102091841', '20240108143136', '滤材:白色熔喷布38g/m2*18.5cm(广能)', 3.80000, '㎡', 22.000000000, 'CNY', 1.00, 0.97, 0.17807, 'mid', '中层', '广能');
INSERT INTO `rm_table` VALUES (1162, '20250102091841', '20240108142921', '气阀:白色ABS料39.4mm圆型自动(阔润)', 0.18000, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.18557, 'others', '其它', '阔润');
INSERT INTO `rm_table` VALUES (1163, '20250102091841', '20240108143002', '气阀片:白色硅胶26.5mm圆型有孔(星成)', 0.08000, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.08247, 'others', '其它', '星成');
INSERT INTO `rm_table` VALUES (1164, '20250102091841', '20240108143229', '油墨:黑色53188 PP用(忠和)', 535.00000, 'kg', 200000.000000000, 'CNY', 1.00, 0.97, 0.00276, 'others', '其它', NULL);
INSERT INTO `rm_table` VALUES (1165, '20250102091841', '20240108142832', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 393.636880000, 'CNY', 1.00, 0.97, 0.02514, 'in', '内层', '汇维仕');
INSERT INTO `rm_table` VALUES (1166, '20250102094044', '20240108142905', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 559.279280000, 'CNY', 1.00, 0.97, 0.01770, 'out', '外层', '汇维仕');
INSERT INTO `rm_table` VALUES (1167, '20250102094044', '20240108142848', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 335.567570000, 'CNY', 1.00, 0.97, 0.02488, 'out', '外层', '日月');
INSERT INTO `rm_table` VALUES (1168, '20250102094044', '20240108142727', '松紧带:黄色氨纶织带6.8mm扁型(唯宾)', 34.00000, 'KG', 470.000000000, 'CNY', 1.00, 0.97, 0.07458, 'binding', '头带', '唯宾');
INSERT INTO `rm_table` VALUES (1169, '20250102094044', '20240108142707', '鼻夹:原色铝片0.8mm*5mm弯型(昊森)', 36.00000, 'KG', 950.000000000, 'CNY', 1.00, 0.97, 0.03907, 'nasal', '鼻夹', '昊森');
INSERT INTO `rm_table` VALUES (1170, '20250102094044', '20240108143214', '鼻垫:黑色PU 90*12*3mm弯型全闭孔圆角(超联)', 0.03400, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.03505, 'foam', '泡棉', '超联');
INSERT INTO `rm_table` VALUES (1171, '20250102094044', '20240108143158', '滤材:白色熔喷布50g/m2*18.5cm(俊富)', 2.05000, '㎡', 22.000000000, 'CNY', 1.00, 0.97, 0.09606, 'mid', '中层', '俊富');
INSERT INTO `rm_table` VALUES (1172, '20250102094044', '20240108143025', '滤材:黑色活性碳布135g/m2*18.5cm(森友)', 89.00000, 'KG', 168.589640000, 'CNY', 1.00, 0.97, 0.54424, 'mid', '中层', '森友');
INSERT INTO `rm_table` VALUES (1173, '20250102094044', '20240108143229', '油墨:黑色53188 PP用(忠和)', 535.00000, 'kg', 200000.000000000, 'CNY', 1.00, 0.97, 0.00276, 'others', '其它', NULL);
INSERT INTO `rm_table` VALUES (1174, '20250102094044', '20240108142832', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 393.636880000, 'CNY', 1.00, 0.97, 0.02514, 'in', '内层', '汇维仕');
INSERT INTO `rm_table` VALUES (1175, '20250102094044', '20240108142800', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 196.523360000, 'CNY', 1.00, 0.97, 0.04249, 'in', '内层', '日月');
INSERT INTO `rm_table` VALUES (1176, '20250102094205', '20240108142905', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 559.279280000, 'CNY', 1.00, 0.97, 0.01770, 'out', '外层', '汇维仕');
INSERT INTO `rm_table` VALUES (1177, '20250102094205', '20240108142848', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 335.567570000, 'CNY', 1.00, 0.97, 0.02488, 'out', '外层', '日月');
INSERT INTO `rm_table` VALUES (1178, '20250102094205', '20240108142848', '化纤棉:白色聚酯棉6D*64mm(日月)', 8.10000, 'KG', 196.523360000, 'CNY', 1.00, 0.97, 0.04249, 'out', '外层', '日月');
INSERT INTO `rm_table` VALUES (1179, '20250102094205', '20240108142727', '松紧带:黄色氨纶织带6.8mm扁型(唯宾)', 34.00000, 'KG', 470.000000000, 'CNY', 1.00, 0.97, 0.07458, 'binding', '头带', '唯宾');
INSERT INTO `rm_table` VALUES (1180, '20250102094205', '20240108142707', '鼻夹:原色铝片0.8mm*5mm弯型(昊森)', 36.00000, 'KG', 950.000000000, 'CNY', 1.00, 0.97, 0.03907, 'nasal', '鼻夹', '昊森');
INSERT INTO `rm_table` VALUES (1181, '20250102094205', '20240108143214', '鼻垫:黑色PU 90*12*3mm弯型全闭孔圆角(超联)', 0.03400, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.03505, 'foam', '泡棉', '超联');
INSERT INTO `rm_table` VALUES (1182, '20250102094205', '20240108143136', '滤材:白色熔喷布38g/m2*18.5cm(广能)', 3.80000, '㎡', 22.000000000, 'CNY', 1.00, 0.97, 0.17807, 'mid', '中层', '广能');
INSERT INTO `rm_table` VALUES (1183, '20250102094205', '20240108143229', '油墨:黑色53188 PP用(忠和)', 535.00000, 'kg', 200000.000000000, 'CNY', 1.00, 0.97, 0.00276, 'others', '其它', NULL);
INSERT INTO `rm_table` VALUES (1184, '20250102094205', '20240108143002', '气阀片:白色硅胶26.5mm圆型有孔(星成)', 0.08000, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.08247, 'others', '其它', '星成');
INSERT INTO `rm_table` VALUES (1185, '20250102094205', '20240108142921', '气阀:白色ABS料39.4mm圆型自动(阔润)', 0.18000, 'PCS', 1.000000000, 'CNY', 1.00, 0.97, 0.18557, 'others', '其它', '阔润');
INSERT INTO `rm_table` VALUES (1186, '20250102094205', '20240108142832', '化纤棉:白色热熔棉4D*51mm(汇维仕)', 9.60000, 'KG', 393.636880000, 'CNY', 1.00, 0.97, 0.02514, 'in', '内层', '汇维仕');

-- ----------------------------
-- Table structure for succession
-- ----------------------------
DROP TABLE IF EXISTS `succession`;
CREATE TABLE `succession`  (
  `sc_id` int NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'uuid,唯一索引',
  `sc_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '系列',
  `sc_user` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `sc_date` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `sc_discripte` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '系列描述',
  `sc_standard` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '系列',
  `sc_type` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '型号',
  `lowest_offer` double(255, 2) NULL DEFAULT NULL COMMENT '最低报价，警示',
  PRIMARY KEY (`sc_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of succession
-- ----------------------------
INSERT INTO `succession` VALUES (2, '20240108164858', '9500-N95系列', 'admin', '2024-11-14 08:33:33', '标准规格', '美规', '9500-N95OV', 0.00);
INSERT INTO `succession` VALUES (3, '20240109084034', '9500-N95系列', '续愿', '2024-12-23 15:17:54', '标准规格', '美规', '9500V-N95', 0.00);
INSERT INTO `succession` VALUES (4, '20240109100334', '9500-N95系列', 'admin', '2024-11-14 08:34:26', '标准规格', '美规', '9500VOV-N95', 0.00);

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `userid` int NOT NULL AUTO_INCREMENT,
  `userName` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `power` int NOT NULL COMMENT '权限0或者1,0是有权限1是没有权限',
  `registrationdate` datetime NOT NULL,
  `company` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '所属公司 台北 迅安 知腾 ',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  PRIMARY KEY (`userid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES (1, '续愿', '123', 1, '2023-05-06 15:00:33', 'C2知腾', '2025-01-02 08:37:14');
INSERT INTO `user` VALUES (2, '李龙凯', '123', 1, '2023-05-12 09:33:30', 'C2知腾', '2025-01-02 09:05:46');
INSERT INTO `user` VALUES (3, 'Cheryl', '123', 7, '2023-06-02 09:07:13', 'C1台北', NULL);
INSERT INTO `user` VALUES (4, '王桃飞', '123', 2, '2023-06-02 13:52:15', 'C2知腾', NULL);
INSERT INTO `user` VALUES (6, '周德坤', '123', 6, '2024-01-19 08:22:37', 'C5迅安', NULL);
INSERT INTO `user` VALUES (8, 'Penny', '0905', 7, '2024-01-19 08:25:01', 'C1台北', '2024-01-25 17:02:59');
INSERT INTO `user` VALUES (9, 'Anna', '123', 7, '2024-01-19 08:25:31', 'C4上海', '2024-03-27 11:00:09');
INSERT INTO `user` VALUES (11, 'Agnes', '123', 7, '2024-01-19 08:26:08', 'C1台北', '2024-01-26 10:54:36');
INSERT INTO `user` VALUES (12, 'Linda', '123', 7, '2024-01-19 08:26:08', 'C1台北', '2024-01-26 10:02:02');
INSERT INTO `user` VALUES (13, 'Jenny', '123', 7, '2024-01-19 08:26:08', 'C1台北', NULL);
INSERT INTO `user` VALUES (14, 'Sammy', '123', 7, '2024-01-19 08:26:08', 'C1台北', '2024-01-26 07:51:43');
INSERT INTO `user` VALUES (15, 'Tina', '123', 7, '2024-01-19 08:26:08', 'C1台北', '2024-01-26 10:36:25');
INSERT INTO `user` VALUES (16, 'Jerry', '123', 7, '2024-01-19 08:26:08', 'C1台北', '2024-01-25 11:43:31');
INSERT INTO `user` VALUES (17, 'Kevin', '123', 7, '2024-01-19 08:26:08', 'C1台北', NULL);
INSERT INTO `user` VALUES (18, 'Sunny', '123411990', 7, '2024-01-19 08:26:08', 'C4上海', '2024-01-26 10:21:58');
INSERT INTO `user` VALUES (19, 'Kay', '123', 7, '2024-01-19 08:26:08', 'C4上海', '2024-01-26 14:03:46');
INSERT INTO `user` VALUES (20, 'Winky', '123', 7, '2024-01-19 08:26:08', 'C4上海', '2024-01-26 08:54:56');
INSERT INTO `user` VALUES (21, 'Jim', 'qazwsx123', 7, '2024-01-19 08:26:08', 'C4上海', NULL);
INSERT INTO `user` VALUES (22, 'Heidi', '123', 7, '2024-01-19 08:26:08', 'C5迅安', '2024-02-01 17:25:14');
INSERT INTO `user` VALUES (23, '余换新', '123', 7, '2024-01-19 08:26:08', 'C5迅安', NULL);
INSERT INTO `user` VALUES (24, 'admin', '123', 8, '2024-01-19 14:01:59', 'C2知腾', '2024-12-17 14:02:49');
INSERT INTO `user` VALUES (25, '业务员test', '123', 7, '2024-01-19 14:15:53', 'C2知腾', '2024-12-17 11:57:13');
INSERT INTO `user` VALUES (26, 'Ken', '123', 1, '2024-05-16 09:18:35', 'C1台北', '2024-09-12 11:04:37');
INSERT INTO `user` VALUES (27, '物料员test', '123', 2, '2024-12-16 14:05:51', 'C2知腾', '2024-12-16 15:24:28');

SET FOREIGN_KEY_CHECKS = 1;
