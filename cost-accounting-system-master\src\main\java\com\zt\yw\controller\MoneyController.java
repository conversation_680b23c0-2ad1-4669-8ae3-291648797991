package com.zt.yw.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zt.yw.config.HttpClientUtil2;
import com.zt.yw.dao.MoneyMapper;
import com.zt.yw.entity.ApiResponse;
import com.zt.yw.entity.MoneyApi;
import com.zt.yw.entity.R;
import com.zt.yw.service.MoneyService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@RequestMapping(value = "/Money")
@RestController
public class MoneyController {



    @Resource
    private MoneyMapper moneyMapper;
    @Resource
    private MoneyService moneyService;

    /**
     * 查询全部汇率
     *
     * @小布学长
     * @return
     */
    @Scheduled(fixedRate = 15 * 24 * 60 * 60 * 1000L)  //每15天更新自动执行一次
    @SneakyThrows
    @GetMapping("/moneyapi")
    public R<String> moneyapi() {
        String apiResponse = HttpClientUtil2.doGet("https://www.mxnzp.com/api/exchange_rate/list?app_id=kzkqp0tbtrskokhp&app_secret=cE9tS2hhTjgxM0ZDODdSVkdjR2RxUT09");
        // 使用ObjectMapper将JSON字符串转换为Java对象
        ObjectMapper objectMapper = new ObjectMapper();
        ApiResponse apiResponseObj = null;
        try {
            apiResponseObj = objectMapper.readValue(apiResponse, ApiResponse.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            return R.fail("解析API响应失败");
        }

        // 现在你可以访问apiResponseObj中的数据
        List<ApiResponse.CurrencyRate> rates = apiResponseObj.getData();
        if (CollectionUtils.isEmpty(rates)) {
            return R.ok("更新成功");
        }

        List<MoneyApi> records = moneyService.selectMoney(null);
        records.forEach(System.out::println);

        Map<String, MoneyApi> moneyMap = records.stream()
                .collect(Collectors.toMap(MoneyApi::getCurrency, money -> money,
                        (existing, duplicate) -> existing));
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 处理rates的数据与数据库里面的币种配对
        rates.forEach(rate -> {
            MoneyApi moneyData = moneyMap.get(rate.getFrom());
            if (null != moneyData) {
                moneyData.setName(rate.getNameDesc().substring(0, rate.getNameDesc().indexOf("/")));
                moneyData.setCurrency(rate.getFrom());
                moneyData.setExchangerate(rate.getPrice());
                moneyData.setModifyuser("系统自动更新");
                moneyData.setMtime(format.format(new Date()));
                System.out.println("moneyApi----------------: " + moneyData);
                moneyService.updateMoney(moneyData);
            }
        });
        return R.ok("汇率自动更新成功");
    }


    //进入税率页面即查询
    @RequestMapping(value = "/money",method = RequestMethod.GET)
    public String money(Model model, HttpServletRequest request){
        MoneyApi moneyApi=moneyMapper.selectById(1);
        model.addAttribute("money",moneyApi);
    //    String a="我是最新汇率";

        String s = HttpClientUtil2.doGet("https://www.mxnzp.com/api/exchange_rate/aim?from=USD&to=CNY&type=1&app_id=kzkqp0tbtrskokhp&app_secret=cE9tS2hhTjgxM0ZDODdSVkdjR2RxUT09");
        //使用JSONObject进行json解析，拿到值
        JSONObject jsonObject= JSONObject.parseObject(s);
        JSONObject data = jsonObject.getJSONObject("data");
        String a = data.getString("nameDesc");
        String b = data.getString("price");
        String c = data.getString("updateTime");


        model.addAttribute("aaa",a);
        model.addAttribute("bbb",b);
        model.addAttribute("ccc",c);
        return "text";

    }

}
