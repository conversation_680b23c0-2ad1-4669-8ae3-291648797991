@import "mixins/mixins";
@import "common/var";

@include b(empty) {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  text-align: center;
  box-sizing: border-box;
  padding: $--empty-padding;

  @include e(image) {
    width: $--empty-image-width;

    img {
      user-select: none;
      width: 100%;
      height: 100%;
      vertical-align: top;
      object-fit: contain;
    }

    svg {
      fill: $--svg-monochrome-grey;
      width: 100%;
      height: 100%;
      vertical-align: top;
    }
  }

  @include e(description) {
    margin-top: $--empty-description-margin-top;

    p {
      margin: 0;
      font-size: $--font-size-base;
      color: $--color-text-secondary;
    }
  }

  @include e(bottom) {
    margin-top: $--empty-bottom-margin-top;
  }
}
