import axios from 'axios'
import { Message } from 'element-ui'
import { isJSON, cookies } from './utils'

const baseURL = ''

axios.defaults.timeout = 50000

axios.defaults.headers['Content-Type'] = 'application/json;charset=UTF-8'

axios.interceptors.response.use((response) => {
  if (response.config.originalData) {
    return response.data
  }
  if (!response.data || !response.data.data || !response.data.code) {
    return isJSON(response.data.data) ? JSON.parse(response) : response
  }
  if (response.status !== 200) {
    Message({
      type: 'error',
      showClose: true,
      message: '操作失败：' + response.data.message
    })
    return Promise.reject(response.data.data)
  }
  return isJSON(response.data.data) ? Promise.resolve(JSON.parse(response.data.data)) : Promise.resolve(response.data.data)
}, (error) => {
  console.log('error', error)
  if (error.response.status === 404 || 500 || 501) {
    Message({
      type: 'error',
      showClose: true,
      offset: 90,
      message: '请求失败：' + error.message
    })
  }
  if (error.response.status === 401) {
    cookies.delCookie('Authorization')
    location.reload()
  }
  return Promise.reject(error)
})

export default function ajax (options) {
  const ajaxObj = {
    method: options.method,
    baseURL: baseURL,
    url: options.url,
    originalData: options.originalData || ''
  }
  const Authorization = cookies.getCookie('Authorization')
  // const Authorization = null
  if (Authorization) {
    ajaxObj.headers = {
      'Authorization': Authorization
    }
  }
  if (options.method === 'GET') {
    ajaxObj.parmas = options.data
  } else {
    ajaxObj.data = options.data
  }
  return axios(ajaxObj)
}
