package com.zt.yw.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zt.yw.dao.AdminLcMapper;
import com.zt.yw.dao.MainMapper;
import com.zt.yw.dao.RmMapper;
import com.zt.yw.entity.AdminLc;
import com.zt.yw.entity.Goodslist;
import com.zt.yw.entity.Maintable;
import com.zt.yw.entity.RmTable;
import com.zt.yw.service.AdminLcService;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service
public class AdminLcServiceImpl implements AdminLcService {
    //工角第一层新增
    @Resource
    private AdminLcMapper adminLcMapper;
    @Resource
    private RmMapper rmMapper;
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public boolean savaadmin_lc(AdminLc adminLc) {
        Date now1 = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        adminLc.setUuid(Integer.valueOf((format.format(now1))));
        adminLcMapper.insert(adminLc);
        return true;
    }

    //根据uuid关联查询
    @Override
    public List selectRmType(RmTable rmTable) {
        QueryWrapper<RmTable> queryWrapper = new QueryWrapper<>();
       queryWrapper.eq("uuid",rmTable.getUuid()).like("rm_itemType",rmTable.getRm_itemType());
        return rmMapper.selectList(queryWrapper);

    }
}
