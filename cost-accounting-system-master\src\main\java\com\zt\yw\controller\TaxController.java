package com.zt.yw.controller;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zt.yw.dao.TaxMapper;
import com.zt.yw.entity.Tax;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import java.io.Serializable;
import java.util.*;


@Controller
public class TaxController {

    @Resource
    private TaxMapper taxMapper;
    private Tax tax;

    @RequestMapping(value = "/login",method = RequestMethod.GET)
    public String login(Model model, HttpServletRequest request){

        String name = request.getParameter("name");
        List<Tax> list = null;
        if (StringUtils.hasText(name)) {
            list = taxMapper.selectList(Wrappers.<Tax>lambdaQuery()
                    .like(Tax::getName, name));
        }else {
            list = taxMapper.selectList(null);
        }

         model.addAttribute("aaa",list);

        List<Tax> list1=taxMapper.selectList(null);
        model.addAttribute("bbb",list1);

        Tax tax= taxMapper.selectById(2L);
        System.out.println("tax = " + tax);

        return "login";
    }
    //进入视图页面
    @RequestMapping("/text")
    public String text(){
        return "text" ;
    }
    //接收页面请求的Json数据
    @RequestMapping("/testJson")
    public List<Map<String, Object>> testJson(@RequestBody Tax tax,Model model) {
        //打印接收的JSON格式数据
        System.out.println("name = " + tax.getName() + " password = " + tax.getPassword() + " ghid = " + tax.getGhid());

        int list1=taxMapper.insert(tax);
        Tax tax1= taxMapper.selectById(tax.getUserid());

        model.addAttribute("hhh",tax1);
        System.out.println(tax1);

        List<Map<String, Object>> list = new ArrayList<>() ;
        Map<String, Object> map = new HashMap<>() ;
        map.put("name", "王国栋") ;
        map.put("password", "123") ;
        map.put("ghdi", 2515) ;
        Map<String, Object> map1 = new HashMap<>() ;
        map1.put("name", "唐乃乔") ;
        map1.put("password", "456") ;
        map1.put("ghid", 2565) ;
        list.add(map) ;
        list.add(map1) ;
        return list ;
    }

}
