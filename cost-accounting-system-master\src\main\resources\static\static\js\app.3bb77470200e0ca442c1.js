webpackJsonp([3],{"4Uwr":function(e,t,a){e.exports=a.p+"static/img/logo.1da21b0.jpg"},NHnr:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a("7+uW"),o={render:function(){var e=this.$createElement,t=this._self._c||e;return t("div",{attrs:{id:"app"}},[t("router-view")],1)},staticRenderFns:[]};var l=a("VU/8")({name:"App"},o,!1,function(e){a("siHd")},null,null).exports,i=a("/ocq"),n=a("Dd8w"),s=a.n(n),c=a("//Fk"),p=a.n(c),u=a("mtWM"),m=a.n(u),d=a("zL8q"),h=a.n(d),_=a("fZjL"),f=a.n(_),b={setAuthorization:function(e){b.setCookie("Authorization",e)},getAuthorization:function(){return b.getCookie("Authorization")},deleteAuthorization:function(){b.delCookie("Authorization")},setUserName:function(e){b.setCookie("userName",e)},getUserName:function(){return b.getCookie("userName")},setCookie:function(e,t){var a=new Date;a.setTime(a.getTime()+432e5),document.cookie=e+"="+escape(t)+";expires="+a.toGMTString()},getCookie:function(e){var t=new RegExp("(^| )"+e+"=([^;]*)(;|$)"),a=document.cookie.match(t);return a?unescape(a[2]):null},delCookie:function(e){var t=new Date;t.setTime(t.getTime()-1);var a=b.getCookie(e);null!=a&&(document.cookie=e+"="+a+";expires="+t.toGMTString())}},g=function(e){if("string"==typeof e)try{return JSON.parse(e),!0}catch(e){return!1}},v=function(e,t){if(!e)return"";var a=new Date(e);/YYYY/.test(t)&&(t=t.replace(/YYYY/,a.getFullYear()));var r=a.getMonth()+1;if(/MM/.test(t)){var o=r<10?"0"+r:r;t=t.replace(/MM/,o)}else/M/.test(t)&&(t=t.replace(/M/,r));var l=a.getDate();if(/DD/.test(t)){var i=l<10?"0"+l:l;t=t.replace(/DD/,i)}else/D/.test(t)&&(t=t.replace(/D/,l));var n=a.getHours();if(/HH/.test(t)){var s=n<10?"0"+n:n;t=t.replace(/HH/,s)}else if(/H/.test(t))t=t.replace(/H/,n);else if(/hh/.test(t)){var c=n>12?n-12:n,p=c<10?"0"+c:c;t=t.replace(/hh/,p)}else if(/h/.test(t)){var u=n>12?n-12:n;t=t.replace(/h/,u)}var m=a.getMinutes();if(/mm/.test(t)){var d=m<10?"0"+m:m;t=t.replace(/mm/,d)}else/m/.test(t)&&(t=t.replace(/m/,m));var h=a.getSeconds();if(/ss/.test(t)){var _=h<10?"0"+h:h;t=t.replace(/ss/,_)}else/s/.test(t)&&(t=t.replace(/s/,h));return t},y="";function k(e){var t={method:e.method,baseURL:y,url:e.url,originalData:e.originalData||""},a=b.getCookie("Authorization");return a&&(t.headers={Authorization:a}),"GET"===e.method?t.parmas=e.data:t.data=e.data,m()(t)}m.a.defaults.timeout=5e4,m.a.defaults.headers["Content-Type"]="application/json;charset=UTF-8",m.a.interceptors.response.use(function(e){return e.config.originalData?e.data:e.data&&e.data.data&&e.data.code?200!==e.status?(Object(d.Message)({type:"error",showClose:!0,message:"操作失败："+e.data.message}),p.a.reject(e.data.data)):g(e.data.data)?p.a.resolve(JSON.parse(e.data.data)):p.a.resolve(e.data.data):g(e.data.data)?JSON.parse(e):e},function(e){return console.log("error",e),e.response.status,Object(d.Message)({type:"error",showClose:!0,offset:90,message:"请求失败："+e.message}),401===e.response.status&&(b.delCookie("Authorization"),location.reload()),p.a.reject(e)});var D=a("NYxO"),w={name:"layout",data:function(){var e=this;return{drawer:!1,userName:b.getCookie("userName"),power:b.getCookie("power"),handleModal:!1,pwdForm:{newPassword:"",checkPassword:""},rules:{newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"}],checkPassword:[{required:!0,message:"不能为空",trigger:"blur"},{validator:function(t,a,r){a===e.pwdForm.newPassword?r():r(new Error("密码不一致"))},trigger:"blur"}]}}},computed:s()({},Object(D.c)(["historiesList","POWER_DIC"])),methods:{handleChangePWD:function(e){var t=this;this.$refs[e].validate(function(e){if(!e)return!1;k({method:"POST",url:"/api/updateUser",data:{userid:b.getCookie("userid"),password:t.pwdForm.newPassword}}).then(function(e){t.handleModal=!1,t.$message({offset:80,message:"密码修改成功，请重新登录！",type:"success"}),setTimeout(function(){t.loginOut()},2e3)})})},handleModalClose:function(){this.$refs.pwdForm.resetFields()},loginOut:function(){b.delCookie("userName"),b.delCookie("userid"),b.delCookie("power"),this.$router.replace({path:"/login"})}}},x={render:function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"layout"},[r("el-container",{staticStyle:{height:"100%"}},[r("el-header",[r("div",{staticClass:"layout-title"},[r("img",{staticClass:"layout-logo",attrs:{src:a("4Uwr")}}),e._v(" "),r("div",{staticClass:"layout-name"},[e._v("成本核算系统")])]),e._v(" "),r("div",{staticClass:"layout-nav"},[r("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.loginOut}},[e._v("登出")])],1),e._v(" "),r("div",{staticClass:"layout-user"},[r("span",{staticClass:"user-name"},[e._v("欢迎您！ "+e._s(e.userName)+"("+e._s(e.POWER_DIC[e.power])+")")]),e._v(" "),r("span",{staticClass:"change-pwd",on:{click:function(t){e.handleModal=!0}}},[e._v("修改密码")])])]),e._v(" "),r("el-container",[r("el-aside",{attrs:{width:"201px"}},[r("el-menu",{attrs:{mode:"vertical","default-active":e.$route.path,router:""}},["1"===e.power||"7"===e.power||"8"===e.power?r("el-menu-item",{staticStyle:{"padding-left":"0px"},attrs:{index:"/product"}},[r("i",{staticClass:"el-icon-collection"}),e._v(" "),r("span",{attrs:{slot:"title"},slot:"title"},[e._v("主品配置")])]):e._e(),e._v(" "),"1"===e.power||"8"===e.power?r("el-menu-item",{staticStyle:{"padding-left":"0px"},attrs:{index:"/succession"}},[r("i",{staticClass:"el-icon-menu"}),e._v(" "),r("span",{attrs:{slot:"title"},slot:"title"},[e._v("系列配置")])]):e._e(),e._v(" "),"1"===e.power||"2"===e.power||"7"===e.power||"8"===e.power?r("el-menu-item",{staticStyle:{"padding-left":"0px"},attrs:{index:"/material"}},[r("i",{staticClass:"el-icon-box"}),e._v(" "),r("span",{attrs:{slot:"title"},slot:"title"},[e._v("物料配置")])]):e._e(),e._v(" "),"1"===e.power||"6"===e.power||"8"===e.power?r("el-menu-item",{staticStyle:{"padding-left":"0px"},attrs:{index:"/laborCost"}},[r("i",{staticClass:"el-icon-timer"}),e._v(" "),r("span",{attrs:{slot:"title"},slot:"title"},[e._v("工角配置")])]):e._e(),e._v(" "),"1"===e.power||"2"===e.power||"7"===e.power||"8"===e.power?r("el-menu-item",{staticStyle:{"padding-left":"0px"},attrs:{index:"/package"}},[r("i",{staticClass:"el-icon-receiving"}),e._v(" "),r("span",{attrs:{slot:"title"},slot:"title"},[e._v("包装配置")])]):e._e(),e._v(" "),"1"===e.power?r("el-menu-item",{staticStyle:{"padding-left":"0px"},attrs:{index:"/user"}},[r("i",{staticClass:"el-icon-user"}),e._v(" "),r("span",{attrs:{slot:"title"},slot:"title"},[e._v("用户管理")])]):e._e(),e._v(" "),"1"===e.power||"8"===e.power||"7"===e.power?r("el-menu-item",{staticStyle:{"padding-left":"0px"},attrs:{index:"/rates"}},[r("i",{staticClass:"el-icon-money"}),e._v(" "),r("span",{attrs:{slot:"title"},slot:"title"},[e._v("汇率维护")])]):e._e()],1),e._v(" "),r("div",{staticStyle:{position:"fixed",bottom:"0",left:"4px","text-align":"left"}},[r("p",{staticStyle:{color:"#bbb",margin:"0"}},[e._v("当前版本："),r("el-button",{attrs:{type:"text"},on:{click:function(t){e.drawer=!0}}},[e._v("20250721-025")])],1),e._v(" "),r("p",{staticStyle:{color:"#bbb","font-size":"13px",margin:"0 0 12px 0"}},[e._v("公测版本，如有BUG请及时反馈")])])],1),e._v(" "),r("el-main",{style:{padding:"0 0 20px 20px",overflow:"hidden"}},[r("el-main",{staticClass:"view-wrap"},[r("transition",{attrs:{name:"el-fade-in"}},[r("keep-alive",[r("router-view")],1)],1)],1)],1)],1),e._v(" "),r("el-drawer",{attrs:{title:"更新记录",visible:e.drawer,direction:"ltr"},on:{"update:visible":function(t){e.drawer=t}}},[r("el-timeline",{staticStyle:{"text-align":"left"}},e._l(e.historiesList,function(t,a){return r("el-timeline-item",{key:a,attrs:{placement:"top",type:t.type,color:t.color,size:t.size,timestamp:t.timestamp}},e._l(t.content,function(t,a){return r("p",{key:a},[e._v(e._s(t))])}),0)}),1)],1),e._v(" "),r("el-dialog",{attrs:{title:"修改密码",visible:e.handleModal,width:"35%"},on:{"update:visible":function(t){e.handleModal=t},close:e.handleModalClose}},[r("el-form",{ref:"pwdForm",attrs:{model:e.pwdForm,rules:e.rules,size:"mini"}},[r("el-form-item",{attrs:{label:"新密码",prop:"newPassword"}},[r("el-input",{attrs:{autocomplete:"off"},model:{value:e.pwdForm.newPassword,callback:function(t){e.$set(e.pwdForm,"newPassword",t)},expression:"pwdForm.newPassword"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"确认密码",prop:"checkPassword"}},[r("el-input",{attrs:{autocomplete:"off"},model:{value:e.pwdForm.checkPassword,callback:function(t){e.$set(e.pwdForm,"checkPassword",t)},expression:"pwdForm.checkPassword"}})],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{size:"mini"},on:{click:function(t){e.handleModal=!1}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.handleChangePWD("pwdForm")}}},[e._v("确 定")])],1)],1)],1)],1)},staticRenderFns:[]};var F=a("VU/8")(w,x,!1,function(e){a("jpBW")},"data-v-48a9b7f8",null).exports,C=a("lHA8"),T=a.n(C),S=a("c/Tr"),R=a.n(S),O=a("Xxa5"),P=a.n(O),M=a("exGp"),$=a.n(M),N=a("mvHQ"),z=a.n(N),L=a("eMjc"),I=a.n(L),j=a("3h9r"),q=Object(r.ref)(),H={name:"Index",data:function(){return{LCFormData:{},ManualExchangeRate:{},FilterPackageArr:[],costa:0,rm_selectData:0,pk_selectData:0,fr_boolean:!1,isIncludePK:!1,inCluedPKdata:[],dynamicSpan:5,main_ids:[],standard_ids:[],selsection_ids:[],contrastData:[],rawContrastData:[],packageType:"",cuft:0,CTN_20INCHI:0,CTN_40INCHI:0,CBM_ALL:0,alertSTR:"",standardsDrawer:!1,drawer:!1,rm_btn:!1,lc_btn:!1,pk_btn:!1,loading:!1,stepIndex:1,isDomesticSales:"",saleRate:13,db_saleRate:13,total_sum:0,rp_total:0,rm_sum:0,lc_sum:0,pk_sum:0,fr_sum:0,lc_percentage:156,marketing:"",db_marketing:20,tt_currencyType:"",db_tt_currencyType:"USDP",successionList:[],typeList:[],totalRate:1,db_totalRate:6.9,tableHeight:0,currentNum:1,pageSize:50,totalNum:0,sizesList:["20尺货柜","40尺货柜"],power:b.getCookie("power"),user:b.getCookie("userName"),standardTableList:[],tableList:[],queryProductName:"",queryProductNo:"",productConnection:"",creator:"",handleMutiplelModal:!1,handleAddModal:!1,handleEditModal:!1,handleDetailModal:!1,copyObjData:{postForm:{},rawMaterialTableData:[],LaborCostTableData:[],packageTableData:[],freightTableData:[{}]},handleCopyModal:!1,productFormData:{productStandard:"",productSuccession:"",productType:""},copyProductFormData:{},pkFormData:{},itemDataForm:{out:[],mid:[],in:[],nasal:[],binding:[],foam:[],others:[]},laborCostList:[],laborCostFilterList:[],packageFormData:[],package_options:[],freightFormData:{sales_type:"",factory:"",port:"",terms:"",boxSize:[]},copyFreightFormData:{factory:"",port:"",terms:"",boxSize:[]},copyPackageFormData:[],out_options:[],mid_options:[],in_options:[],nasal_options:[],binding_options:[],foam_options:[],others_options:[],price_options:[],editTitle:"",ttTableData:[],historyModal:!1,activeNames:["rm","lc","pk","fr","tt"],detailObjData:{postForm:{},rawMaterialTableData:[],LaborCostTableData:[],packageTableData:[],freightTableData:[{}]},historyTableData:{rawMaterialTableData:[],LaborCostTableData:[],packageTableData:[]},PKLCTableFormData:{lc_name:"包装统一工角"},copyPKLCTableFormData:{lc_name:"包装统一工角"},copyLCTableFormData:{},rules:{pk_number:[{required:!0,message:"请填写品号",trigger:"blur"}],pk_material:[{required:!0,message:"请填写品名",trigger:"blur"}],pk_itemType:[{required:!0,message:"请选择种类",trigger:"change"}],pk_length:[{required:!0,message:"请填写",trigger:"blur"}],pk_width:[{required:!0,message:"请填写",trigger:"blur"}],pk_height:[{required:!0,message:"请填写",trigger:"blur"}],pk_price:[{required:!0,type:"number",message:"请填写单价",trigger:"blur"}],pk_unit:[{required:!0,message:"请填写单位",trigger:"blur"}],pk_currencyType:[{required:!0,message:"请选择币种",trigger:"change"}]},cpLcRules:{lc_name:[{required:!0,message:"请选择",trigger:"change"}],lc_factory:[{required:!0,message:"请选择",trigger:"change"}],lc_price:[{required:!0,type:"number",message:"请填写单价",trigger:"blur"}]},pklcRules:{lc_price:[{required:!0,type:"number",message:"请填写单价",trigger:"blur"}],lc_consumption:[{required:!0,type:"number",message:"请填写用量",trigger:"blur"}],lc_name:[{required:!0,message:"请填写名称",trigger:"change"}]},rmRules:{rm_exchangeRate:[{required:!0,type:"number",message:"请填写汇率",trigger:"blur"}],rm_price:[{required:!0,type:"number",message:"请填写单价",trigger:"blur"}],rm_consumption:[{required:!0,type:"number",message:"请填写用量",trigger:"blur"}],rm_loss:[{required:!0,type:"number",message:"请填写损耗",trigger:"blur"}],rm_material:[{required:!0,message:"请填写名称",trigger:"change"}],rm_unit:[{required:!0,message:"请选择单位",trigger:"change"}],rm_currencyType:[{required:!0,message:"请选择币种",trigger:"change"}]},lcFormRules:{lcData:[{required:!0,message:"请选择工角",trigger:"change"}]},lcRules:{lc_unit:[{required:!0,message:"请选择单位",trigger:"change"}],lc_exchangeRate:[{required:!0,type:"number",message:"请填写汇率",trigger:"blur"}],lc_price:[{required:!0,type:"number",message:"请填写单价",trigger:"blur"}],lc_consumption:[{required:!0,type:"number",message:"请填写用量",trigger:"blur"}],lc_name:[{required:!0,message:"请填写名称",trigger:"change"}]},pkRules:{pk_exchangeRate:[{required:!0,type:"number",message:"请填写汇率",trigger:"blur"}],pk_price:[{required:!0,type:"number",message:"请填写单价",trigger:"blur"}],pk_consumption:[{required:!0,type:"number",message:"请填写用量",trigger:"blur"}],pk_material:[{required:!0,message:"请填写包装名称",trigger:"change"}],pk_unit:[{required:!0,message:"请选择单位",trigger:"change"}],pk_currencyType:[{required:!0,message:"请选择币种",trigger:"change"}]},pr_rules:{productName:[{required:!0,message:"请填写产品名称",trigger:"blur"}],productConnection:[{required:!0,message:"请填写产品客户信息",trigger:"blur"}],productFactory:[{required:!0,message:"请选择生产加工工厂",trigger:"blur"}]},ma_rules:{productStandard:[{required:!0,message:"请选择标准",trigger:"change"}],productSuccession:[{required:!0,message:"请选择系列",trigger:"change"}],productType:[{required:!0,message:"请选择型号",trigger:"change"}]},pk_rules:{pcsCount:[{required:!0,type:"number",message:"请填写",trigger:"blur"}],bagCount:[{required:!0,message:"请填写",trigger:"blur"}],boxCount:[{required:!0,message:"请填写",trigger:"blur"}]},fr_rules:{sales_type:[{required:!0,message:"请选择",trigger:"change"}],goodsCount:[{required:!0,type:"number",message:"请选填写",trigger:"blur"}],factory:[{required:!0,message:"请选择",trigger:"change"}],port:[{required:!0,message:"请选择",trigger:"change"}],terms:[{required:!0,message:"请选择",trigger:"change"}]},pkTreeData:[],filterText:"",defaultProps:{children:"children",label:"pk_material"}}},computed:s()({},Object(D.c)(["FXRates","ct_options","CURRENCY_DIC","itemType_options","ITEM_DIC","standard_options","pk_itemType_options","unit_options"])),mounted:function(){var e=this;this.$nextTick(function(){e.tableHeight=window.innerHeight-136});var t=this;window.onresize=function(){t.resizeFlag&&clearTimeout(t.resizeFlag),t.resizeFlag=setTimeout(function(){t.getTableHeight(),t.resizeFlag=null},100)},k({method:"POST",url:"/api/selectMoney"}).then(function(t){var a={};t.forEach(function(e){a[e.currency]=e.exchangerate}),e.ManualExchangeRate=s()({},a)})},watch:{filterText:function(e){this.$refs.tree.filter(e)}},activated:function(){this.seachAll(),this.getLaborCostList()},methods:s()({},Object(D.b)(["getFXRates"]),{onTermsChange:function(e){"Ex Works"===e&&(this.freightFormData.expense=0)},createFilter:function(e){return function(t){return 0===t.value.toLowerCase().indexOf(e.toLowerCase())}},handleDomesticSalesChange:function(e){e?(this.tt_currencyType="CNY",this.totalRate=1):(this.tt_currencyType="USDP",this.totalRate=6.9)},handleDBDomesticSalesChange:function(e){e?(this.db_tt_currencyType="CNY",this.db_totalRate=1):(this.db_tt_currencyType="USDP",this.db_totalRate=6.9),this.contrastCalculation()},onTypeChange:function(e){this.packageType=e,this.packageFormData=[],this.copyPackageFormData=[],this.copyProductFormData.pcsCount=1,this.copyProductFormData.bagCount=1,this.copyProductFormData.boxCount=1,this.productFormData.pcsCount=1,this.productFormData.bagCount=1,this.productFormData.boxCount=1,this.$refs.tree.setCheckedKeys([])},handleCourseSelection:function(e){if(this.main_ids=e.map(function(e){return e.admin_id}),this.selsection_ids=this.main_ids.concat(this.standard_ids),this.selsection_ids.length>4){this.$message.warning("最多只能选择4条数据！");var t=e.pop();this.$refs.multipleTable.toggleRowSelection(t,!1)}},handleStandardsSelection:function(e){if(this.standard_ids=e.map(function(e){return e.admin_id}),this.selsection_ids=this.standard_ids.concat(this.main_ids),this.selsection_ids.length>4){this.$message.warning("最多只能选择4条数据！");var t=e.pop();this.$refs.multipleStandardsTable.toggleRowSelection(t,!1)}},handleMutiple:function(){var e=this;if(this.selsection_ids.length<=1)this.$message.warning("请至少选择2条数据，才能对比~");else{switch(this.selsection_ids.length){case 2:this.dynamicSpan=10;break;case 3:this.dynamicSpan=7;break;case 4:this.dynamicSpan=5}k({method:"POST",url:"/api/ByIdlist",data:{admin_id:this.selsection_ids}}).then(function(t){if(e.selsection_ids.length){for(var a=0;a<e.selsection_ids.length;a++)e.rawContrastData.push(e.sortContrastData(t[a],a));e.contrastData=JSON.parse(z()(e.rawContrastData)),e.contrastCalculation(),e.handleMutiplelModal=!0}})}},sortContrastData:function(e,t){var a={styleText:""};a.productName=e.postForm.productName+"("+e.postForm.productFactory.substring(0,2)+"-"+e.postForm.productFactory.substring(3,5)+")",a.packageMsg="每袋"+e.postForm.pcsCount+"个，每盒"+e.postForm.bagCount+"袋，每箱"+e.postForm.boxCount+"盒",a.fr_sum=e.freightTableData[0].fr_price;var r=0;e.LaborCostTableData.forEach(function(e){r+=e.lc_amounts}),a.lc_sum=Number((156*r/100).toFixed(5));var o=0;e.rawMaterialTableData.forEach(function(e){o+=e.rm_amounts}),a.rm_sum=Number(o.toFixed(5));var l=0;switch(e.packageTableData.forEach(function(e){l+=e.pk_amounts}),a.pk_sum=Number(l.toFixed(5)),a.cost=Number((a.fr_sum+a.lc_sum+a.rm_sum+a.pk_sum).toFixed(5)),a.total=a.cost,t){case 0:a.styleText="one";break;case 1:a.styleText="two";break;case 2:a.styleText="three";break;case 3:a.styleText="four"}return a},handleEditModalClose:function(){this.rm_btn=!1,this.pk_btn=!1,this.lc_btn=!1},handleMutiplelModalClose:function(){this.handleMutiplelModal=!1,this.db_tt_currencyType="USDP",this.db_totalRate=6.9,this.db_marketing=20,this.db_saleRate=13,this.rawContrastData=[],this.contrastData=[],this.selsection_ids=[],this.$refs.multipleTable.clearSelection(),this.$refs.multipleStandardsTable.clearSelection()},handleDbCTchange:function(e){this.db_tt_currencyType=e,this.db_totalRate=this.ManualExchangeRate[e],this.contrastCalculation()},handleDbRateChange:function(e){this.db_totalRate=e,this.contrastCalculation()},handleDBSaleRateChange:function(e){this.db_saleRate=e,this.contrastCalculation()},handleDBMarketChange:function(e){this.db_marketing=e,this.contrastCalculation()},contrastCalculation:function(){var e=this;this.contrastData.forEach(function(t,a){var r=/内销/.test(t.productName);t.fr_sum="100JPY"===e.db_tt_currencyType?+(e.rawContrastData[a].fr_sum/e.db_totalRate*100).toFixed(5):+(e.rawContrastData[a].fr_sum/e.db_totalRate).toFixed(5),t.lc_sum="100JPY"===e.db_tt_currencyType?+(e.rawContrastData[a].lc_sum/e.db_totalRate*100).toFixed(5):+(e.rawContrastData[a].lc_sum/e.db_totalRate).toFixed(5),t.rm_sum="100JPY"===e.db_tt_currencyType?+(e.rawContrastData[a].rm_sum/e.db_totalRate*100).toFixed(5):+(e.rawContrastData[a].rm_sum/e.db_totalRate).toFixed(5),t.pk_sum="100JPY"===e.db_tt_currencyType?+(e.rawContrastData[a].pk_sum/e.db_totalRate*100).toFixed(5):+(e.rawContrastData[a].pk_sum/e.db_totalRate).toFixed(5);var o=r?+parseFloat(e.rawContrastData[a].cost/((100-e.db_marketing)/100)*(e.db_saleRate/100+1)).toFixed(5):+parseFloat(e.rawContrastData[a].cost/((100-e.db_marketing)/100)*1.003).toFixed(5);t.cost="100JPY"===e.db_tt_currencyType?+parseFloat(e.rawContrastData[a].cost/e.db_totalRate*100).toFixed(5):+parseFloat(e.rawContrastData[a].cost/e.db_totalRate).toFixed(5),t.total="100JPY"===e.db_tt_currencyType?+parseFloat(o/e.db_totalRate*100).toFixed(5):+parseFloat(o/e.db_totalRate).toFixed(5)})},handleNextStep:function(){var e=this;if(1===this.stepIndex)return this.$refs.productForm.validate(function(t){t&&(e.stepIndex=2)}),!1;if(2===this.stepIndex)return this.$refs.materialForm.validate(function(t){t&&(e.itemDataForm.out.length||e.$confirm('当前没有选择"外层"用料，请问是否继续?',"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.stepIndex=3}).catch(function(){}),e.itemDataForm.mid.length||e.$confirm('当前没有选择"中层"用料，请问是否继续?',"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.stepIndex=3}).catch(function(){}),e.itemDataForm.in.length||e.$confirm('当前没有选择"内层"用料，请问是否继续?',"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.stepIndex=3}).catch(function(){}),e.itemDataForm.nasal.length||e.$confirm('当前没有选择"鼻夹"用料，请问是否继续?',"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.stepIndex=3}).catch(function(){}),e.itemDataForm.binding.length||e.$confirm('当前没有选择"松紧带"用料，请问是否继续?',"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.stepIndex=3}).catch(function(){}),e.itemDataForm.foam.length||e.$confirm('当前没有选择"鼻垫"用料，请问是否继续?',"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.stepIndex=3}).catch(function(){}),e.itemDataForm.others.length||e.$confirm('当前没有选择"其它"用料，请问是否继续?',"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.stepIndex=3}).catch(function(){}),e.itemDataForm.out.length&&e.itemDataForm.mid.length&&e.itemDataForm.in.length&&e.itemDataForm.nasal.length&&e.itemDataForm.binding.length&&e.itemDataForm.foam.length&&e.itemDataForm.others.length&&(e.stepIndex=3))}),!1;if(3===this.stepIndex){var t=new p.a(function(t,a){e.$refs.packageForm.validate(function(e){e?t():a(new Error("错误！"))})});p.a.all([t]).then(function(){return e.packageFormData.every(function(e){return e.pk_consumption})?e.stepIndex=4:e.$message.error("请填写用量！"),!1})}if(4===this.stepIndex){var a=new p.a(function(t,a){e.$refs.LCForm.validate(function(e){e?t():a(new Error("错误！"))})}),r=new p.a(function(t,a){e.$refs.PKLCTableForm.validate(function(e){e?t():a(new Error("错误！"))})});p.a.all([a,r]).then(function(){return e.stepIndex=5,!1})}},getPackageList:function(){var e=this,t=new FormData;t.append("current",1),t.append("size",1e4),t.append("pk_material",""),t.append("pk_number",""),k({method:"POST",url:"/api/PackAll",data:t}).then(function(t){var a=JSON.parse(z()(t.records));e.pkTreeData=[{packid:"a",pk_material:"胶袋",children:a.filter(function(e){return"plastic"===e.pk_itemType})},{packid:"b",pk_material:"彩盒",children:a.filter(function(e){return"colorbox"===e.pk_itemType})},{packid:"c",pk_material:"彩/吊卡",children:a.filter(function(e){return"card"===e.pk_itemType})},{packid:"d",pk_material:"说明书",children:a.filter(function(e){return"instructions"===e.pk_itemType})},{packid:"e",pk_material:"合格证",children:a.filter(function(e){return"certificate"===e.pk_itemType})},{packid:"f",pk_material:"泡壳",children:a.filter(function(e){return"listershell"===e.pk_itemType})},{packid:"g",pk_material:"外箱",children:a.filter(function(e){return"carton"===e.pk_itemType})},{packid:"h",pk_material:"封箱胶带",children:a.filter(function(e){return"tape"===e.pk_itemType})},{packid:"i",pk_material:"内箱",children:a.filter(function(e){return"innerBox"===e.pk_itemType})},{packid:"j",pk_material:"纸盖/纸板",children:a.filter(function(e){return"paperboard"===e.pk_itemType})},{packid:"k",pk_material:"纸栈板",children:a.filter(function(e){return"paperpallet"===e.pk_itemType})},{packid:"l",pk_material:"卷料",children:a.filter(function(e){return"coil"===e.pk_itemType})},{packid:"m",pk_material:"白纸衬",children:a.filter(function(e){return"paperlining"===e.pk_itemType})},{packid:"n",pk_material:"绑封口",children:a.filter(function(e){return"seal"===e.pk_itemType})},{packid:"o",pk_material:"贴标",children:a.filter(function(e){return"labeling"===e.pk_itemType})},{packid:"p",pk_material:"其它",children:a.filter(function(e){return"others"===e.pk_itemType})}]}).catch(function(e){console.log(e)})},filterNode:function(e,t){return!e||-1!==t.pk_material.indexOf(e)},subRoleTreeCheck:function(e,t){var a=this;this.packageFormData=t.checkedNodes.filter(function(e){var t=1;t="彩盒"===a.packageType?a.productFormData.pcsCount*a.productFormData.boxCount*a.productFormData.bagCount:a.productFormData.pcsCount*a.productFormData.boxCount,"plastic"!==e.pk_itemType&&"listershell"!==e.pk_itemType&&"certificate"!==e.pk_itemType&&"instructions"!==e.pk_itemType&&"card"!==e.pk_itemType||(e.pk_consumption=a.productFormData.pcsCount,e.pk_amounts=(e.pk_price/e.pk_consumption).toFixed(6)),"colorbox"===e.pk_itemType&&(e.pk_consumption=a.productFormData.pcsCount*a.productFormData.bagCount,e.pk_amounts=(e.pk_price/e.pk_consumption).toFixed(6)),"carton"===e.pk_itemType&&(e.pk_consumption=t,e.pk_amounts=(e.pk_price/e.pk_consumption).toFixed(6));var r=a.packageFormData.filter(function(e){return"carton"===e.pk_itemType});return r.length?("tape"===e.pk_itemType&&(e.pk_consumption=((2*(r[0].pk_length+6)+4*(r[0].pk_width+6))/36/45/t).toFixed(6),e.pk_amounts=(e.pk_price*e.pk_consumption).toFixed(6)),!!e.pk_price):!!e.pk_price})},copySubRoleTreeCheck:function(e,t){var a=this;this.copyPackageFormData=t.checkedNodes.filter(function(e){var t=1;t="彩盒"===a.packageType?a.copyProductFormData.pcsCount*a.copyProductFormData.boxCount*a.copyProductFormData.bagCount:a.copyProductFormData.pcsCount*a.copyProductFormData.boxCount,"plastic"!==e.pk_itemType&&"listershell"!==e.pk_itemType&&"certificate"!==e.pk_itemType&&"instructions"!==e.pk_itemType&&"card"!==e.pk_itemType||(e.pk_consumption=a.copyProductFormData.pcsCount,e.pk_amounts=(e.pk_price/e.pk_consumption).toFixed(6)),"colorbox"===e.pk_itemType&&(e.pk_consumption=a.copyProductFormData.pcsCount*a.copyProductFormData.bagCount,e.pk_amounts=(e.pk_price/e.pk_consumption).toFixed(6)),"carton"===e.pk_itemType&&(e.pk_consumption=t,e.pk_amounts=(e.pk_price/e.pk_consumption).toFixed(6));var r=a.copyPackageFormData.filter(function(e){return"carton"===e.pk_itemType});return r.length?("tape"===e.pk_itemType&&(e.pk_consumption=((2*(r[0].pk_length+6)+4*(r[0].pk_width+6))/36/45/t).toFixed(6),e.pk_amounts=(e.pk_price*e.pk_consumption).toFixed(6)),!!e.pk_price):!!e.pk_price})},getSuccessionList:function(){var e=this,t=new FormData;t.append("current",1),t.append("size",1e3),t.append("sc_name",""),k({method:"POST",url:"/api/selectSc",data:t}).then(function(t){e.successionList=t.records})},handleBoxChange:function(e,t){e.forEach(function(e){e.rm_prop=t})},getAsyncMaterialOptions:function(e){var t=this;return $()(P.a.mark(function a(){var r;return P.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return r=t.itemType_options.map(function(e){return new p.a(function(a,r){var o=new FormData;o.append("uuid",t.productFormData.productType),o.append("rm_itemType",e.label),k({method:"POST",url:"/api/selectRmType",data:o}).then(function(r){t[e.value+"_options"]=r,a()}).catch(function(){r(new Error("请求失败, 请刷新重试"))})})}),a.next=3,p.a.all(r);case 3:t.optionsAssignments(e);case 4:case"end":return a.stop()}},a,t)}))()},filterMaterials:function(){var e=this;this.itemDataForm={out:[],mid:[],in:[],nasal:[],binding:[],foam:[],others:[]},k({method:"POST",url:"/api/selectLc",data:{uuid:this.productFormData.productType}}).then(function(t){e.laborCostList=t}),this.getAsyncMaterialOptions("",null)},getTableHeight:function(){var e=window.innerHeight-180;this.tableHeight=e<=300?300:window.innerHeight-180},filterHeaders:function(e,t,a){return t[a.property]===e},seachAll:function(){this.creator="",this.queryProductNo="",this.queryProductName="",this.productConnection="",this.currentNum=1,this.pageSize=50,this.getAllList()},seachMine:function(){this.creator=b.getCookie("userName"),this.queryProductNo="",this.queryProductName="",this.productConnection="",this.currentNum=1,this.pageSize=50,this.getAllList()},seach:function(){this.currentNum=1,this.pageSize=50,this.getAllList()},getAllList:function(){var e=this;this.loading=!0;var t=new FormData;t.append("current",this.currentNum),t.append("size",this.pageSize),t.append("productName",this.queryProductName),t.append("number",this.queryProductNo),t.append("productConnection",this.productConnection),t.append("creator",this.creator),k({method:"POST",url:"/api/PostAllTo",data:t}).then(function(t){e.loading=!1,e.standardTableList=t.records.filter(function(e){return"true"===e.isSticky});var a=t.records.filter(function(e){return!e.isSticky});e.tableList=a,e.totalNum=a.length}).catch(function(){e.loading=!1})},handleSizeChange:function(e){this.pageSize=e,this.getAllList()},handleCurrentChange:function(e){this.currentNum=e,this.getAllList()},selectStandard:function(){var e=this;this.productFormData.productSuccession=0,this.productFormData.productType=0,this.typeList=[],this.itemDataForm={out:[],mid:[],in:[],nasal:[],binding:[],foam:[],others:[]},this.out_options=[],this.mid_options=[],this.in_options=[],this.nasal_options=[],this.binding_options=[],this.foam_options=[],this.others_options=[];var t=new FormData;t.append("sc_standard",this.productFormData.productStandard),t.append("sc_name",""),k({method:"POST",url:"/api/SelectType_Model",data:t}).then(function(t){var a=t.map(function(e){return e.sc_name});e.successionList=R()(new T.a(a))}).catch(function(e){console.log(e)})},selectSuccession:function(e){var t=this;this.typeList=[],this.itemDataForm={out:[],mid:[],in:[],nasal:[],binding:[],foam:[],others:[]},this.out_options=[],this.mid_options=[],this.in_options=[],this.nasal_options=[],this.binding_options=[],this.foam_options=[],this.others_options=[];var a=new FormData;a.append("sc_standard",this.productFormData.productStandard),a.append("sc_name",e),k({method:"POST",url:"/api/SelectType_Model",data:a}).then(function(e){t.typeList=e}).catch(function(e){console.log(e)})},selectType:function(){this.laborCostList=[],this.filterMaterials()},createProductForm:function(){this.alertSTR="",this.productFormData={},this.packageFormData=[],this.out_options=[],this.mid_options=[],this.in_options=[],this.nasal_options=[],this.binding_options=[],this.foam_options=[],this.others_options=[],this.stepIndex=1,this.handleAddModal=!0,this.getPackageList()},postCopyMain:function(){var e=this,t=new p.a(function(t,a){e.$refs.productForm.validate(function(e){e?t():a(new Error("错误！"))})}),a=new p.a(function(t,a){e.$refs.freightForm.validate(function(e){e?t():a(new Error("错误！"))})}),r=new p.a(function(t,a){e.$refs.CPLCTableForm.validate(function(e){e?t():a(new Error("错误！"))})});p.a.all([t,a,r]).then(function(){if(!b.getCookie("userName"))return e.$message.warning("登录超时，请重新登录！"),!1;if(!e.copyPackageFormData.every(function(e){return e.pk_consumption}))return e.$message.error("请填写用量！"),!1;var t=e.filterProps(e.copyObjData.rawMaterialTableData,"rm_prop");e.copyFreightFormData.fr_price=0;var a=e.copyObjData.postForm.pcsCount,r=e.copyObjData.postForm.bagCount,o=e.copyObjData.postForm.boxCount;if(e.copyFreightFormData.goodsCount)"Ex Works"===e.copyFreightFormData.terms?e.copyFreightFormData.fr_price=0:e.copyFreightFormData.fr_price=e.copyFreightFormData.expense/e.copyFreightFormData.goodsCount;else if("Ex Works"===e.copyFreightFormData.terms)e.copyFreightFormData.fr_price=0;else if("FOB"===e.copyFreightFormData.terms&&"散货"===e.copyFreightFormData.shippingType){var l=a*r*o;e.copyFreightFormData.fr_price=e.copyFreightFormData.expense/(2e3*l)}else e.copyFreightFormData.fr_price=e.copyFreightFormData.expense/e.CTN_20INCHI;var i=e.copyObjData.LaborCostTableData.filter(function(e){return!e.lc_factory}),n=[s()({lc_amounts:e.copyLCTableFormData.lc_price,lc_consumption:1,lc_exchangeRate:1,lc_unit:"无"},e.copyLCTableFormData,{lc_factory:e.copyProductFormData.productFactory})],c=[{lc_amounts:e.copyPKLCTableFormData.lc_price,lc_consumption:1,lc_exchangeRate:1,lc_name:e.copyPKLCTableFormData.lc_name,lc_unit:"无",lc_price:e.copyPKLCTableFormData.lc_price}],p=[];p=e.isIncludePK?i.concat(n):c.concat(n),e.copyProductFormData.productFactory=e.copyProductFormData.productFactory+"("+e.copyFreightFormData.sales_type+")";var u=s()({uuid:"",materialData:[t],laborCostData:p,packageFormData:e.isIncludePK?e.inCluedPKdata:e.copyPackageFormData,freightFormData:s()({},e.copyFreightFormData,{factory:e.copyProductFormData.productFactory}),creator:b.getCookie("userName"),creationDate:v(Date.now(),"YYYY-MM-DD HH:mm:ss"),bagCount:r,boxCount:o,pcsCount:a},e.copyProductFormData);console.log("copyPostData",u),k({method:"POST",url:"/api/mainadd2",data:u}).then(function(t){e.$message({offset:80,message:"保存成功！",type:"success"}),e.callOfCopy()})})},handleCalculateCopyCTN:function(e,t,a){var r=this;if(this.inCluedPKdata.forEach(function(e){switch(e.pk_itemType){case"colorbox":r.packageType="彩盒";break;case"card":r.packageType="吊卡";break;case"listershell":r.packageType="泡壳"}}),this.isIncludePK){var o=s()({pcsCount:this.copyObjData.postForm.pcsCount,bagCount:this.copyObjData.postForm.bagCount,boxCount:this.copyObjData.postForm.boxCount},t);this.handleCalculateCTN(e,o,this.inCluedPKdata)}else this.handleCalculateCTN(e,t,a)},handleCalculateCTN:function(e,t,a){var r=1;if(r="彩盒"===this.packageType?t.pcsCount*t.boxCount*t.bagCount:t.pcsCount*t.boxCount,e.ctn=Math.ceil(e.goodsCount/r),!a.length)return!1;this.cuft=0;var o=a.filter(function(e){return"carton"===e.pk_itemType});if(!o.length)return this.$message.error("未配置“外箱”包装，请在包装配置中增加！"),!1;this.cuft=(2.54*o[0].pk_length*o[0].pk_width*2.54*o[0].pk_height*2.54*353e-7).toFixed(6),this.CTN_20INCHI=Math.floor(950/this.cuft*r),this.CTN_40INCHI=Math.floor(2e3/this.cuft*r);var l=(this.cuft/35.315).toFixed(6);this.CBM_ALL=(l*e.ctn).toFixed(1),e.cmb=this.CBM_ALL,this.alertSTR="提示：散装2000个彩盒共有"+2e3*t.pcsCount+"个口罩。"},calculateFR:function(){this.detailObjData.freightTableData[0].fr_price=0;var e=this.detailObjData.postForm.pcsCount*this.detailObjData.postForm.bagCount*this.detailObjData.postForm.boxCount;this.detailObjData.freightTableData[0].goodsCount?this.detailObjData.freightTableData[0].fr_price=this.detailObjData.freightTableData[0].expense/this.detailObjData.freightTableData[0].goodsCount:"FOB"===this.detailObjData.freightTableData[0].terms&&"散货"===this.detailObjData.freightTableData[0].shippingType?this.detailObjData.freightTableData[0].fr_price=this.detailObjData.freightTableData[0].expense/(2e3*e):this.detailObjData.freightTableData[0].fr_price=0,this.getSummariesFR(this.detailObjData)},postAddMain:function(e){var t=this;this.$refs[e].validate(function(e){if(!e)return!1;if(b.getCookie("userName")){t.freightFormData.fr_price=0;var a=t.productFormData.pcsCount,r=t.productFormData.bagCount,o=t.productFormData.boxCount;if(t.freightFormData.goodsCount)"Ex Works"===t.freightFormData.terms?t.freightFormData.fr_price=0:t.freightFormData.fr_price=t.freightFormData.expense/t.freightFormData.goodsCount;else if("Ex Works"===t.freightFormData.terms)t.freightFormData.fr_price=0;else if("FOB"===t.freightFormData.terms&&"散货"===t.freightFormData.shippingType){var l=a*r*o;t.freightFormData.fr_price=t.freightFormData.expense/(2e3*l)}else t.freightFormData.fr_price=t.freightFormData.expense/t.CTN_20INCHI;var i=s()({},t.freightFormData),n=[{lc_amounts:t.PKLCTableFormData.lc_price,lc_consumption:1,lc_exchangeRate:1,lc_name:t.PKLCTableFormData.lc_name,lc_unit:"无",lc_price:t.PKLCTableFormData.lc_price},s()({},t.LCFormData.lcData,{lc_amounts:t.LCFormData.lcData.lc_price})];t.productFormData.productFactory=t.productFormData.productFactory+"("+t.freightFormData.sales_type+")";var c=s()({uuid:"",isSticky:null,materialData:[t.itemDataForm],laborCostData:n,packageFormData:t.packageFormData,freightFormData:i,creator:b.getCookie("userName"),creationDate:v(Date.now(),"YYYY-MM-DD HH:mm:ss")},t.productFormData);console.log("postData",c),k({method:"POST",url:"/api/mainadd2",data:c}).then(function(e){t.$message({offset:80,message:"保存成功！",type:"success"}),t.callOf()})}else t.$message.warning("登录超时，请重新登录！")})},updateProductForm:function(e){var t=this;k({method:"POST",url:"/api/getById",data:{admin_id:e.admin_id}}).then(function(a){t.isDomesticSales=a.freightTableData[0].sales_type,t.editTitle=e.productName,t.detailObjData=a,t.handleEditModal=!0})},handleModifyClose:function(){this.handleEditModal=!1,this.getAllList()},copyProductForm:function(e){var t=this;this.alertSTR="",k({method:"POST",url:"/api/getById",data:{admin_id:e.admin_id}}).then(function(e){t.copyObjData=e,t.inCluedPKdata=e.packageTableData,t.getPackageList(),t.handleCopyModal=!0})},queryLCSearchAsync:function(e,t){var a=new FormData;a.append("current",1),a.append("size",9999),a.append("lc_name",e),k({method:"POST",url:"/api/LaborCostAll",data:a}).then(function(e){var a=e.records.map(function(e){return s()({value:e.lc_name+" ("+e.lc_factory+")"},e)});t(a)})},handleLCrSelect:function(e){this.copyLCTableFormData.lc_name=e.lc_name,this.copyLCTableFormData.lc_price=e.lc_price},handleCurrencyChange:function(e,t,a){e[t]=this.FXRates[e[a]]},queryRawMaterialList:function(){var e=this;k({method:"POST",url:"/api/selectRm",data:{uuid:this.detailObjData.postForm.uuid}}).then(function(t){e.detailObjData.rawMaterialTableData=t,e.rm_btn=!1})},addRawMaterialRow:function(){this.rm_btn=!0,this.detailObjData.rawMaterialTableData.unshift({showInput:!0,uuid:this.detailObjData.postForm.uuid,rm_material:"",rm_price:0,rm_unit:"",rm_currencyType:"",rm_amounts:"",rm_exchangeRate:"",rm_loss:.97,rm_consumption:"",rm_number:""})},queryRmNumberSearchAsync:function(e,t){var a=new FormData;a.append("current",1),a.append("size",9999),a.append("rm_material",""),a.append("rm_number",e),k({method:"POST",url:"/api/GoodsAll",data:a}).then(function(e){var a=e.records.map(function(e){return s()({value:e.rm_number},e)});t(a)})},handleRmNumberSelect:function(e){this.detailObjData.rawMaterialTableData[0].rm_material=e.rm_material,this.detailObjData.rawMaterialTableData[0].rm_itemType=e.rm_itemType,this.detailObjData.rawMaterialTableData[0].rm_price=e.rm_price,this.detailObjData.rawMaterialTableData[0].rm_unit=e.rm_unit,this.detailObjData.rawMaterialTableData[0].rm_currencyType=e.rm_currencyType,this.detailObjData.rawMaterialTableData[0].rm_exchangeRate=e.rm_exchangeRate},queryNameSearchAsync:function(e,t){var a=new FormData;a.append("current",1),a.append("size",9999),a.append("rm_material",e),a.append("rm_number",""),k({method:"POST",url:"/api/GoodsAll",data:a}).then(function(e){var a=e.records.map(function(e){return s()({value:e.rm_material},e)});t(a)})},handleNameSelect:function(e){this.detailObjData.rawMaterialTableData[0].rm_number=e.rm_number,this.detailObjData.rawMaterialTableData[0].rm_itemType=e.rm_itemType,this.detailObjData.rawMaterialTableData[0].rm_price=e.rm_price,this.detailObjData.rawMaterialTableData[0].rm_unit=e.rm_unit,this.detailObjData.rawMaterialTableData[0].rm_exchangeRate=e.rm_exchangeRate,this.detailObjData.rawMaterialTableData[0].rm_currencyType=e.rm_currencyType},calculateRM:function(e){if(0!==e.rm_consumption){if(e.rm_price&&e.rm_exchangeRate&&e.rm_loss){var t=0;e.rm_consumption?e.rm_consumption>0&&e.rm_consumption<1?t=e.rm_price*e.rm_exchangeRate*e.rm_consumption/e.rm_loss:e.rm_consumption>=1&&(t=e.rm_price*e.rm_exchangeRate/e.rm_consumption/e.rm_loss):t=e.rm_price*e.rm_exchangeRate/e.rm_loss,e.rm_amounts=t.toFixed(5)}}else this.$message.error("用量不能为0！")},updateRawMaterialRow:function(e){e.showInput=!0,this.rm_btn=!0},cancelRawMaterialRow:function(e){e.rm_id?this.queryRawMaterialList():(this.detailObjData.rawMaterialTableData.splice(0,1),this.rm_btn=!1)},saveRawMaterialRow:function(e){var t=this;0!==e.rm_consumption?(this.calculateRM(e),this.$refs.rmTableForm.validate(function(a){if(!a)return!1;if(b.getCookie("userName")){var r={updatePersonnel:b.getCookie("userName"),admin_id:t.detailObjData.postForm.admin_id,updateTime:v(Date.now(),"YYYY-MM-DD HH:mm:ss"),pcsCount:t.detailObjData.postForm.pcsCount,bagCount:t.detailObjData.postForm.bagCount,boxCount:t.detailObjData.postForm.boxCount};k({method:"POST",url:"/api/updateadmin",data:r}).then(function(e){console.log(e)}),k({method:"POST",url:e.rm_id?"/api/updaterm":"/api/insertScRm",data:s()({},e,r)}).then(function(a){t.$message({offset:80,message:"保存成功！",type:"success"}),e.showInput=!1,t.queryRawMaterialList()})}else t.$message.warning("登录超时，请重新登录！")})):this.$message.error("用量不能为0！")},deleteRawMaterialRow:function(e){var t=this;this.$refs.rmTableForm.clearValidate(),e.showInput?(this.detailObjData.rawMaterialTableData.splice(0,1),this.rm_btn=!1):this.$confirm("是否删除”"+e.rm_material+"“信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){k({method:"POST",url:"/api/updateadmin",data:{updatePersonnel:b.getCookie("userName"),admin_id:t.detailObjData.postForm.admin_id,updateTime:v(Date.now(),"YYYY-MM-DD HH:mm:ss"),pcsCount:t.detailObjData.postForm.pcsCount,bagCount:t.detailObjData.postForm.bagCount,boxCount:t.detailObjData.postForm.boxCount}}).then(function(e){console.log(e)}),k({method:"POST",url:"/api/deleterm",data:{rm_id:e.rm_id}}).then(function(a){t.$message({offset:80,message:"删除成功！",type:"success"}),e.showInput=!1,t.queryRawMaterialList()})}).catch(function(){t.$message({offset:80,type:"info",message:"已取消删除！"})})},queryLaborCostList:function(){var e=this;k({method:"POST",url:"/api/selectLc",data:{uuid:this.detailObjData.postForm.uuid}}).then(function(t){e.detailObjData.LaborCostTableData=t,e.lc_btn=!1})},getLaborCostList:function(){var e=this,t=new FormData;t.append("current",1),t.append("size",9999),t.append("lc_name",""),k({method:"POST",url:"/api/LaborCostAll",data:t}).then(function(t){e.laborCostFilterList=t.records})},queryLCNameSearchAsync:function(e,t){var a=this.laborCostFilterList.map(function(e){return s()({value:e.lc_name+" ("+e.lc_factory+")"},e)});t(e?a.filter(this.createFilter(e)):a)},handleLCNameSelect:function(e){this.detailObjData.LaborCostTableData[0].lc_name=e.lc_name+"人工",this.detailObjData.LaborCostTableData[0].lc_price=e.lc_price,this.detailObjData.LaborCostTableData[0].lc_unit=e.lc_unit,this.detailObjData.LaborCostTableData[0].lc_factory=e.lc_factory},addLaborRow:function(){this.lc_btn=!0,this.detailObjData.LaborCostTableData.unshift({showInput:!0,uuid:this.detailObjData.postForm.uuid,lc_name:"",lc_price:0,lc_unit:"",lc_exchangeRate:1,lc_consumption:""})},updateLaborRow:function(e){e.showInput=!0,this.lc_btn=!0},calculateLC:function(e){if(0!==e.lc_consumption){if(e.lc_price&&e.lc_exchangeRate&&e.lc_consumption){var t=0;e.lc_consumption?e.lc_consumption>0&&e.lc_consumption<1?t=e.lc_price*e.lc_exchangeRate*e.lc_consumption:e.lc_consumption>=1&&(t=e.lc_price*e.lc_exchangeRate/e.lc_consumption):t=e.lc_price*e.lc_exchangeRate,e.lc_amounts=t.toFixed(5)}}else this.$message.error("用量不能为0！")},cancelLaborRow:function(e){e.lc_id?this.queryLaborCostList():(this.detailObjData.LaborCostTableData.splice(0,1),this.lc_btn=!1)},deleteLaborRow:function(e){var t=this;this.$refs.lcTableForm.clearValidate(),e.showInput?(this.detailObjData.LaborCostTableData.splice(0,1),this.lc_btn=!1):this.$confirm("是否删除”"+e.lc_name+"“信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){k({method:"POST",url:"/api/updateadmin",data:{updatePersonnel:b.getCookie("userName"),admin_id:t.detailObjData.postForm.admin_id,updateTime:v(Date.now(),"YYYY-MM-DD HH:mm:ss"),pcsCount:t.detailObjData.postForm.pcsCount,bagCount:t.detailObjData.postForm.bagCount,boxCount:t.detailObjData.postForm.boxCount}}).then(function(e){console.log(e)}),k({method:"POST",url:"/api/deletelc",data:{lc_id:e.lc_id}}).then(function(a){t.$message({offset:80,message:"删除成功！",type:"success"}),e.showInput=!1,t.queryLaborCostList()})}).catch(function(){t.$message({offset:80,type:"info",message:"已取消删除！"})})},saveLaborCostRow:function(e){var t=this;0!==e.lc_consumption?(this.calculateLC(e),this.$refs.lcTableForm.validate(function(a){if(!a)return!1;if(b.getCookie("userName")){var r={updatePersonnel:b.getCookie("userName"),admin_id:t.detailObjData.postForm.admin_id,updateTime:v(Date.now(),"YYYY-MM-DD HH:mm:ss"),pcsCount:t.detailObjData.postForm.pcsCount,bagCount:t.detailObjData.postForm.bagCount,boxCount:t.detailObjData.postForm.boxCount};k({method:"POST",url:"/api/updateadmin",data:r}).then(function(e){console.log(e)}),e.lc_currencyType="CNY",e.lc_exchangeRate=1,k({method:"POST",url:e.lc_id?"/api/updatelc":"/api/lcadd",data:s()({},e,r)}).then(function(a){t.$message({offset:80,message:"保存成功！",type:"success"}),e.showInput=!1,t.queryLaborCostList()})}else t.$message.warning("登录超时，请重新登录！")})):this.$message.error("用量不能为0！")},handleClose:function(e){this.$confirm("确认关闭？").then(function(t){e()}).catch(function(e){})},queryPkNameSearchAsync:function(e,t){var a=new FormData;a.append("current",1),a.append("size",9999),a.append("pk_material",e),a.append("pk_number",""),k({method:"POST",url:"/api/PackAll",data:a}).then(function(e){var a=e.records.map(function(e){return s()({value:e.pk_material},e)});t(a)})},queryPkNumberSearchAsync:function(e,t){var a=new FormData;a.append("current",1),a.append("size",9999),a.append("pk_material",""),a.append("pk_number",e),k({method:"POST",url:"/api/PackAll",data:a}).then(function(e){var a=e.records.map(function(e){return s()({value:e.pk_number},e)});t(a)})},handlePkNumberSelect:function(e){this.detailObjData.packageTableData[0].pk_material=e.pk_material,this.detailObjData.packageTableData[0].pk_price=e.pk_price,this.detailObjData.packageTableData[0].pk_unit=e.pk_unit,this.detailObjData.packageTableData[0].pk_currencyType=e.pk_currencyType,this.detailObjData.packageTableData[0].pk_exchangeRate=e.pk_exchangeRate},handlePkNameSelect:function(e){this.detailObjData.packageTableData[0].pk_number=e.pk_number,this.detailObjData.packageTableData[0].pk_price=e.pk_price,this.detailObjData.packageTableData[0].pk_unit=e.pk_unit,this.detailObjData.packageTableData[0].pk_currencyType=e.pk_currencyType,this.detailObjData.packageTableData[0].pk_exchangeRate=e.pk_exchangeRate},addPackageRow:function(){this.pk_btn=!0,this.detailObjData.packageTableData.unshift({showInput:!0,uuid:this.detailObjData.postForm.uuid,pk_material:"",pk_price:0,pk_unit:"",pk_currencyType:"CNY",pk_amounts:"",pk_exchangeRate:1,pk_consumption:"",pk_number:""})},updatePackageRow:function(e){e.showInput=!0,this.pk_btn=!0},cancelPackageRow:function(e){e.pk_id?this.queryPackageList():(this.detailObjData.packageTableData.splice(0,1),this.pk_btn=!1)},savePackageRow:function(e){var t=this;0!==e.pk_consumption?(this.calculatePackage(e),this.$refs.pkTableForm.validate(function(a){if(!a)return!1;if(b.getCookie("userName")){var r={updatePersonnel:b.getCookie("userName"),admin_id:t.detailObjData.postForm.admin_id,updateTime:v(Date.now(),"YYYY-MM-DD HH:mm:ss"),pcsCount:t.detailObjData.postForm.pcsCount,bagCount:t.detailObjData.postForm.bagCount,boxCount:t.detailObjData.postForm.boxCount};k({method:"POST",url:"/api/updateadmin",data:r}).then(function(e){console.log(e)}),k({method:"POST",url:e.pk_id?"/api/updatepk":"/api/pkadd",data:s()({},e,r)}).then(function(a){t.$message({offset:80,message:"保存成功！",type:"success"}),e.showInput=!1,t.queryPackageList()})}else t.$message.warning("登录超时，请重新登录！")})):this.$message.error("用量不能为0！")},deletePackageRow:function(e){var t=this;this.$refs.pkTableForm.clearValidate(),e.showInput?(this.detailObjData.packageTableData.splice(0,1),this.pk_btn=!1):this.$confirm("是否删除”"+e.pk_material+"“信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){k({method:"POST",url:"/api/updateadmin",data:{updatePersonnel:b.getCookie("userName"),admin_id:t.detailObjData.postForm.admin_id,updateTime:v(Date.now(),"YYYY-MM-DD HH:mm:ss"),pcsCount:t.detailObjData.postForm.pcsCount,bagCount:t.detailObjData.postForm.bagCount,boxCount:t.detailObjData.postForm.boxCount}}).then(function(e){console.log(e)}),k({method:"POST",url:"/api/deletepk",data:{pk_id:e.pk_id}}).then(function(a){t.$message({offset:80,message:"删除成功！",type:"success"}),e.showInput=!1,t.queryPackageList()})}).catch(function(){t.$message({offset:80,type:"info",message:"已取消删除！"})})},queryPackageList:function(){var e=this;k({method:"POST",url:"/api/selectPk",data:{uuid:this.detailObjData.postForm.uuid}}).then(function(t){e.detailObjData.packageTableData=t,e.pk_btn=!1})},calculatePackage:function(e){if(0!==e.pk_consumption){if(e.pk_price&&e.pk_consumption){e.pk_exchangeRate||(e.pk_exchangeRate=1);var t=0;e.pk_consumption?e.pk_consumption>0&&e.pk_consumption<1?t=e.pk_price*e.pk_exchangeRate*e.pk_consumption:e.pk_consumption>=1&&(t=e.pk_price*e.pk_exchangeRate/e.pk_consumption):t=e.pk_price*e.pk_exchangeRate,e.pk_amounts=t.toFixed(5),this.$forceUpdate()}}else this.$message.error("用量不能为0！")},optionsAssignments:function(e){var t=this;return $()(P.a.mark(function a(){return P.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:e&&k({method:"POST",url:"/api/getById",data:{admin_id:e.admin_id}}).then(function(a){t.editTitle=e.productName,t.itemDataForm=t.filterProps(a.rawMaterialTableData,"rm_prop"),t.filterChecked("out","rm_material"),t.filterChecked("mid","rm_material"),t.filterChecked("in","rm_material"),t.filterChecked("nasal","rm_material"),t.filterChecked("binding","rm_material"),t.filterChecked("foam","rm_material"),t.filterChecked("others","rm_material")});case 1:case"end":return a.stop()}},a,t)}))()},filterProps:function(e,t){var a={out:[],mid:[],in:[],nasal:[],binding:[],foam:[],others:[]};return e.forEach(function(e){"out"===e[t]&&a.out.push(e),"in"===e[t]&&a.in.push(e),"mid"===e[t]&&a.mid.push(e),"nasal"===e[t]&&a.nasal.push(e),"foam"===e[t]&&a.foam.push(e),"binding"===e[t]&&a.binding.push(e),"others"===e[t]&&a.others.push(e)}),a},filterChecked:function(e,t){var a=this;this.itemDataForm[e].map(function(r){var o=a[e+"_options"].findIndex(function(e){return e[t]===r[t]});a[e+"_options"][o]=r})},handleDetail:function(e){var t=this;return $()(P.a.mark(function a(){var r;return P.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:t.ttTableData=[],t.lc_percentage=156,t.marketing=20,(r=new FormData).append("current",1),r.append("size",99999),r.append("pk_material",""),r.append("pk_number",""),k({method:"POST",url:"/api/PackAll",data:r}).then(function(e){t.changePackageList=e.records}).catch(function(e){console.log(e)}),k({method:"POST",url:"/api/getById",data:{admin_id:e.admin_id}}).then(function(a){var r,o,l,i;t.editTitle=e.productName,t.isDomesticSales=a.freightTableData[0].sales_type,t.saleRate=a.freightTableData[0].tax_domestic?a.freightTableData[0].tax_domestic:13,t.marketing=a.freightTableData[0].sales?a.freightTableData[0].sales:20,t.tt_currencyType=a.freightTableData[0].currency?a.freightTableData[0].currency:"内销"===t.isDomesticSales?"CNY":"USDP",t.totalRate=a.freightTableData[0].exchange_rate?a.freightTableData[0].exchange_rate:"内销"===t.isDomesticSales?1:6.9,t.lc_percentage=a.freightTableData[0].lc_percentage?a.freightTableData[0].lc_percentage:156,t.getSummariesFR(a),t.getFXRates(),t.detailObjData=a,t.FilterPackageArr=(r=t.changePackageList,o=a.packageTableData,l="pk_number",i="pk_price",r.filter(function(e){return o.some(function(t){return t[l]===e[l]&&t[i]!==e[i]})})),t.handleDetailModal=!0});case 10:case"end":return a.stop()}},a,t)}))()},updateFreightPrice:function(){k({method:"POST",url:"/api/updateadmin",data:{updatePersonnel:b.getCookie("userName"),admin_id:this.detailObjData.postForm.admin_id,updateTime:v(Date.now(),"YYYY-MM-DD HH:mm:ss"),pcsCount:this.detailObjData.postForm.pcsCount,bagCount:this.detailObjData.postForm.bagCount,boxCount:this.detailObjData.postForm.boxCount}}).then(function(e){console.log(e)}),k({method:"POST",url:"/api/updateadminFr",data:{uuid:this.detailObjData.postForm.uuid,expense:this.detailObjData.freightTableData[0].expense,fr_price:this.detailObjData.freightTableData[0].fr_price}}).then(function(e){console.log(e)})},handleDetailModalClose:function(){var e=this;this.fr_boolean=!1,k({method:"POST",url:"/api/updateadminFr",data:{uuid:this.detailObjData.postForm.uuid,sales_type:this.isDomesticSales,tax_domestic:this.saleRate,sales:this.marketing,currency:this.tt_currencyType,exchange_rate:this.totalRate,lc_percentage:this.lc_percentage,expense:this.detailObjData.freightTableData[0].expense,fr_price:this.detailObjData.freightTableData[0].fr_price}}).then(function(t){console.log(t),e.handleDetailModal=!1,e.getAllList()}).catch(function(e){console.log(e)})},viewhHistory:function(e){var t=this;k({method:"POST",url:"/api/selectRc",data:{uuid:e.uuid}}).then(function(a){t.editTitle=e.productName,t.historyTableData=a.data,t.historyModal=!0}).catch(function(e){console.log(e)})},handleDelete:function(e){var t=this;this.$confirm("此操作将永久删除”"+e.productName+"“产品信息, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){k({method:"POST",url:"/api/deleteadmin",data:{uuid:e.uuid}}).then(function(e){t.$message({offset:80,type:"success",message:"操作成功！"}),t.getAllList()})}).catch(function(){t.$message({offset:80,type:"info",message:"已取消删除！"})})},handleMainClose:function(){this.$refs.productForm.resetFields(),this.$refs.materialForm.resetFields(),this.$refs.packageForm.resetFields(),this.$refs.freightForm.resetFields()},callOf:function(){this.cuft=0,this.CBM_ALL=0,this.CTN_20INCHI=0,this.CTN_40INCHI=0,this.$refs.productForm.resetFields(),this.$refs.materialForm.resetFields(),this.$refs.freightForm.resetFields(),this.$refs.itemDataForm.resetFields(),this.$refs.PKLCTableForm.resetFields(),this.$refs.LCForm.resetFields(),this.PKLCTableFormData={lc_name:"包装统一工角报价"},this.productFormData={},this.successionList=[],this.typeList=[],this.laborCostList=[],this.packageFormData=[],this.handleAddModal=!1,this.getAllList()},callOfdrawer:function(){this.drawer=!1,this.pkFormData={},this.$refs.ruleForm.resetFields(),this.getPackageList()},handlePostForm:function(){var e=this;this.$refs.ruleForm.validate(function(t){if(!t)return!1;e.pkFormData.pk_currencyType="CNY",e.pkFormData.pk_exchangeRate=1,k({method:"POST",url:"/api/packadd",data:e.pkFormData}).then(function(t){e.$message({offset:80,message:"保存成功！",type:"success"}),e.callOfdrawer()})})},callOfCopy:function(){this.cuft=0,this.CBM_ALL=0,this.CTN_20INCHI=0,this.CTN_40INCHI=0,this.alertSTR="",this.packageType="",this.handleCopyModal=!1,this.isIncludePK=!1,this.inCluedPKdata=[],this.$refs.productForm.resetFields(),this.$refs.freightForm.resetFields(),this.$refs.PKLCTableForm.resetFields(),this.$refs.CPLCTableForm.resetFields(),this.copyProductFormData={},this.copyObjData={postForm:{},rawMaterialTableData:[],LaborCostTableData:[],packageTableData:[],freightTableData:[{}]},this.copyPKLCTableFormData={lc_name:"包装统一工角报价"},this.copyPackageFormData=[],this.copyFreightFormData={factory:"",port:"",terms:"",boxSize:[]},this.getAllList()},handleDetailClose:function(e){var t=this;this.$confirm("确认关闭？").then(function(a){e(),t.getAllList()}).catch(function(e){})},handleCTchange:function(e){this.tt_currencyType=e,this.totalRate=this.ManualExchangeRate[e],this.fr_boolean=!1},handleRMStatusChange:function(e){this.rm_selectData=e.reduce(function(e,t){return e+t.rm_amounts},0)},handlePKStatusChange:function(e){this.pk_selectData=e.reduce(function(e,t){return e+t.pk_amounts},0)},getSummariesRM:function(e){var t=e.columns,a=e.data,r=[];return t.forEach(function(e,o){if(o!==t.length-3){if(o===t.length-2){var l=a.map(function(t){return Number(t[e.property])});l.every(function(e){return isNaN(e)})?r[o]="":(r[o]=l.reduce(function(e,t){var a=Number(t);return isNaN(a)?e:e+t},0),r[o]=r[o].toFixed(5))}}else r[o]="合计"}),r[r.length-1]?this.rm_sum=r[r.length-1]:this.rm_sum=0,this.totalMethod(),r},getSummariesLC:function(e){var t=this,a=e.columns,r=e.data,o=[];return a.forEach(function(e,l){if(l!==a.length-2){if(l===a.length-1){var i=r.map(function(t){return Number(t[e.property])});i.every(function(e){return isNaN(e)})?o[l]="":(o[l]=i.reduce(function(e,t){var a=Number(t);return isNaN(a)?e:e+t},0),o[l]=(o[l]*t.lc_percentage/100).toFixed(5))}}else o[l]="合计"}),o[o.length-1]?this.lc_sum=o[o.length-1]:this.lc_sum=0,this.totalMethod(),o},getSummariesPK:function(e){var t=e.columns,a=e.data,r=[];return t.forEach(function(e,o){if(o!==t.length-3){if(o===t.length-2){var l=a.map(function(t){return Number(t[e.property])});l.every(function(e){return isNaN(e)})?r[o]="":(r[o]=l.reduce(function(e,t){var a=Number(t);return isNaN(a)?e:e+t},0),r[o]=r[o].toFixed(5))}}else r[o]="合计"}),r[r.length-1]?this.pk_sum=r[r.length-1]:this.pk_sum=0,this.totalMethod(),r},getSummariesFR:function(e){this.fr_sum=0,this.fr_sum=e.freightTableData[0].fr_price.toFixed(5)},handleRateChange:function(e){var t="内销"===this.isDomesticSales?+parseFloat(this.total_sum/((100-this.marketing)/100)*(this.saleRate/100+1)).toFixed(5):+parseFloat(this.total_sum/((100-this.marketing)/100)*1.003).toFixed(5);e.row=+parseFloat(t+t*Number(e.column)/100).toFixed(5)},totalMethod:function(){var e=0,t=0,a=0;if("100JPY"===this.tt_currencyType?this.total_sum=+((parseFloat(this.rm_sum)+parseFloat(this.lc_sum)+parseFloat(this.pk_sum)+parseFloat(this.fr_sum))/this.totalRate*100).toFixed(5):this.total_sum=+((parseFloat(this.rm_sum)+parseFloat(this.lc_sum)+parseFloat(this.pk_sum)+parseFloat(this.fr_sum))/this.totalRate).toFixed(5),this.rm_selectData||this.pk_selectData){this.rp_total=+parseFloat(parseFloat(this.rm_selectData)+parseFloat(this.pk_selectData)).toFixed(5);var r=this.fr_boolean?+parseFloat(Number(this.fr_sum)+this.rp_total).toFixed(5):this.rp_total,o=+parseFloat(this.total_sum-r/this.totalRate).toFixed(5);"内销"===this.isDomesticSales?(t=+parseFloat(o/((100-this.marketing)/100)*(this.saleRate/100+1)).toFixed(5),a=+parseFloat(r*(this.saleRate/100+1)/this.totalRate).toFixed(5),e=+parseFloat(t+a).toFixed(5)):(t=+parseFloat(o/((100-this.marketing)/100)).toFixed(5),a=+parseFloat(r/this.totalRate).toFixed(5),e=+parseFloat(1.003*(t+a)).toFixed(5))}else e="内销"===this.isDomesticSales?+parseFloat(this.total_sum/((100-this.marketing)/100)*(this.saleRate/100+1)).toFixed(5):+parseFloat(this.total_sum/((100-this.marketing)/100)*1.003).toFixed(5);var l=+parseFloat(e+.05*e).toFixed(5),i=+parseFloat(e+.1*e).toFixed(5),n=+parseFloat(e+.15*e).toFixed(5),s=+parseFloat(e+.2*e).toFixed(5),c=+parseFloat(e+.25*e).toFixed(5),p=+parseFloat(e+.3*e).toFixed(5);this.costa=this.total_sum,this.ttTableData=[{column:"成本",row:this.costa,meta:!1},{column:"综合成本",row:e,meta:!1},{column:"5%",row:l,meta:!1},{column:"10%",row:i,meta:!1},{column:"15%",row:n,meta:!1},{column:"20%",row:s,meta:!1},{column:"25%",row:c,meta:!1},{column:"30%",row:p,meta:!1},{column:1,row:Number(e+.01*e).toFixed(5),meta:!0}]},handleFRchange:function(e){if(e){var t=0,a=0,r=0,o=+parseFloat(this.rp_total+Number(this.fr_sum)).toFixed(5),l=+parseFloat(this.total_sum-o/this.totalRate).toFixed(5);"内销"===this.isDomesticSales?(a=+parseFloat(l/((100-this.marketing)/100)*(this.saleRate/100+1)).toFixed(5),r=+parseFloat(o*(this.saleRate/100+1)/this.totalRate).toFixed(5),t=+parseFloat(a+r).toFixed(5)):(a=+parseFloat(l/((100-this.marketing)/100)).toFixed(5),r=+parseFloat(o/this.totalRate).toFixed(5),t=+parseFloat(1.003*(a+r)).toFixed(5));var i=+parseFloat(t+.05*t).toFixed(5),n=+parseFloat(t+.1*t).toFixed(5),s=+parseFloat(t+.15*t).toFixed(5),c=+parseFloat(t+.2*t).toFixed(5),p=+parseFloat(t+.25*t).toFixed(5),u=+parseFloat(t+.3*t).toFixed(5);this.costa=this.total_sum,this.ttTableData=[{column:"成本",row:this.costa,meta:!1},{column:"综合成本",row:t,meta:!1},{column:"5%",row:i,meta:!1},{column:"10%",row:n,meta:!1},{column:"15%",row:s,meta:!1},{column:"20%",row:c,meta:!1},{column:"25%",row:p,meta:!1},{column:"30%",row:u,meta:!1},{column:1,row:Number(t+.01*t).toFixed(5),meta:!0}]}else this.totalMethod()},exportPNG:function(){var e=this;I()(this.$refs.htmlDom).then(function(t){var a=t.toDataURL("image/png");q.value=a;var r=document.createElement("a");r.href=a,r.download="《"+e.editTitle+"》-"+e.detailObjData.postForm.productConnection+"-"+v(Date.now(),"YYYYMMDD_HHmmss")+".png",r.click()})},exportDBPNG:function(){I()(this.$refs.htmlDBDom).then(function(e){var t=e.toDataURL("image/png");q.value=t;var a=document.createElement("a");a.href=t,a.download="价格对比-"+v(Date.now(),"YYYYMMDD_HHmmss")+".png",a.click()})},exportPDF:function(){var e=this,t=this.$refs.htmlDom.getBoundingClientRect();I()(document.querySelector("#pdfDom"),{allowTaint:!0,taintTest:!1,useCORS:!0,async:!0,scale:"1",dpi:"192",scrollY:t.top,height:t.height+50}).then(function(t){var a=t.width,r=t.height,o=t.toDataURL("image/jpeg/png",1),l=new j.a("","pt","a4"),i=555.28/a*r,n=i,s=0;if(n<841.89)l.addImage(o,"JPEG",20,0,555.28,i);else for(;n>0;)l.addImage(o,"JPEG",20,s,555.28,i),s-=841.89,(n-=841.89)>0&&l.addPage();return l.save("《"+e.editTitle+"》-"+e.detailObjData.postForm.productConnection+"-"+v(Date.now(),"YYYYMMDD_HHmmss")+".pdf"),l.output("datauristring")})}})},E={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"main"},[a("div",{staticClass:"query"},[a("div",{staticClass:"query-wrapper"},[a("div",{staticClass:"query-info"},[a("span",[e._v("主品名称：")]),e._v(" "),a("el-input",{attrs:{size:"mini",placeholder:"请输入主品名称查询",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.seach.apply(null,arguments)}},model:{value:e.queryProductName,callback:function(t){e.queryProductName=t},expression:"queryProductName"}})],1),e._v(" "),a("div",{staticClass:"query-info"},[a("span",[e._v("物料品号：")]),e._v(" "),a("el-input",{attrs:{size:"mini",placeholder:"请输入物料品号查询",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.seach.apply(null,arguments)}},model:{value:e.queryProductNo,callback:function(t){e.queryProductNo=t},expression:"queryProductNo"}})],1),e._v(" "),a("div",{staticClass:"query-info"},[a("span",[e._v("客户公司：")]),e._v(" "),a("el-input",{attrs:{size:"mini",placeholder:"请输入客户公司查询",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.seach.apply(null,arguments)}},model:{value:e.productConnection,callback:function(t){e.productConnection=t},expression:"productConnection"}})],1),e._v(" "),a("div",{staticClass:"query-info"},[a("span",[e._v("创建人员：")]),e._v(" "),a("el-input",{attrs:{size:"mini",placeholder:"请输入创建人员查询",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.seach.apply(null,arguments)}},model:{value:e.creator,callback:function(t){e.creator=t},expression:"creator"}})],1),e._v(" "),a("div",{staticClass:"query-info",staticStyle:{"margin-top":"12px"}},[a("el-button",{attrs:{size:"mini",icon:"el-icon-search",type:"primary",plain:""},on:{click:e.seach}},[e._v("查询")]),e._v(" "),a("el-button",{attrs:{size:"mini",icon:"el-icon-view",type:"primary",plain:""},on:{click:e.seachMine}},[e._v("我的")]),e._v(" "),a("el-button",{attrs:{size:"mini",icon:"el-icon-tickets",type:"success"},on:{click:function(t){e.standardsDrawer=!0}}},[e._v("标准")])],1),e._v(" "),a("div",{staticClass:"query-info",staticStyle:{"text-align":"right",width:"18%"}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.createProductForm}},[e._v("新增")]),e._v(" "),a("el-badge",{staticClass:"item",attrs:{value:e.selsection_ids.length}},[a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-paperclip",plain:""},on:{click:e.handleMutiple}},[e._v("对比")])],1)],1)])]),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"multipleTable",staticClass:"mainTable",attrs:{data:e.tableList,"header-cell-style":{backgroundColor:"rgb(244, 244, 245)"},height:e.tableHeight,stripe:""},on:{"selection-change":e.handleCourseSelection}},[a("el-table-column",{attrs:{type:"selection",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{prop:"productName",label:"主品名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"productConnection",label:"客户公司",width:"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"productFactory",label:"生产工厂",width:"120"}}),e._v(" "),a("el-table-column",{attrs:{prop:"productPackage",label:"包装信息",minWidth:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n           "+e._s(t.row.bagCount?"每袋"+t.row.pcsCount+"个、每盒"+t.row.bagCount+"袋、每箱"+t.row.boxCount+"盒":"泡壳包装，每盒"+t.row.pcsCount+"个、每箱"+t.row.boxCount+"盒")+"\n        ")]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"productDetail",label:"产品说明",minWidth:"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"creator",label:"创建人员",width:"120"}}),e._v(" "),a("el-table-column",{attrs:{prop:"creationDate",label:"创建时间"},scopedSlots:e._u([{key:"header",fn:function(){return[a("div",[a("span",[e._v("创建时间")]),e._v(" "),a("br"),e._v(" "),a("span",[e._v("修改时间")])])]},proxy:!0},{key:"default",fn:function(t){return[e._v("\n          "+e._s(t.row.creationDate)+"\n          "),a("br"),e._v("\n          "+e._s(t.row.updateTime)+"\n        ")]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",icon:"el-icon-document"},on:{click:function(a){return e.handleDetail(t.row)}}},[e._v("报价单")]),e._v(" "),a("el-button",{attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(a){return e.copyProductForm(t.row)}}},[e._v("复制")]),e._v(" "),a("el-dropdown",[a("span",{staticStyle:{"margin-left":"12px",cursor:"pointer",color:"#409EFF"}},[e._v("\n              更多"),a("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),e._v(" "),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",[a("el-button",{attrs:{type:"text",icon:"el-icon-time"},on:{click:function(a){return e.viewhHistory(t.row)}}},[e._v("历史记录")])],1),e._v(" "),a("el-dropdown-item",["1"===e.power||e.user===t.row.creator?a("el-button",{attrs:{type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.updateProductForm(t.row)}}},[e._v("修改主品")]):e._e()],1),e._v(" "),a("el-dropdown-item",["1"===e.power||e.user===t.row.creator?a("el-button",{attrs:{type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除主品")]):e._e()],1)],1)],1)]}}])})],1),e._v(" "),a("div",{staticClass:"page"},[a("el-pagination",{attrs:{"current-page":e.currentNum,"page-sizes":[50,100,150,200],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.totalNum},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),a("el-drawer",{attrs:{title:"标准模版列表",visible:e.standardsDrawer,direction:"btt",size:"85%"},on:{"update:visible":function(t){e.standardsDrawer=t}}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"multipleStandardsTable",staticClass:"mainTable",attrs:{data:e.standardTableList,"header-cell-style":{backgroundColor:"rgb(244, 244, 245)"},stripe:""},on:{"selection-change":e.handleStandardsSelection}},[a("el-table-column",{attrs:{type:"selection",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{prop:"productName",label:"主品名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"productConnection",label:"客户公司"}}),e._v(" "),a("el-table-column",{attrs:{prop:"productFactory",label:"生产工厂"}}),e._v(" "),a("el-table-column",{attrs:{prop:"productPackage",label:"包装信息"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n            "+e._s(t.row.bagCount?"每袋"+t.row.pcsCount+"个、每盒"+t.row.bagCount+"袋、每箱"+t.row.boxCount+"盒":"泡壳包装，每盒"+t.row.pcsCount+"个、每箱"+t.row.boxCount+"盒")+"\n          ")]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"creator",label:"创建人员"}}),e._v(" "),a("el-table-column",{attrs:{prop:"creationDate",label:"创建时间"},scopedSlots:e._u([{key:"header",fn:function(){return[a("div",[a("span",[e._v("创建时间")]),e._v(" "),a("br"),e._v(" "),a("span",[e._v("修改时间")])])]},proxy:!0},{key:"default",fn:function(t){return[e._v("\n            "+e._s(t.row.creationDate)+"\n            "),a("br"),e._v("\n            "+e._s(t.row.updateTime)+"\n          ")]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",icon:"el-icon-document"},on:{click:function(a){return e.handleDetail(t.row)}}},[e._v("报价单")]),e._v(" "),a("el-button",{attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(a){return e.copyProductForm(t.row)}}},[e._v("复制")]),e._v(" "),a("el-dropdown",[a("span",{staticStyle:{"margin-left":"12px",cursor:"pointer",color:"#409EFF"}},[e._v("\n                更多"),a("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),e._v(" "),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",[a("el-button",{attrs:{type:"text",icon:"el-icon-time"},on:{click:function(a){return e.viewhHistory(t.row)}}},[e._v("历史记录")])],1),e._v(" "),a("el-dropdown-item",["1"===e.power||e.user===t.row.creator?a("el-button",{attrs:{type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.updateProductForm(t.row)}}},[e._v("修改主品")]):e._e()],1),e._v(" "),a("el-dropdown-item",["1"===e.power||e.user===t.row.creator?a("el-button",{attrs:{type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除主品")]):e._e()],1)],1)],1)]}}])})],1)],1),e._v(" "),a("el-dialog",{attrs:{id:"configDlg",title:"新增主品",visible:e.handleAddModal,"close-on-click-modal":!1,"show-close":!1,top:"12vh",width:"90%"},on:{"update:visible":function(t){e.handleAddModal=t},closed:e.handleMainClose}},[a("el-steps",{staticStyle:{margin:"12px 0 24px 0"},attrs:{active:e.stepIndex,"align-center":"",simple:"","finish-status":"success","process-status":"wait"}},[a("el-step",{attrs:{title:"主品配置",icon:"el-icon-collection"}}),e._v(" "),a("el-step",{attrs:{title:"物料配置",icon:"el-icon-box"}}),e._v(" "),a("el-step",{attrs:{title:"包装配置",icon:"el-icon-receiving"}}),e._v(" "),a("el-step",{attrs:{title:"工角配置",icon:"el-icon-timer"}}),e._v(" "),a("el-step",{attrs:{title:"运输配置",icon:"el-icon-truck"}})],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:1===e.stepIndex,expression:"stepIndex === 1"}]},[a("el-form",{ref:"productForm",attrs:{model:e.productFormData,rules:e.pr_rules}},[a("el-form-item",{attrs:{prop:"productName",label:"主品名称："}},[a("el-input",{attrs:{size:"small",placeholder:"请填写客户公司对应的主品名称",clearable:""},model:{value:e.productFormData.productName,callback:function(t){e.$set(e.productFormData,"productName",t)},expression:"productFormData.productName"}})],1),e._v(" "),a("el-form-item",{attrs:{prop:"productConnection",label:"客户公司："}},[a("el-input",{attrs:{size:"small",placeholder:"请填写客户公司",clearable:""},model:{value:e.productFormData.productConnection,callback:function(t){e.$set(e.productFormData,"productConnection",t)},expression:"productFormData.productConnection"}})],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"productFactory",label:"生产工厂："}},[a("el-radio-group",{attrs:{size:"medium"},model:{value:e.productFormData.productFactory,callback:function(t){e.$set(e.productFormData,"productFactory",t)},expression:"productFormData.productFactory"}},[a("el-radio",{attrs:{label:"迅安"}}),e._v(" "),a("el-radio",{attrs:{label:"知腾"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{prop:"productDetail",label:"主品描述："}},[a("el-input",{attrs:{size:"small",placeholder:"请填写产品详细信息",clearable:""},model:{value:e.productFormData.productDetail,callback:function(t){e.$set(e.productFormData,"productDetail",t)},expression:"productFormData.productDetail"}})],1)],1)],1),e._v(" "),a("el-form",{directives:[{name:"show",rawName:"v-show",value:2===e.stepIndex,expression:"stepIndex === 2"}],ref:"materialForm",attrs:{model:e.productFormData,rules:e.ma_rules}},[a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"productStandard",label:"标准：","label-width":"80px"}},[a("el-radio-group",{on:{input:e.selectStandard},model:{value:e.productFormData.productStandard,callback:function(t){e.$set(e.productFormData,"productStandard",t)},expression:"productFormData.productStandard"}},e._l(e.standard_options,function(e,t){return a("el-radio",{key:t+e,attrs:{label:e.label}})}),1)],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"productSuccession",label:"系列：","label-width":"80px"}},[a("el-radio-group",{on:{input:e.selectSuccession},model:{value:e.productFormData.productSuccession,callback:function(t){e.$set(e.productFormData,"productSuccession",t)},expression:"productFormData.productSuccession"}},e._l(e.successionList,function(e,t){return a("el-radio",{key:t,attrs:{label:e}})}),1)],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"productType",label:"型号：","label-width":"80px"}},[a("el-radio-group",{on:{input:e.selectType},model:{value:e.productFormData.productType,callback:function(t){e.$set(e.productFormData,"productType",t)},expression:"productFormData.productType"}},e._l(e.typeList,function(t){return a("el-radio",{key:t.sc_id,attrs:{label:t.uuid,disabled:!!e.productFormData.productType}},[e._v(e._s(t.sc_type))])}),1),e._v(" "),e.productFormData.productType?a("el-button",{staticStyle:{margin:"0 0 0 24px"},attrs:{type:"text"},on:{click:e.selectStandard}},[e._v("重置")]):e._e()],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"productItems",label:"材料：","label-width":"80px"}},[[a("el-form",{ref:"itemDataForm",attrs:{model:e.itemDataForm}},[a("el-form-item",{attrs:{prop:"out",label:"外层：","label-width":"80px"}},[a("el-checkbox-group",{on:{change:function(t){return e.handleBoxChange(t,"out")}},model:{value:e.itemDataForm.out,callback:function(t){e.$set(e.itemDataForm,"out",t)},expression:"itemDataForm.out"}},e._l(e.out_options,function(t,r){return a("el-checkbox",{key:r,attrs:{label:t,checked:!0}},[e._v(e._s(t.rm_material))])}),1)],1),e._v(" "),a("el-form-item",{attrs:{prop:"mid",label:"中层：","label-width":"80px"}},[a("el-checkbox-group",{on:{change:function(t){return e.handleBoxChange(t,"mid")}},model:{value:e.itemDataForm.mid,callback:function(t){e.$set(e.itemDataForm,"mid",t)},expression:"itemDataForm.mid"}},e._l(e.mid_options,function(t,r){return a("el-checkbox",{key:r,attrs:{label:t,checked:!1}},[e._v(e._s(t.rm_material))])}),1)],1),e._v(" "),a("el-form-item",{attrs:{prop:"in",label:"内层：","label-width":"80px"}},[a("el-checkbox-group",{on:{change:function(t){return e.handleBoxChange(t,"in")}},model:{value:e.itemDataForm.in,callback:function(t){e.$set(e.itemDataForm,"in",t)},expression:"itemDataForm.in"}},e._l(e.in_options,function(t,r){return a("el-checkbox",{key:r,attrs:{label:t,checked:!0}},[e._v(e._s(t.rm_material))])}),1)],1),e._v(" "),a("el-form-item",{attrs:{prop:"nasal",label:"鼻夹：","label-width":"80px"}},[a("el-checkbox-group",{on:{change:function(t){return e.handleBoxChange(t,"nasal")}},model:{value:e.itemDataForm.nasal,callback:function(t){e.$set(e.itemDataForm,"nasal",t)},expression:"itemDataForm.nasal"}},e._l(e.nasal_options,function(t,r){return a("el-checkbox",{key:r,attrs:{label:t,checked:!0}},[e._v(e._s(t.rm_material))])}),1)],1),e._v(" "),a("el-form-item",{attrs:{prop:"binding",label:"松紧带：","label-width":"80px"}},[a("el-checkbox-group",{on:{change:function(t){return e.handleBoxChange(t,"binding")}},model:{value:e.itemDataForm.binding,callback:function(t){e.$set(e.itemDataForm,"binding",t)},expression:"itemDataForm.binding"}},e._l(e.binding_options,function(t,r){return a("el-checkbox",{key:r,attrs:{label:t,checked:!0}},[e._v(e._s(t.rm_material))])}),1)],1),e._v(" "),a("el-form-item",{attrs:{prop:"foam",label:"鼻垫：","label-width":"80px"}},[a("el-checkbox-group",{on:{change:function(t){return e.handleBoxChange(t,"foam")}},model:{value:e.itemDataForm.foam,callback:function(t){e.$set(e.itemDataForm,"foam",t)},expression:"itemDataForm.foam"}},e._l(e.foam_options,function(t,r){return a("el-checkbox",{key:r,attrs:{label:t,checked:!0}},[e._v(e._s(t.rm_material))])}),1)],1),e._v(" "),a("el-form-item",{attrs:{prop:"others",label:"其它：","label-width":"80px"}},[a("el-checkbox-group",{on:{change:function(t){return e.handleBoxChange(t,"others")}},model:{value:e.itemDataForm.others,callback:function(t){e.$set(e.itemDataForm,"others",t)},expression:"itemDataForm.others"}},e._l(e.others_options,function(t,r){return a("el-checkbox",{key:r,attrs:{label:t,checked:!0}},[e._v(e._s(t.rm_material))])}),1)],1)],1)]],2)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:3==e.stepIndex,expression:"stepIndex == 3"}]},[a("el-form",{staticStyle:{"text-align":"left","white-space":"nowrap","margin-left":"12px"},attrs:{inline:""}},[a("el-form-item",{attrs:{label:"包装方式："}},[a("el-radio-group",{attrs:{size:"mini"},on:{input:e.onTypeChange},model:{value:e.packageType,callback:function(t){e.packageType=t},expression:"packageType"}},[a("el-radio-button",{attrs:{label:"彩盒"}}),e._v(" "),a("el-radio-button",{attrs:{label:"泡壳"}}),e._v(" "),a("el-radio-button",{attrs:{label:"袋装"}})],1)],1)],1),e._v(" "),"彩盒"===e.packageType?a("el-form",{ref:"packageForm",staticStyle:{"text-align":"left"},attrs:{model:e.productFormData,rules:e.pk_rules,inline:""}},[a("el-form-item",{attrs:{prop:"pcsCount",label:"包装规格："}},[a("el-input-number",{staticStyle:{width:"60%"},attrs:{size:"mini",min:1,"controls-position":"right",clearable:""},model:{value:e.productFormData.pcsCount,callback:function(t){e.$set(e.productFormData,"pcsCount",t)},expression:"productFormData.pcsCount"}}),e._v(" PCS / 袋，\n          ")],1),e._v(" "),a("el-form-item",{attrs:{prop:"bagCount"}},[a("el-input-number",{staticStyle:{width:"60%"},attrs:{size:"mini",min:1,"controls-position":"right",clearable:""},model:{value:e.productFormData.bagCount,callback:function(t){e.$set(e.productFormData,"bagCount",t)},expression:"productFormData.bagCount"}}),e._v(" 袋 / 盒，\n          ")],1),e._v(" "),a("el-form-item",{attrs:{prop:"boxCount"}},[a("el-input-number",{staticStyle:{width:"60%"},attrs:{size:"mini",min:1,"controls-position":"right",clearable:""},model:{value:e.productFormData.boxCount,callback:function(t){e.$set(e.productFormData,"boxCount",t)},expression:"productFormData.boxCount"}}),e._v(" 盒 / 箱。\n          ")],1)],1):e._e(),e._v(" "),"泡壳"===e.packageType?a("el-form",{ref:"packageForm",staticStyle:{"text-align":"left"},attrs:{model:e.productFormData,rules:e.pk_rules,inline:""}},[a("el-form-item",{attrs:{prop:"pcsCount",label:"包装规格："}},[a("el-input-number",{staticStyle:{width:"60%"},attrs:{size:"mini",min:1,"controls-position":"right",clearable:""},model:{value:e.productFormData.pcsCount,callback:function(t){e.$set(e.productFormData,"pcsCount",t)},expression:"productFormData.pcsCount"}}),e._v(" PCS / 泡壳，\n          ")],1),e._v(" "),a("el-form-item",{attrs:{prop:"boxCount"}},[a("el-input-number",{staticStyle:{width:"60%"},attrs:{size:"mini",min:1,"controls-position":"right",clearable:""},model:{value:e.productFormData.boxCount,callback:function(t){e.$set(e.productFormData,"boxCount",t)},expression:"productFormData.boxCount"}}),e._v(" 泡壳 / 箱。\n          ")],1)],1):e._e(),e._v(" "),"袋装"===e.packageType?a("el-form",{ref:"packageForm",staticStyle:{"text-align":"left"},attrs:{model:e.productFormData,rules:e.pk_rules,inline:""}},[a("el-form-item",{attrs:{prop:"pcsCount",label:"包装规格："}},[a("el-input-number",{staticStyle:{width:"60%"},attrs:{size:"mini",min:1,"controls-position":"right",clearable:""},model:{value:e.productFormData.pcsCount,callback:function(t){e.$set(e.productFormData,"pcsCount",t)},expression:"productFormData.pcsCount"}}),e._v(" PCS / 袋，\n          ")],1),e._v(" "),a("el-form-item",{attrs:{prop:"boxCount"}},[a("el-input-number",{staticStyle:{width:"60%"},attrs:{size:"mini",min:1,"controls-position":"right",clearable:""},model:{value:e.productFormData.boxCount,callback:function(t){e.$set(e.productFormData,"boxCount",t)},expression:"productFormData.boxCount"}}),e._v(" 袋 / 箱。\n          ")],1)],1):e._e(),e._v(" "),a("el-container",{directives:[{name:"show",rawName:"v-show",value:e.packageType,expression:"packageType"}],staticStyle:{height:"500px"}},[a("el-aside",{attrs:{width:"200px"}},[a("el-input",{attrs:{"prefix-icon":"el-icon-search",placeholder:"输入关键字查询",size:"mini",clearable:""},model:{value:e.filterText,callback:function(t){e.filterText=t},expression:"filterText"}},[a("el-button",{attrs:{slot:"append",disabled:""},on:{click:function(t){e.drawer=!0}},slot:"append"},[e._v("新建")])],1),e._v(" "),a("el-tree",{ref:"tree",staticClass:"filter-tree",attrs:{data:e.pkTreeData,props:e.defaultProps,"filter-node-method":e.filterNode,"node-key":"packid","show-checkbox":"","default-expand-all":!1},on:{check:e.subRoleTreeCheck}})],1),e._v(" "),a("el-main",[a("div",{staticStyle:{"text-align":"left"}},["彩盒"===e.packageType?a("p",[e._v("========"),a("b",{staticStyle:{color:"#1989fa"}},[e._v("彩盒")]),e._v("基本包装包含："),a("b",{staticStyle:{color:"#1989fa"}},[e._v("胶袋、彩盒、合格证、外箱、封箱胶带")]),e._v("等物料，提交前请确认是否完整========")]):e._e(),e._v(" "),"泡壳"===e.packageType?a("p",[e._v("========"),a("b",{staticStyle:{color:"#1989fa"}},[e._v("泡壳")]),e._v("基本包装包含："),a("b",{staticStyle:{color:"#1989fa"}},[e._v("泡壳、彩/吊卡、说明书、外箱、封箱胶带")]),e._v("等物料，提交前请确认是否完整========")]):e._e(),e._v(" "),"袋装"===e.packageType?a("p",[e._v("========"),a("b",{staticStyle:{color:"#1989fa"}},[e._v("袋装")]),e._v("基本包装包含："),a("b",{staticStyle:{color:"#1989fa"}},[e._v("胶袋、彩/吊卡、说明书、外箱、封箱胶带")]),e._v("等物料，提交前请确认是否完整========")]):e._e(),e._v(" "),e._l(e.packageFormData,function(t,r){return a("p",{key:t.packid},[a("span",{staticStyle:{width:"29%",display:"inline-block"}},[e._v(e._s(r+1+"、名称："+t.pk_material))]),e._v(" "),a("span",{staticStyle:{width:"18%",display:"inline-block"}},[e._v("尺寸："+e._s(t.pk_length?t.pk_length+'" x '+t.pk_width+'" x '+t.pk_height+'"':"/"))]),e._v(" "),a("span",{staticStyle:{width:"15%",display:"inline-block"}},[e._v("单价：\n                  "),a("el-input-number",{staticStyle:{width:"60%"},attrs:{size:"mini","controls-position":"right",clearable:""},on:{change:function(a){return e.calculatePackage(t)}},model:{value:t.pk_price,callback:function(a){e.$set(t,"pk_price",a)},expression:"item.pk_price"}})],1),e._v(" "),a("span",{staticStyle:{width:"20%",display:"inline-block"}},[e._v("用量："),a("el-input-number",{staticStyle:{width:"60%"},attrs:{size:"mini","controls-position":"right",clearable:""},on:{change:function(a){return e.calculatePackage(t)}},model:{value:t.pk_consumption,callback:function(a){e.$set(t,"pk_consumption",a)},expression:"item.pk_consumption"}})],1),e._v(" "),a("span",{staticStyle:{width:"14%",display:"inline-block"}},[e._v("金额："+e._s(t.pk_amounts))])])})],2)])],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:4==e.stepIndex,expression:"stepIndex == 4"}]},[a("el-form",{ref:"LCForm",staticStyle:{width:"70%",margin:"0px 0px 24px",display:"inline-flex","text-align":"left"},attrs:{model:e.LCFormData,rules:e.lcFormRules,size:"mini",inline:""}},[a("el-form-item",{attrs:{prop:"lcData"}},[a("el-radio-group",{model:{value:e.LCFormData.lcData,callback:function(t){e.$set(e.LCFormData,"lcData",t)},expression:"LCFormData.lcData"}},e._l(e.laborCostList,function(t){return a("el-radio",{key:t.lc_id,attrs:{label:t}},[e._v(e._s(t.lc_name)+" 【"+e._s(t.lc_factory)+"】 单价：\n                "),a("el-input-number",{staticStyle:{width:"30%"},attrs:{size:"mini","controls-position":"right",clearable:""},model:{value:t.lc_price,callback:function(a){e.$set(t,"lc_price",a)},expression:"op.lc_price"}})],1)}),1)],1)],1),e._v(" "),a("el-form",{ref:"PKLCTableForm",staticStyle:{width:"70%",margin:"0px 0px 24px",display:"inline-flex","text-align":"left"},attrs:{model:e.PKLCTableFormData,rules:e.pklcRules,size:"mini",inline:""}},[a("el-form-item",{attrs:{prop:"lc_name",label:"包装统一工价："}}),e._v(" "),a("el-form-item",{attrs:{prop:"lc_price",label:"单价(RMB)："}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请填写",step:1,precision:5,clearable:""},model:{value:e.PKLCTableFormData.lc_price,callback:function(t){e.$set(e.PKLCTableFormData,"lc_price",t)},expression:"PKLCTableFormData.lc_price"}})],1),e._v(" "),a("el-form-item",[e._v("\n            (包装统一报价，含全部包装工角单价)\n          ")])],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:5==e.stepIndex,expression:"stepIndex == 5"}]},[a("div",{staticStyle:{overflow:"hidden"}},[a("el-form",{ref:"freightForm",attrs:{id:"shipping",model:e.freightFormData,rules:e.fr_rules}},[a("el-form-item",{staticStyle:{"text-align":"left",width:"80%"},attrs:{prop:"goodsCount",label:"订单数量：","label-width":"100px"}},[a("el-input-number",{attrs:{size:"mini",placeholder:"不确定订单数量可输入0","controls-position":"right"},on:{blur:function(t){return e.handleCalculateCTN(e.freightFormData,e.productFormData,e.packageFormData)}},model:{value:e.freightFormData.goodsCount,callback:function(t){e.$set(e.freightFormData,"goodsCount",t)},expression:"freightFormData.goodsCount"}}),e._v(" "),a("span",{staticClass:"tips"},[e._v('不确定订单数量可输入"0"，整柜默认按950材积、散货按2000个彩盒进行计算')])],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left",width:"80%"},attrs:{prop:"ctn",label:"CTN：","label-width":"100px"}},[a("el-input-number",{attrs:{size:"mini",placeholder:"CTN如果不满一箱按一箱进行计算","controls-position":"right"},model:{value:e.freightFormData.ctn,callback:function(t){e.$set(e.freightFormData,"ctn",t)},expression:"freightFormData.ctn"}}),e._v(" "),a("span",{staticClass:"tips"},[e._v("CTN如果不满一箱按一箱进行计算")])],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left",width:"80%"},attrs:{prop:"cmb",label:"CBM：","label-width":"100px"}},[a("el-input-number",{attrs:{size:"small",placeholder:"请输入货柜体积","controls-position":"right"},model:{value:e.freightFormData.cmb,callback:function(t){e.$set(e.freightFormData,"cmb",t)},expression:"freightFormData.cmb"}})],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"sales_type",label:"销售类型：","label-width":"100px"}},[a("el-radio-group",{model:{value:e.freightFormData.sales_type,callback:function(t){e.$set(e.freightFormData,"sales_type",t)},expression:"freightFormData.sales_type"}},[a("el-radio",{attrs:{label:"外销"}}),e._v(" "),a("el-radio",{attrs:{label:"内销"}})],1)],1),e._v(" "),e.freightFormData.sales_type&&"外销"==e.freightFormData.sales_type?[a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"factory",label:"出货工厂：","label-width":"100px"}},[a("el-radio-group",{model:{value:e.freightFormData.factory,callback:function(t){e.$set(e.freightFormData,"factory",t)},expression:"freightFormData.factory"}},[a("el-radio",{attrs:{label:"知腾"}}),e._v(" "),a("el-radio",{attrs:{label:"迅安"}})],1)],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"port",label:"出货港口：","label-width":"100px"}},[a("el-radio-group",{model:{value:e.freightFormData.port,callback:function(t){e.$set(e.freightFormData,"port",t)},expression:"freightFormData.port"}},[a("el-radio",{attrs:{label:"上海"}}),e._v(" "),a("el-radio",{attrs:{label:"武汉"}}),e._v(" "),a("el-radio",{attrs:{label:"深圳"}})],1)],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"terms",label:"贸易条件：","label-width":"100px"}},[a("el-radio-group",{on:{change:e.onTermsChange},model:{value:e.freightFormData.terms,callback:function(t){e.$set(e.freightFormData,"terms",t)},expression:"freightFormData.terms"}},[a("el-radio",{attrs:{label:"FOB"}}),e._v(" "),a("el-radio",{attrs:{label:"Ex Works"}}),e._v(" "),a("el-radio",{attrs:{label:"To Door"}}),e._v(" "),a("el-radio",{attrs:{label:"CIF"}})],1)],1),e._v(" "),e.freightFormData.terms&&"Ex Works"===e.freightFormData.terms?[a("el-form-item",{staticStyle:{"text-align":"left",width:"49%"},attrs:{prop:"expense",label:"运费(RMB):","label-width":"100px"}},[e._v("\n                  0.00 (Exit Work不产生运费)\n                ")])]:e._e(),e._v(" "),e.freightFormData.terms&&"CIF"===e.freightFormData.terms?[a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"shippingType",label:"出货类别：","label-width":"100px"}},[a("el-radio-group",{model:{value:e.freightFormData.shippingType,callback:function(t){e.$set(e.freightFormData,"shippingType",t)},expression:"freightFormData.shippingType"}},[a("el-radio",{attrs:{label:"整柜"}}),e._v(" "),"武汉"!==e.freightFormData.port?a("el-radio",{attrs:{label:"散货"}}):e._e()],1)],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left",width:"49%"},attrs:{prop:"expense",label:"运费(RMB):","label-width":"100px"}},[a("el-input-number",{attrs:{size:"small",placeholder:"请填写运费","controls-position":"right"},model:{value:e.freightFormData.expense,callback:function(t){e.$set(e.freightFormData,"expense",t)},expression:"freightFormData.expense"}}),e._v(" "),a("span",[e._v("(CIF包含运费及报关费)")])],1)]:e._e(),e._v(" "),e.freightFormData.terms&&"To Door"===e.freightFormData.terms?[a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"shippingType",label:"出货类别：","label-width":"100px"}},[a("el-radio-group",{model:{value:e.freightFormData.shippingType,callback:function(t){e.$set(e.freightFormData,"shippingType",t)},expression:"freightFormData.shippingType"}},[a("el-radio",{attrs:{label:"整柜"}}),e._v(" "),"武汉"!==e.freightFormData.port?a("el-radio",{attrs:{label:"散货"}}):e._e()],1)],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left",width:"49%"},attrs:{prop:"expense",label:"运费(RMB):","label-width":"100px"}},[a("el-input-number",{attrs:{size:"small",placeholder:"请填写运费","controls-position":"right"},model:{value:e.freightFormData.expense,callback:function(t){e.$set(e.freightFormData,"expense",t)},expression:"freightFormData.expense"}})],1)]:e._e(),e._v(" "),e.freightFormData.terms&&"FOB"===e.freightFormData.terms?[a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"shippingType",label:"出货类别：","label-width":"100px"}},[a("el-radio-group",{model:{value:e.freightFormData.shippingType,callback:function(t){e.$set(e.freightFormData,"shippingType",t)},expression:"freightFormData.shippingType"}},[a("el-radio",{attrs:{label:"整柜"}}),e._v(" "),"武汉"!==e.freightFormData.port?a("el-radio",{attrs:{label:"散货"}}):e._e()],1)],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left",width:"49%"},attrs:{prop:"expense",label:"运输综合费用(RMB)：","label-width":"156px"}},[a("el-input-number",{attrs:{size:"small",placeholder:"请输入金额","controls-position":"right"},model:{value:e.freightFormData.expense,callback:function(t){e.$set(e.freightFormData,"expense",t)},expression:"freightFormData.expense"}})],1)]:e._e()]:e._e(),e._v(" "),e.freightFormData.sales_type&&"内销"==e.freightFormData.sales_type?[a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"factory",label:"出货工厂：","label-width":"100px"}},[a("el-radio-group",{model:{value:e.freightFormData.factory,callback:function(t){e.$set(e.freightFormData,"factory",t)},expression:"freightFormData.factory"}},[a("el-radio",{attrs:{label:"知腾"}}),e._v(" "),a("el-radio",{attrs:{label:"迅安"}})],1)],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"terms",label:"贸易条件：","label-width":"100px"}},[a("el-radio-group",{on:{change:e.onTermsChange},model:{value:e.freightFormData.terms,callback:function(t){e.$set(e.freightFormData,"terms",t)},expression:"freightFormData.terms"}},[a("el-radio",{attrs:{label:"Ex Works"}}),e._v(" "),a("el-radio",{attrs:{label:"To Door"}})],1)],1),e._v(" "),e.freightFormData.terms&&"Ex Works"===e.freightFormData.terms?[a("el-form-item",{staticStyle:{"text-align":"left",width:"49%"},attrs:{prop:"expense",label:"运费(RMB):","label-width":"100px"}},[e._v("\n                  0.00 (Exit Work不产生运费)\n                ")])]:e._e(),e._v(" "),e.freightFormData.terms&&"To Door"===e.freightFormData.terms?[a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"shippingType",label:"出货类别：","label-width":"100px"}},[a("el-radio-group",{model:{value:e.freightFormData.shippingType,callback:function(t){e.$set(e.freightFormData,"shippingType",t)},expression:"freightFormData.shippingType"}},[a("el-radio",{attrs:{label:"整柜"}}),e._v(" "),a("el-radio",{attrs:{label:"散货"}})],1)],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left",width:"49%"},attrs:{prop:"expense",label:"运费(RMB):","label-width":"100px"}},[a("el-input-number",{attrs:{size:"small",placeholder:"请填写运费","controls-position":"right"},model:{value:e.freightFormData.expense,callback:function(t){e.$set(e.freightFormData,"expense",t)},expression:"freightFormData.expense"}})],1)]:e._e()]:e._e()],2),e._v(" "),a("div",{staticStyle:{width:"30%",float:"left","text-align":"left"}},[a("h3",{staticStyle:{margin:"0 0 36px 0"}},[e._v("货品信息")]),e._v(" "),a("p",{staticStyle:{margin:"0 0 36px 0"}},[e._v("单箱材积(cuft)："+e._s(e.cuft))]),e._v(" "),a("p",{staticStyle:{margin:"0 0 36px 0"}},[e._v("货品总体积(CBM)："+e._s(e.CBM_ALL))]),e._v(" "),a("p",{staticStyle:{margin:"0 0 36px 0"}},[e._v("20尺货柜容量(PCS)："+e._s(e.CTN_20INCHI))]),e._v(" "),a("p",{staticStyle:{margin:"0 0 36px 0"}},[e._v("40尺货柜容量(PCS)："+e._s(e.CTN_40INCHI))]),e._v(" "),"彩盒"===e.packageType?a("p",{staticStyle:{color:"#409EFF",margin:"0 0 36px 0"}},[e._v(e._s(e.alertSTR))]):e._e(),e._v(" "),e.freightFormData.goodsCount>e.CTN_20INCHI?a("p",{staticStyle:{color:"#F56C6C",margin:"0 0 36px 0"}},[e._v("注意：当前订单数量已超过20尺货柜最大容量，请选择更大尺寸的货柜！")]):e._e()])],1)]),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:e.callOf}},[e._v("取 消")]),e._v(" "),1!=e.stepIndex?a("el-button",{attrs:{size:"mini"},on:{click:function(t){e.stepIndex--}}},[e._v("上一步")]):e._e(),e._v(" "),5!=e.stepIndex?a("el-button",{attrs:{size:"mini"},on:{click:e.handleNextStep}},[e._v("下一步")]):e._e(),e._v(" "),5==e.stepIndex?a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.postAddMain("freightForm")}}},[e._v("保 存")]):e._e()],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"《"+e.editTitle+"》修改历史记录",visible:e.historyModal,width:"90%"},on:{"update:visible":function(t){e.historyModal=t}}},[a("el-collapse",{model:{value:e.activeNames,callback:function(t){e.activeNames=t},expression:"activeNames"}},[a("el-collapse-item",{attrs:{title:"物料",name:"rm"}},[a("el-table",{staticClass:"list-table",staticStyle:{width:"100%"},attrs:{data:e.historyTableData.rawMaterialTableData,"header-cell-style":{backgroundColor:"rgb(217, 236, 255)"},"row-style":{backgroundColor:"rgb(236, 245, 255)"},"max-height":"540"}},[a("el-table-column",{attrs:{label:"#",type:"index",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{label:"品号",align:"left",prop:"rm_number"}}),e._v(" "),a("el-table-column",{attrs:{label:"品名",align:"left",prop:"rm_material"}}),e._v(" "),a("el-table-column",{attrs:{label:"厂商",align:"center",prop:"rm_supplier"}}),e._v(" "),a("el-table-column",{attrs:{label:"历史单价",align:"center",prop:"rm_price"}}),e._v(" "),a("el-table-column",{attrs:{label:"单位",align:"center",prop:"rm_unit"}}),e._v(" "),a("el-table-column",{attrs:{label:"更新时间",align:"center",prop:"updateTime",sortable:""}}),e._v(" "),a("el-table-column",{attrs:{label:"更新人员",align:"center",prop:"updatePersonnel"}})],1)],1),e._v(" "),a("el-collapse-item",{attrs:{title:"包装",name:"pk"}},[a("el-table",{staticClass:"list-table",staticStyle:{width:"100%"},attrs:{data:e.historyTableData.packageTableData,"header-cell-style":{backgroundColor:"rgb(250, 236, 216)"},"row-style":{backgroundColor:"rgb(253, 246, 236)"},"max-height":"540"}},[a("el-table-column",{attrs:{label:"#",type:"index",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{label:"品号",align:"left",prop:"pk_number"}}),e._v(" "),a("el-table-column",{attrs:{label:"品名",align:"left",prop:"pk_material"}}),e._v(" "),a("el-table-column",{attrs:{label:"厂商",align:"center",prop:"pk_supplier"}}),e._v(" "),a("el-table-column",{attrs:{label:"历史单价",align:"center",prop:"pk_price"}}),e._v(" "),a("el-table-column",{attrs:{label:"单位",align:"center",prop:"pk_unit"}}),e._v(" "),a("el-table-column",{attrs:{label:"更新时间",align:"center",prop:"updateTime",sortable:""}}),e._v(" "),a("el-table-column",{attrs:{label:"更新人员",align:"center",prop:"updatePersonnel"}})],1)],1),e._v(" "),a("el-collapse-item",{staticClass:"lc-collapse",attrs:{title:"工角",name:"lc"}},[a("el-table",{staticClass:"list-table",staticStyle:{width:"100%"},attrs:{data:e.historyTableData.LaborCostTableData,"header-cell-style":{backgroundColor:"rgb(225, 243, 216)"},"row-style":{backgroundColor:"rgb(240, 249, 235)"},"max-height":"540"}},[a("el-table-column",{attrs:{label:"#",type:"index",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{label:"品名",align:"left",prop:"lc_name"}}),e._v(" "),a("el-table-column",{attrs:{label:"历史单价",align:"center",prop:"lc_price"}}),e._v(" "),a("el-table-column",{attrs:{label:"单位",align:"center",prop:"lc_unit"}}),e._v(" "),a("el-table-column",{attrs:{label:"更新时间",align:"center",prop:"updateTime",sortable:""}}),e._v(" "),a("el-table-column",{attrs:{label:"更新人员",align:"center",prop:"updatePersonnel"}})],1)],1)],1),e._v(" "),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:function(t){e.historyModal=!1}}},[e._v("关 闭")])],1)],1),e._v(" "),a("el-dialog",{attrs:{width:"70%",visible:e.handleMutiplelModal,"close-on-click-modal":!1,"show-close":!1,top:"10vh"},on:{"update:visible":function(t){e.handleMutiplelModal=t},closed:e.handleMutiplelModalClose}},[a("el-button",{staticStyle:{position:"absolute",top:"80px",right:"90px"},attrs:{type:"primary",size:"mini",icon:"el-icon-download",plain:"",round:""},on:{click:e.exportDBPNG}},[e._v("导出")]),e._v(" "),a("div",{ref:"htmlDBDom",staticStyle:{padding:"24px 0"}},[a("div",{staticStyle:{padding:"0 0 24px 0","line-height":"24px","font-size":"18px",color:"#303133"}},[e._v("价格对比")]),e._v(" "),a("div",{staticClass:"db-collapse"},[a("el-row",{staticStyle:{"margin-bottom":"24px"}},[a("span",{staticStyle:{"margin-left":"54px",color:"rgb(25, 137, 250)"}},[e._v("******请注意：此处外销包含3‰保险，内销包含13%税率******")])]),e._v(" "),a("el-row",{staticStyle:{"margin-bottom":"24px"}},[a("el-col",{attrs:{offset:1,span:10}},[a("span",{staticStyle:{width:"80px"}},[e._v("币种：")]),e._v(" "),a("el-select",{staticStyle:{width:"70%"},attrs:{size:"mini"},on:{change:e.handleDbCTchange},model:{value:e.db_tt_currencyType,callback:function(t){e.db_tt_currencyType=t},expression:"db_tt_currencyType"}},e._l(e.ct_options,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-col",{attrs:{offset:1,span:10}},[a("span",{staticStyle:{width:"80px"}},[e._v("汇率：")]),e._v(" "),a("el-input-number",{staticStyle:{width:"70%"},attrs:{size:"mini",min:0,"controls-position":"right"},on:{change:e.handleDbRateChange},model:{value:e.db_totalRate,callback:function(t){e.db_totalRate=t},expression:"db_totalRate"}})],1)],1)],1),e._v(" "),a("el-row",[a("el-col",{attrs:{span:2,offset:1}},[a("div",{staticClass:"col",staticStyle:{"font-weight":"bold"}},[e._v("主品名称：")]),e._v(" "),a("div",{staticClass:"col",staticStyle:{"font-weight":"bold"}},[e._v("包装信息：")]),e._v(" "),a("div",{staticClass:"col",staticStyle:{"font-weight":"bold"}},[e._v("物料费用：")]),e._v(" "),a("div",{staticClass:"col",staticStyle:{"font-weight":"bold"}},[e._v("包装费用：")]),e._v(" "),a("div",{staticClass:"col",staticStyle:{"font-weight":"bold"}},[e._v("人工费用：")]),e._v(" "),a("div",{staticClass:"col",staticStyle:{"font-weight":"bold"}},[e._v("运输综合费用：")]),e._v(" "),a("div",{staticClass:"col",staticStyle:{"font-weight":"bold"}},[e._v("成本：")]),e._v(" "),a("div",{staticClass:"col",staticStyle:{"font-weight":"bold"}},[e._v("综合成本：")]),e._v(" "),a("div",{staticClass:"col",staticStyle:{"font-weight":"bold"}},[e._v("利润5%：")]),e._v(" "),a("div",{staticClass:"col",staticStyle:{"font-weight":"bold"}},[e._v("利润10%：")]),e._v(" "),a("div",{staticClass:"col",staticStyle:{"font-weight":"bold"}},[e._v("利润15%：")]),e._v(" "),a("div",{staticClass:"col",staticStyle:{"font-weight":"bold"}},[e._v("利润20%：")]),e._v(" "),a("div",{staticClass:"col",staticStyle:{"font-weight":"bold"}},[e._v("利润25%：")]),e._v(" "),a("div",{staticClass:"col",staticStyle:{"font-weight":"bold"}},[e._v("利润30%：")])]),e._v(" "),e._l(e.contrastData,function(t,r){return a("el-col",{key:r,class:t.styleText,attrs:{span:e.dynamicSpan}},[a("div",{staticClass:"col",staticStyle:{"font-weight":"bold","font-size":"16px","text-align":"center"}},[e._v(e._s(t.productName))]),e._v(" "),a("el-divider"),e._v(" "),a("div",{staticClass:"col",staticStyle:{"text-align":"center"}},[e._v(e._s(t.packageMsg))]),e._v(" "),a("el-divider"),e._v(" "),a("div",{staticClass:"col",staticStyle:{"text-align":"center"}},[e._v(e._s(t.rm_sum))]),e._v(" "),a("el-divider"),e._v(" "),a("div",{staticClass:"col",staticStyle:{"text-align":"center"}},[e._v(e._s(t.pk_sum))]),e._v(" "),a("el-divider"),e._v(" "),a("div",{staticClass:"col",staticStyle:{"text-align":"center"}},[e._v(e._s(t.lc_sum))]),e._v(" "),a("el-divider"),e._v(" "),a("div",{staticClass:"col",staticStyle:{"text-align":"center"}},[e._v(e._s(t.fr_sum))]),e._v(" "),a("el-divider"),e._v(" "),a("div",{staticClass:"col",staticStyle:{"text-align":"center","font-weight":"bold",color:"#1989fa"}},[e._v(e._s(t.cost))]),e._v(" "),a("el-divider"),e._v(" "),a("div",{staticClass:"col",staticStyle:{"text-align":"center","font-weight":"bold"}},[e._v(e._s(t.total))]),e._v(" "),a("el-divider"),e._v(" "),a("div",{staticClass:"col",staticStyle:{"text-align":"center","font-weight":"bold"}},[e._v(e._s((t.total+.05*t.total).toFixed(5)))]),e._v(" "),a("el-divider"),e._v(" "),a("div",{staticClass:"col",staticStyle:{"text-align":"center","font-weight":"bold"}},[e._v(e._s((t.total+.1*t.total).toFixed(5)))]),e._v(" "),a("el-divider"),e._v(" "),a("div",{staticClass:"col",staticStyle:{"text-align":"center","font-weight":"bold"}},[e._v(e._s((t.total+.15*t.total).toFixed(5)))]),e._v(" "),a("el-divider"),e._v(" "),a("div",{staticClass:"col",staticStyle:{"text-align":"center","font-weight":"bold"}},[e._v(e._s((t.total+.2*t.total).toFixed(5)))]),e._v(" "),a("el-divider"),e._v(" "),a("div",{staticClass:"col",staticStyle:{"text-align":"center","font-weight":"bold"}},[e._v(e._s((t.total+.25*t.total).toFixed(5)))]),e._v(" "),a("el-divider"),e._v(" "),a("div",{staticClass:"col",staticStyle:{"text-align":"center","font-weight":"bold"}},[e._v(e._s((t.total+.3*t.total).toFixed(5)))])],1)})],2)],1),e._v(" "),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:e.handleMutiplelModalClose}},[e._v("关 闭")])],1)],1),e._v(" "),a("el-dialog",{attrs:{width:"90%",visible:e.handleDetailModal,"close-on-click-modal":!1,"show-close":!1,top:"10vh"},on:{"update:visible":function(t){e.handleDetailModal=t},closed:e.handleDetailModalClose}},[a("el-button",{staticStyle:{position:"absolute",top:"144px",right:"80px"},attrs:{type:"primary",size:"mini",icon:"el-icon-download",plain:"",round:""},on:{click:e.exportPNG}},[e._v("导出")]),e._v(" "),a("span",{staticStyle:{"font-size":"12px",color:"#409EFF",position:"absolute",top:"286px",right:"80px"}},[e._v("******提示：勾选物料和包装可剔除管销******")]),e._v(" "),a("div",{ref:"htmlDom",staticStyle:{padding:"0 20px 20px"},attrs:{id:"pdfDom"}},[a("div",{staticStyle:{padding:"24px 0","line-height":"24px","font-size":"18px",color:"#303133"}},[e._v("《"+e._s(e.editTitle)+"》报价单")]),e._v(" "),a("el-descriptions",{attrs:{size:"small",column:2}},[a("el-descriptions-item",{attrs:{label:"品号"}},[e._v(e._s(e.detailObjData.postForm.uuid))]),e._v(" "),a("el-descriptions-item",{attrs:{label:"品名"}},[e._v(e._s(e.detailObjData.postForm.productName))]),e._v(" "),a("el-descriptions-item",{attrs:{label:"创建人员"}},[e._v(e._s(e.detailObjData.postForm.creator))]),e._v(" "),a("el-descriptions-item",{attrs:{label:"创建时间"}},[e._v(e._s(e.detailObjData.postForm.creationDate))]),e._v(" "),a("el-descriptions-item",{attrs:{label:"生产工厂"}},[e._v(e._s(e.detailObjData.postForm.productFactory))]),e._v(" "),a("el-descriptions-item",{attrs:{label:"客户公司"}},[e._v(e._s(e.detailObjData.postForm.productConnection))]),e._v(" "),a("el-descriptions-item",{attrs:{label:"订单数量"}},[e._v(e._s(e.detailObjData.freightTableData[0].goodsCount)+"PCS")]),e._v(" "),a("el-descriptions-item",{attrs:{label:"包装信息"}},[e._v(e._s("每袋"+e.detailObjData.postForm.pcsCount+"pcs、每盒"+e.detailObjData.postForm.bagCount+"袋、每箱"+e.detailObjData.postForm.boxCount+"盒"))]),e._v(" "),a("el-descriptions-item",{attrs:{label:"销售类型"}},[e._v(e._s(e.detailObjData.freightTableData[0].sales_type))]),e._v(" "),a("el-descriptions-item",{attrs:{label:"产品说明"}},[e._v(e._s(e.detailObjData.postForm.productDetail))])],1),e._v(" "),a("el-collapse",{model:{value:e.activeNames,callback:function(t){e.activeNames=t},expression:"activeNames"}},[a("el-collapse-item",{attrs:{title:"物料合计(RMB)：    "+e.rm_sum,name:"rm"}},[a("el-table",{staticClass:"list-table",staticStyle:{width:"100%"},attrs:{data:e.detailObjData.rawMaterialTableData,"header-cell-style":{backgroundColor:"rgb(217, 236, 255)"},"row-style":{backgroundColor:"rgb(236, 245, 255)"},"show-summary":"","summary-method":e.getSummariesRM},on:{"selection-change":e.handleRMStatusChange}},[a("el-table-column",{attrs:{label:"#",type:"index",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{label:"品名",align:"left",prop:"rm_material"}}),e._v(" "),a("el-table-column",{attrs:{label:"厂商",align:"center",prop:"rm_supplier"}}),e._v(" "),a("el-table-column",{attrs:{label:"单价(RMB)",align:"center",prop:"rm_price"}}),e._v(" "),a("el-table-column",{attrs:{label:"单位",align:"center",prop:"rm_unit"}}),e._v(" "),a("el-table-column",{attrs:{label:"用量",align:"center",prop:"rm_consumption"}}),e._v(" "),a("el-table-column",{attrs:{label:"金额(RMB)",align:"center",prop:"rm_amounts",sortable:""}}),e._v(" "),a("el-table-column",{attrs:{type:"selection",width:"55",align:"center",label:"xxxx"}})],1)],1),e._v(" "),a("el-collapse-item",{attrs:{title:"包装费合计(RMB)：    "+e.pk_sum,name:"pk"}},[a("el-table",{staticClass:"list-table",staticStyle:{width:"100%"},attrs:{data:e.detailObjData.packageTableData,"header-cell-style":{backgroundColor:"rgb(250, 236, 216)"},"row-style":{backgroundColor:"rgb(253, 246, 236)"},"show-summary":"","summary-method":e.getSummariesPK},on:{"selection-change":e.handlePKStatusChange}},[a("el-table-column",{attrs:{label:"#",type:"index",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{label:"品名",align:"left",prop:"pk_material"}}),e._v(" "),a("el-table-column",{attrs:{label:"厂商",align:"center",prop:"pk_supplier"}}),e._v(" "),a("el-table-column",{attrs:{label:"单价(RMB)",align:"center",prop:"pk_price"}}),e._v(" "),a("el-table-column",{attrs:{label:"原价(RMB)",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return e._l(e.FilterPackageArr,function(r,o){return a("span",{key:o,staticStyle:{color:"#F56C6C"}},[e._v("\n                      "+e._s(t.row.pk_number===r.pk_number&&t.row.pk_price!==r.pk_price?r.pk_price:"")+"\n                    ")])})}}])}),e._v(" "),a("el-table-column",{attrs:{label:"单位",align:"center",prop:"pk_unit"}}),e._v(" "),a("el-table-column",{attrs:{label:"用量",align:"center",prop:"pk_consumption"}}),e._v(" "),a("el-table-column",{attrs:{label:"金额(RMB)",align:"center",prop:"pk_amounts"}}),e._v(" "),a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}})],1)],1),e._v(" "),a("el-collapse-item",{staticClass:"lc-collapse",attrs:{title:"人工费合计(RMB)：    "+e.lc_sum,name:"lc"}},[a("el-table",{staticClass:"list-table",staticStyle:{width:"100%"},attrs:{data:e.detailObjData.LaborCostTableData,"header-cell-style":{backgroundColor:"rgb(225, 243, 216)"},"row-style":{backgroundColor:"rgb(240, 249, 235)"},"show-summary":"","summary-method":e.getSummariesLC}},[a("el-table-column",{attrs:{label:"#",type:"index",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{label:"工角名称",align:"left",prop:"lc_name"}}),e._v(" "),a("el-table-column",{attrs:{label:"所属工厂",align:"left",prop:"lc_factory"}}),e._v(" "),a("el-table-column",{attrs:{label:"单价(RMB)",align:"center",prop:"lc_price"}}),e._v(" "),a("el-table-column",{attrs:{label:"金额(RMB)",align:"center",prop:"lc_amounts"}})],1),e._v(" "),a("div",{staticClass:"lc-percent"},[a("span",[e._v("人工费百分比：")]),e._v(" "),a("el-input-number",{staticStyle:{width:"50%"},attrs:{size:"mini",min:0,max:300,"controls-position":"right"},on:{change:e.getSummariesLC},model:{value:e.lc_percentage,callback:function(t){e.lc_percentage=t},expression:"lc_percentage"}}),e._v("\n              %\n            ")],1)],1),e._v(" "),a("el-collapse-item",{attrs:{id:"freightCSS",title:"运输综合费用(RMB)：    "+e.fr_sum,name:"fr"}},[a("el-descriptions",{attrs:{size:"small",column:3}},["外销"==e.isDomesticSales?a("el-descriptions-item",{attrs:{label:"港口"}},[e._v(e._s("从"+e.detailObjData.freightTableData[0].factory+"到"+e.detailObjData.freightTableData[0].port))]):e._e(),e._v(" "),"内销"==e.isDomesticSales?a("el-descriptions-item",{attrs:{label:"出货工厂"}},[e._v(e._s(e.detailObjData.postForm.productFactory))]):e._e(),e._v(" "),a("el-descriptions-item",{attrs:{label:"订单数量"}},[e._v(e._s(e.detailObjData.freightTableData[0].goodsCount)+"PCS")]),e._v(" "),a("el-descriptions-item",{attrs:{label:"贸易条件"}},[e._v(e._s(e.detailObjData.freightTableData[0].terms))]),e._v(" "),e.detailObjData.freightTableData[0].shippingType?a("el-descriptions-item",{attrs:{label:"出货类别"}},[e._v(e._s(e.detailObjData.freightTableData[0].shippingType))]):e._e(),e._v(" "),e.detailObjData.freightTableData[0].cmb?a("el-descriptions-item",{attrs:{label:"货柜体积"}},[e._v(e._s(e.detailObjData.freightTableData[0].cmb+"立方米"))]):e._e(),e._v(" "),e.detailObjData.freightTableData[0].ctn?a("el-descriptions-item",{attrs:{label:"CTN"}},[e._v(e._s(e.detailObjData.freightTableData[0].ctn))]):e._e(),e._v(" "),a("el-descriptions-item",{attrs:{label:"运费(RMB)"}},[[a("el-input",{staticStyle:{width:"160px"},attrs:{size:"mini"},on:{blur:e.calculateFR},model:{value:e.detailObjData.freightTableData[0].expense,callback:function(t){e.$set(e.detailObjData.freightTableData[0],"expense",t)},expression:"detailObjData.freightTableData[0].expense"}})]],2),e._v(" "),e.detailObjData.freightTableData[0].cif?a("el-descriptions-item",{attrs:{label:"CIF"}},[e._v(e._s(e.detailObjData.freightTableData[0].cif))]):e._e(),e._v(" "),a("el-descriptions-item",{attrs:{label:"剔除管销"}},[[a("el-checkbox",{on:{change:e.handleFRchange},model:{value:e.fr_boolean,callback:function(t){e.fr_boolean=t},expression:"fr_boolean"}})]],2)],1)],1),e._v(" "),a("el-collapse-item",{attrs:{title:"最终报价",name:"tt"}},[a("div",{staticClass:"tt-collapse"},[a("div",{staticClass:"tt-left"},[a("div",[a("span",{staticStyle:{width:"80px"}},[e._v("销售类型：")]),e._v("\n                  "+e._s("内销"==e.isDomesticSales?"内销":"外销（含3‰保险）")+"\n                ")]),e._v(" "),"内销"==e.isDomesticSales?a("div",[a("span",{staticStyle:{width:"80px"}},[e._v("内销税率：")]),e._v(" "),a("el-input-number",{staticStyle:{width:"70%"},attrs:{size:"mini",min:0,max:300,"controls-position":"right"},model:{value:e.saleRate,callback:function(t){e.saleRate=t},expression:"saleRate"}}),e._v("\n                  %\n                ")],1):e._e(),e._v(" "),a("div",[a("span",{staticStyle:{width:"80px"}},[e._v("管销费：")]),e._v(" "),a("el-input-number",{staticStyle:{width:"70%"},attrs:{size:"mini",min:0,max:300,"controls-position":"right"},model:{value:e.marketing,callback:function(t){e.marketing=t},expression:"marketing"}}),e._v("\n                  %\n                ")],1),e._v(" "),a("div",[a("span",{staticStyle:{width:"80px"}},[e._v("币种：")]),e._v(" "),a("el-select",{staticStyle:{width:"70%"},attrs:{size:"mini"},on:{change:e.handleCTchange},model:{value:e.tt_currencyType,callback:function(t){e.tt_currencyType=t},expression:"tt_currencyType"}},e._l(e.ct_options,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("div",[a("span",{staticStyle:{width:"80px"}},[e._v("汇率：")]),e._v(" "),a("el-input-number",{staticStyle:{width:"70%"},attrs:{size:"mini",min:0,"controls-position":"right"},model:{value:e.totalRate,callback:function(t){e.totalRate=t},expression:"totalRate"}})],1)]),e._v(" "),a("el-table",{staticClass:"tt-right",attrs:{data:e.ttTableData,"max-height":"540",stripe:""}},[a("el-table-column",{attrs:{label:"利润",align:"center",prop:"column"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.meta?a("div",[a("el-input-number",{staticStyle:{width:"40%"},attrs:{"controls-position":"right",min:0,step:1,precision:1,size:"mini",clearable:""},on:{change:function(a){return e.handleRateChange(t.row)}},model:{value:t.row.column,callback:function(a){e.$set(t.row,"column",a)},expression:"scope.row.column"}}),e._v(" %\n                    ")],1):a("span",[e._v(e._s(t.row.column))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"价格",align:"center",prop:"row"}})],1)],1)])],1)],1),e._v(" "),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:e.handleDetailModalClose}},[e._v("关 闭")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"修改《"+e.editTitle+"》配置",width:"90%",visible:e.handleEditModal,"close-on-click-modal":!1,"show-close":!1},on:{"update:visible":function(t){e.handleEditModal=t},closed:e.handleEditModalClose}},[a("el-descriptions",{attrs:{size:"small",column:2}},[a("el-descriptions-item",{attrs:{label:"品号"}},[e._v(e._s(e.detailObjData.postForm.uuid))]),e._v(" "),a("el-descriptions-item",{attrs:{label:"品名"}},[e._v(e._s(e.detailObjData.postForm.productName))]),e._v(" "),a("el-descriptions-item",{attrs:{label:"创建人员"}},[e._v(e._s(e.detailObjData.postForm.creator))]),e._v(" "),a("el-descriptions-item",{attrs:{label:"创建时间"}},[e._v(e._s(e.detailObjData.postForm.creationDate))]),e._v(" "),a("el-descriptions-item",{attrs:{label:"生产工厂"}},[e._v(e._s(e.detailObjData.freightTableData[0].factory))]),e._v(" "),a("el-descriptions-item",{attrs:{label:"客户公司"}},[e._v(e._s(e.detailObjData.postForm.productConnection))]),e._v(" "),a("el-descriptions-item",{attrs:{label:"订单数量"}},[e._v(e._s(e.detailObjData.freightTableData[0].goodsCount)+"PCS")]),e._v(" "),a("el-descriptions-item",{attrs:{label:"包装信息"}},[e._v(e._s("每袋"+e.detailObjData.postForm.pcsCount+"pcs、每盒"+e.detailObjData.postForm.bagCount+"袋、每箱"+e.detailObjData.postForm.boxCount+"盒"))]),e._v(" "),a("el-descriptions-item",{attrs:{label:"销售类型"}},[e._v(e._s(e.detailObjData.freightTableData[0].sales_type))]),e._v(" "),a("el-descriptions-item",{attrs:{label:"产品说明"}},[e._v(e._s(e.detailObjData.postForm.productDetail))])],1),e._v(" "),a("el-collapse",{model:{value:e.activeNames,callback:function(t){e.activeNames=t},expression:"activeNames"}},[a("el-collapse-item",{attrs:{title:"原材料",name:"rm"}},[a("el-button",{staticClass:"addBtn",attrs:{disabled:e.rm_btn,type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.addRawMaterialRow}},[e._v("新增")]),e._v(" "),a("el-form",{ref:"rmTableForm",attrs:{model:e.detailObjData,rules:e.rmRules,size:"small"}},[a("el-table",{staticClass:"list-table",staticStyle:{width:"100%"},attrs:{data:e.detailObjData.rawMaterialTableData,"header-cell-style":{backgroundColor:"rgb(217, 236, 255)"},"row-style":{backgroundColor:"rgb(236, 245, 255)"}}},[a("el-table-column",{attrs:{label:"#",type:"index",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{label:"品号",align:"left",prop:"rm_number","min-width":"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"rawMaterialTableData."+t.$index+".rm_number"}},[a("el-autocomplete",{attrs:{"popper-append-to-body":!1,"fetch-suggestions":e.queryRmNumberSearchAsync,placeholder:"请填品号",clearable:""},on:{select:e.handleRmNumberSelect},model:{value:t.row.rm_number,callback:function(a){e.$set(t.row,"rm_number",a)},expression:"scope.row.rm_number"}})],1):a("span",[e._v(e._s(t.row.rm_number))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"品名",align:"left",prop:"rm_material","min-width":"160"},scopedSlots:e._u([{key:"header",fn:function(){return[a("div",[a("span",[e._v("品名：")]),e._v(" "),a("span",{staticClass:"nameTips"},[e._v("总称：颜色+材质+尺寸+特性(+厂商)")])])]},proxy:!0},{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"rawMaterialTableData."+t.$index+".rm_material",rules:e.rmRules.rm_material}},[a("el-autocomplete",{attrs:{"fetch-suggestions":e.queryNameSearchAsync,placeholder:"请填写物料名称","popper-append-to-body":!1,clearable:""},on:{select:e.handleNameSelect},model:{value:t.row.rm_material,callback:function(a){e.$set(t.row,"rm_material",a)},expression:"scope.row.rm_material"}})],1):a("span",[e._v(e._s(t.row.rm_material))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"单价",align:"center",prop:"rm_price"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"rawMaterialTableData."+t.$index+".rm_price",rules:e.rmRules.rm_price}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请填写",min:0,clearable:""},model:{value:t.row.rm_price,callback:function(a){e.$set(t.row,"rm_price",a)},expression:"scope.row.rm_price"}})],1):a("span",[e._v(e._s(t.row.rm_price))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"单位",align:"center",prop:"rm_unit"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.rm_unit))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"用量",align:"center",prop:"rm_consumption"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"rawMaterialTableData."+t.$index+".rm_consumption",rules:e.rmRules.rm_consumption}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请填写",min:0,clearable:""},model:{value:t.row.rm_consumption,callback:function(a){e.$set(t.row,"rm_consumption",a)},expression:"scope.row.rm_consumption"}})],1):a("span",[e._v(e._s(t.row.rm_consumption))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"币种",align:"center",prop:"rm_currencyType"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"rawMaterialTableData."+t.$index+".rm_currencyType",rules:e.rmRules.rm_currencyType}},[a("el-select",{attrs:{size:"mini"},on:{change:function(a){return e.handleCurrencyChange(t.row,"rm_exchangeRate","rm_currencyType")}},model:{value:t.row.rm_currencyType,callback:function(a){e.$set(t.row,"rm_currencyType",a)},expression:"scope.row.rm_currencyType"}},e._l(e.ct_options,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1):a("span",[e._v(e._s(e.CURRENCY_DIC[t.row.rm_currencyType]))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"汇率",align:"center",prop:"rm_exchangeRate"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"rawMaterialTableData."+t.$index+".rm_exchangeRate",rules:e.rmRules.rm_exchangeRate}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请填写",min:0,clearable:""},model:{value:t.row.rm_exchangeRate,callback:function(a){e.$set(t.row,"rm_exchangeRate",a)},expression:"scope.row.rm_exchangeRate"}})],1):a("span",[e._v(e._s(t.row.rm_exchangeRate))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"损耗",align:"center",prop:"rm_loss"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"rawMaterialTableData."+t.$index+".rm_loss",rules:e.rmRules.rm_loss}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请填写",step:.1,min:0,clearable:""},model:{value:t.row.rm_loss,callback:function(a){e.$set(t.row,"rm_loss",a)},expression:"scope.row.rm_loss"}})],1):a("span",[e._v(e._s(t.row.rm_loss))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"金额",align:"center",prop:"rm_amounts"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-button",{attrs:{type:"text",icon:"el-icon-search"},on:{click:function(a){return e.calculateRM(t.row)}}}):e._e(),e._v(" "),a("span",[e._v(e._s(t.row.rm_amounts))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-button",{attrs:{type:"text",icon:"el-icon-check"},nativeOn:{click:function(a){return a.preventDefault(),e.saveRawMaterialRow(t.row)}}},[e._v("提交")]):a("el-button",{attrs:{disabled:e.rm_btn,type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.updateRawMaterialRow(t.row)}}},[e._v("修改")]),e._v(" "),t.row.showInput?a("el-button",{attrs:{type:"text",icon:"el-icon-close"},nativeOn:{click:function(a){return a.preventDefault(),e.cancelRawMaterialRow(t.row)}}},[e._v("取消")]):a("el-button",{attrs:{disabled:e.rm_btn&&!t.row.showInput,type:"text",icon:"el-icon-delete"},nativeOn:{click:function(a){return a.preventDefault(),e.deleteRawMaterialRow(t.row)}}},[e._v("删除")])]}}])})],1)],1)],1),e._v(" "),a("el-collapse-item",{attrs:{title:"包装费",name:"pk"}},[a("el-button",{staticClass:"addBtn",attrs:{disabled:e.pk_btn,type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.addPackageRow}},[e._v("新增")]),e._v(" "),a("el-form",{ref:"pkTableForm",attrs:{model:e.detailObjData,rules:e.pkRules,size:"small"}},[a("el-table",{staticClass:"list-table",staticStyle:{width:"100%"},attrs:{data:e.detailObjData.packageTableData,"header-cell-style":{backgroundColor:"rgb(250, 236, 216)"},"row-style":{backgroundColor:"rgb(253, 246, 236)"}}},[a("el-table-column",{attrs:{label:"#",type:"index",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{label:"品号",align:"left",prop:"pk_number","min-width":"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"packageTableData."+t.$index+".pk_number"}},[a("el-autocomplete",{attrs:{"fetch-suggestions":e.queryPkNumberSearchAsync,placeholder:"请填包装品号",clearable:""},on:{select:e.handlePkNumberSelect},model:{value:t.row.pk_number,callback:function(a){e.$set(t.row,"pk_number",a)},expression:"scope.row.pk_number"}})],1):a("span",[e._v(e._s(t.row.pk_number))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"品名",align:"left",prop:"pk_material","min-width":"160"},scopedSlots:e._u([{key:"header",fn:function(){return[a("div",[a("span",[e._v("品名：")]),e._v(" "),a("span",{staticClass:"nameTips"},[e._v("总称：颜色+材质+尺寸+特性(+厂商)")])])]},proxy:!0},{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"packageTableData."+t.$index+".pk_material",rules:e.pkRules.pk_material}},[a("el-autocomplete",{attrs:{"popper-append-to-body":!1,"fetch-suggestions":e.queryPkNameSearchAsync,placeholder:"请填写包装名称",clearable:""},on:{select:e.handlePkNameSelect},model:{value:t.row.pk_material,callback:function(a){e.$set(t.row,"pk_material",a)},expression:"scope.row.pk_material"}})],1):a("span",[e._v(e._s(t.row.pk_material))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"单价",align:"center",prop:"pk_price"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"packageTableData."+t.$index+".pk_price",rules:e.pkRules.pk_price}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请填写",min:0,clearable:""},model:{value:t.row.pk_price,callback:function(a){e.$set(t.row,"pk_price",a)},expression:"scope.row.pk_price"}})],1):a("span",[e._v(e._s(t.row.pk_price))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"单位",align:"center",prop:"pk_unit"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.pk_unit))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"用量",align:"center",prop:"pk_consumption"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"packageTableData."+t.$index+".pk_consumption",rules:e.pkRules.pk_consumption}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请填写",min:0,precision:5,clearable:""},model:{value:t.row.pk_consumption,callback:function(a){e.$set(t.row,"pk_consumption",a)},expression:"scope.row.pk_consumption"}})],1):a("span",[e._v(e._s(t.row.pk_consumption))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"币种",align:"center",prop:"pk_currencyType"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"packageTableData."+t.$index+".pk_currencyType",rules:e.pkRules.pk_currencyType}},[a("el-select",{attrs:{size:"mini"},on:{change:function(a){return e.handleCurrencyChange(t.row,"pk_exchangeRate","pk_currencyType")}},model:{value:t.row.pk_currencyType,callback:function(a){e.$set(t.row,"pk_currencyType",a)},expression:"scope.row.pk_currencyType"}},e._l(e.ct_options,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1):a("span",[e._v(e._s(e.CURRENCY_DIC[t.row.pk_currencyType]))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"汇率",align:"center",prop:"pk_exchangeRate"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"packageTableData."+t.$index+".pk_exchangeRate",rules:e.pkRules.pk_exchangeRate}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请填写",min:0,clearable:""},model:{value:t.row.pk_exchangeRate,callback:function(a){e.$set(t.row,"pk_exchangeRate",a)},expression:"scope.row.pk_exchangeRate"}})],1):a("span",[e._v(e._s(t.row.pk_exchangeRate))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"金额",align:"center",prop:"pk_amounts"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-button",{attrs:{type:"text",icon:"el-icon-search"},on:{click:function(a){return e.calculatePackage(t.row)}}}):e._e(),e._v(" "),a("span",[e._v(e._s(t.row.pk_amounts))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-button",{attrs:{type:"text",icon:"el-icon-check"},nativeOn:{click:function(a){return a.preventDefault(),e.savePackageRow(t.row)}}},[e._v("提交")]):a("el-button",{attrs:{disabled:e.pk_btn,type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.updatePackageRow(t.row)}}},[e._v("修改")]),e._v(" "),t.row.showInput?a("el-button",{attrs:{type:"text",icon:"el-icon-close"},nativeOn:{click:function(a){return a.preventDefault(),e.cancelPackageRow(t.row)}}},[e._v("取消")]):a("el-button",{attrs:{disabled:e.pk_btn&&!t.row.showInput,type:"text",icon:"el-icon-delete"},nativeOn:{click:function(a){return a.preventDefault(),e.deletePackageRow(t.row)}}},[e._v("删除")])]}}])})],1)],1)],1),e._v(" "),a("el-collapse-item",{attrs:{title:"人工费",name:"lc"}},[a("el-button",{staticClass:"addBtn",attrs:{disabled:e.lc_btn,type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.addLaborRow}},[e._v("新增")]),e._v(" "),a("el-form",{ref:"lcTableForm",attrs:{model:e.detailObjData,rules:e.lcRules,size:"small"}},[a("el-table",{staticClass:"list-table",staticStyle:{width:"100%"},attrs:{data:e.detailObjData.LaborCostTableData,"header-cell-style":{backgroundColor:"rgb(225, 243, 216)"},"row-style":{backgroundColor:"rgb(240, 249, 235)"}}},[a("el-table-column",{attrs:{label:"#",type:"index",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{label:"工角名称",align:"left",prop:"lc_name"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"LaborCostTableData."+t.$index+".lc_name",rules:e.lcRules.lc_name}},[a("el-autocomplete",{attrs:{"popper-append-to-body":!1,"fetch-suggestions":e.queryLCNameSearchAsync,placeholder:"请填写工角名称",clearable:""},on:{select:e.handleLCNameSelect},model:{value:t.row.lc_name,callback:function(a){e.$set(t.row,"lc_name",a)},expression:"scope.row.lc_name"}})],1):a("span",[e._v(e._s(t.row.lc_name))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"所属工厂",align:"center",prop:"lc_factory"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.lc_factory))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"单价",align:"center",prop:"lc_price"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"LaborCostTableData."+t.$index+".lc_price",rules:e.lcRules.lc_price}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请填写",min:0,clearable:""},model:{value:t.row.lc_price,callback:function(a){e.$set(t.row,"lc_price",a)},expression:"scope.row.lc_price"}})],1):a("span",[e._v(e._s(t.row.lc_price))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"用量",align:"center",prop:"lc_consumption"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"LaborCostTableData."+t.$index+".lc_consumption",rules:e.lcRules.lc_consumption}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请填写",min:0,clearable:""},model:{value:t.row.lc_consumption,callback:function(a){e.$set(t.row,"lc_consumption",a)},expression:"scope.row.lc_consumption"}})],1):a("span",[e._v(e._s(t.row.lc_consumption))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"金额",align:"center",prop:"lc_amounts"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-button",{attrs:{type:"text",icon:"el-icon-search"},on:{click:function(a){return e.calculateLC(t.row)}}}):e._e(),e._v(" "),a("span",[e._v(e._s(t.row.lc_amounts))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-button",{attrs:{type:"text",icon:"el-icon-check"},on:{click:function(a){return e.saveLaborCostRow(t.row)}}},[e._v("提交")]):a("el-button",{attrs:{disabled:e.lc_btn,type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.updateLaborRow(t.row)}}},[e._v("修改")]),e._v(" "),t.row.showInput?a("el-button",{attrs:{type:"text",icon:"el-icon-close"},on:{click:function(a){return e.cancelLaborRow(t.row)}}},[e._v("取消")]):a("el-button",{attrs:{disabled:e.lc_btn&&!t.row.showInput,type:"text",icon:"el-icon-delete"},nativeOn:{click:function(a){return a.preventDefault(),e.deleteLaborRow(t.row)}}},[e._v("删除")])]}}])})],1)],1)],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:e.handleModifyClose}},[e._v("关 闭")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"复制主品",visible:e.handleCopyModal,"close-on-click-modal":!1,"show-close":!1,top:"12vh",width:"80%"},on:{"update:visible":function(t){e.handleCopyModal=t},closed:e.callOfCopy}},[a("el-form",{ref:"productForm",attrs:{model:e.copyProductFormData,rules:e.pr_rules}},[a("el-form-item",{attrs:{prop:"productName",label:"主品名称：","label-width":"100px"}},[a("el-input",{attrs:{size:"small",placeholder:"请填写客户公司对应的主品名称",clearable:""},model:{value:e.copyProductFormData.productName,callback:function(t){e.$set(e.copyProductFormData,"productName",t)},expression:"copyProductFormData.productName"}})],1),e._v(" "),a("el-form-item",{attrs:{prop:"productConnection",label:"客户公司：","label-width":"100px"}},[a("el-input",{attrs:{size:"small",placeholder:"请填写客户公司",clearable:""},model:{value:e.copyProductFormData.productConnection,callback:function(t){e.$set(e.copyProductFormData,"productConnection",t)},expression:"copyProductFormData.productConnection"}})],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"productFactory",label:"生产工厂："}},[a("el-radio-group",{attrs:{size:"medium"},model:{value:e.copyProductFormData.productFactory,callback:function(t){e.$set(e.copyProductFormData,"productFactory",t)},expression:"copyProductFormData.productFactory"}},[a("el-radio",{attrs:{label:"迅安"}}),e._v(" "),a("el-radio",{attrs:{label:"知腾"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{prop:"productDetail",label:"主品描述：","label-width":"100px"}},[a("el-input",{attrs:{size:"small",placeholder:"请填写产品详细信息",clearable:""},model:{value:e.copyProductFormData.productDetail,callback:function(t){e.$set(e.copyProductFormData,"productDetail",t)},expression:"copyProductFormData.productDetail"}})],1)],1),e._v(" "),a("el-form",{staticStyle:{"text-align":"left","white-space":"nowrap","margin-left":"12px"},attrs:{inline:""}},[a("el-form-item",{attrs:{label:"是否复制包装数据："}},[a("el-switch",{attrs:{"active-text":"是","inactive-text":"否"},model:{value:e.isIncludePK,callback:function(t){e.isIncludePK=t},expression:"isIncludePK"}})],1)],1),e._v(" "),e.isIncludePK?e._e():[a("el-form",{staticStyle:{"text-align":"left","white-space":"nowrap","margin-left":"12px"},attrs:{inline:""}},[a("el-form-item",{attrs:{label:"包装方式："}},[a("el-radio-group",{attrs:{size:"mini"},on:{input:e.onTypeChange},model:{value:e.packageType,callback:function(t){e.packageType=t},expression:"packageType"}},[a("el-radio-button",{attrs:{label:"彩盒"}}),e._v(" "),a("el-radio-button",{attrs:{label:"泡壳"}}),e._v(" "),a("el-radio-button",{attrs:{label:"袋装"}})],1)],1)],1),e._v(" "),"彩盒"===e.packageType?a("el-form",{ref:"packageForm",staticStyle:{"text-align":"left","white-space":"nowrap"},attrs:{model:e.copyProductFormData,rules:e.pk_rules,inline:""}},[a("el-form-item",{staticStyle:{width:"45%"},attrs:{prop:"pcsCount",label:"包装规格："}},[a("el-input-number",{staticStyle:{width:"80%"},attrs:{size:"mini",min:1,"controls-position":"right",clearable:""},model:{value:e.copyProductFormData.pcsCount,callback:function(t){e.$set(e.copyProductFormData,"pcsCount",t)},expression:"copyProductFormData.pcsCount"}}),e._v(" PCS / 袋\n          ")],1),e._v(" "),a("el-form-item",{staticStyle:{width:"25%"},attrs:{prop:"bagCount"}},[a("el-input-number",{staticStyle:{width:"80%"},attrs:{size:"mini",min:1,"controls-position":"right",clearable:""},model:{value:e.copyProductFormData.bagCount,callback:function(t){e.$set(e.copyProductFormData,"bagCount",t)},expression:"copyProductFormData.bagCount"}}),e._v(" 袋 / 盒\n          ")],1),e._v(" "),a("el-form-item",{staticStyle:{width:"25%"},attrs:{prop:"boxCount"}},[a("el-input-number",{staticStyle:{width:"80%"},attrs:{size:"mini",min:1,"controls-position":"right",clearable:""},model:{value:e.copyProductFormData.boxCount,callback:function(t){e.$set(e.copyProductFormData,"boxCount",t)},expression:"copyProductFormData.boxCount"}}),e._v(" 盒 / 箱\n          ")],1)],1):e._e(),e._v(" "),"泡壳"===e.packageType?a("el-form",{ref:"packageForm",staticStyle:{"text-align":"left","white-space":"nowrap"},attrs:{model:e.copyProductFormData,rules:e.pk_rules,inline:""}},[a("el-form-item",{staticStyle:{width:"45%"},attrs:{prop:"pcsCount",label:"包装规格："}},[a("el-input-number",{staticStyle:{width:"80%"},attrs:{size:"mini",min:1,"controls-position":"right",clearable:""},model:{value:e.copyProductFormData.pcsCount,callback:function(t){e.$set(e.copyProductFormData,"pcsCount",t)},expression:"copyProductFormData.pcsCount"}}),e._v(" PCS / 泡壳\n          ")],1),e._v(" "),a("el-form-item",{staticStyle:{width:"25%"},attrs:{prop:"boxCount"}},[a("el-input-number",{staticStyle:{width:"80%"},attrs:{size:"mini",min:1,"controls-position":"right",clearable:""},model:{value:e.copyProductFormData.boxCount,callback:function(t){e.$set(e.copyProductFormData,"boxCount",t)},expression:"copyProductFormData.boxCount"}}),e._v(" 泡壳 / 箱\n          ")],1)],1):e._e(),e._v(" "),"袋装"===e.packageType?a("el-form",{ref:"packageForm",staticStyle:{"text-align":"left","white-space":"nowrap"},attrs:{model:e.copyProductFormData,rules:e.pk_rules,inline:""}},[a("el-form-item",{staticStyle:{width:"45%"},attrs:{prop:"pcsCount",label:"包装规格："}},[a("el-input-number",{staticStyle:{width:"80%"},attrs:{size:"mini",min:1,"controls-position":"right",clearable:""},model:{value:e.copyProductFormData.pcsCount,callback:function(t){e.$set(e.copyProductFormData,"pcsCount",t)},expression:"copyProductFormData.pcsCount"}}),e._v(" PCS / 袋\n          ")],1),e._v(" "),a("el-form-item",{staticStyle:{width:"25%"},attrs:{prop:"boxCount"}},[a("el-input-number",{staticStyle:{width:"80%"},attrs:{size:"mini",min:1,"controls-position":"right",clearable:""},model:{value:e.copyProductFormData.boxCount,callback:function(t){e.$set(e.copyProductFormData,"boxCount",t)},expression:"copyProductFormData.boxCount"}}),e._v(" 袋 / 箱\n          ")],1)],1):e._e(),e._v(" "),a("el-container",{directives:[{name:"show",rawName:"v-show",value:e.packageType,expression:"packageType"}],staticStyle:{height:"500px"}},[a("el-aside",{attrs:{width:"200px"}},[a("el-input",{attrs:{"prefix-icon":"el-icon-search",placeholder:"输入关键字查询",size:"mini",clearable:""},model:{value:e.filterText,callback:function(t){e.filterText=t},expression:"filterText"}},[a("el-button",{attrs:{slot:"append",disabled:""},on:{click:function(t){e.drawer=!0}},slot:"append"},[e._v("新建")])],1),e._v(" "),a("el-tree",{ref:"tree",staticClass:"filter-tree",attrs:{data:e.pkTreeData,props:e.defaultProps,"filter-node-method":e.filterNode,"node-key":"packid","show-checkbox":"","default-expand-all":!1},on:{check:e.copySubRoleTreeCheck}})],1),e._v(" "),a("el-main",[a("div",{staticStyle:{"text-align":"left"}},["彩盒"===e.packageType?a("p",[e._v("========"),a("b",{staticStyle:{color:"#1989fa"}},[e._v("彩盒")]),e._v("基本包装包含："),a("b",{staticStyle:{color:"#1989fa"}},[e._v("胶袋、彩盒、合格证、外箱、封箱胶带")]),e._v("等物料，提交前请确认是否完整========")]):e._e(),e._v(" "),"泡壳"===e.packageType?a("p",[e._v("========"),a("b",{staticStyle:{color:"#1989fa"}},[e._v("泡壳")]),e._v("基本包装包含："),a("b",{staticStyle:{color:"#1989fa"}},[e._v("泡壳、彩/吊卡、说明书、外箱、封箱胶带")]),e._v("等物料，提交前请确认是否完整========")]):e._e(),e._v(" "),"袋装"===e.packageType?a("p",[e._v("========"),a("b",{staticStyle:{color:"#1989fa"}},[e._v("袋装")]),e._v("基本包装包含："),a("b",{staticStyle:{color:"#1989fa"}},[e._v("胶袋、彩/吊卡、说明书、外箱、封箱胶带")]),e._v("等物料，提交前请确认是否完整========")]):e._e(),e._v(" "),e._l(e.copyPackageFormData,function(t,r){return a("p",{key:t.packid},[a("span",{staticStyle:{width:"25%",display:"inline-block"}},[e._v(e._s(r+1+"、名称："+t.pk_material))]),e._v(" "),a("span",{staticStyle:{width:"20%",display:"inline-block"}},[e._v("尺寸："+e._s(t.pk_length?t.pk_length+'" x '+t.pk_width+'" x '+t.pk_height+'"':"/"))]),e._v(" "),a("span",{staticStyle:{width:"15%",display:"inline-block"}},[e._v("单价：\n                  "),a("el-input-number",{staticStyle:{width:"60%"},attrs:{size:"mini","controls-position":"right",clearable:""},on:{change:function(a){return e.calculatePackage(t)}},model:{value:t.pk_price,callback:function(a){e.$set(t,"pk_price",a)},expression:"item.pk_price"}})],1),e._v(" "),a("span",{staticStyle:{width:"20%",display:"inline-block"}},[e._v("用量：\n                  "),a("el-input-number",{staticStyle:{width:"60%"},attrs:{size:"mini","controls-position":"right",clearable:""},on:{change:function(a){return e.calculatePackage(t)}},model:{value:t.pk_consumption,callback:function(a){e.$set(t,"pk_consumption",a)},expression:"item.pk_consumption"}})],1),e._v(" "),a("span",{staticStyle:{width:"14%",display:"inline-block"}},[e._v("金额："+e._s(t.pk_amounts))])])})],2)])],1),e._v(" "),a("el-form",{ref:"PKLCTableForm",staticStyle:{"text-align":"left","margin-top":"24px"},attrs:{model:e.copyPKLCTableFormData,rules:e.pklcRules,size:"mini",inline:""}},[a("el-form-item",{attrs:{prop:"lc_name",label:"包装工角："}},[a("el-input",{attrs:{placeholder:"请输入工角名称",clearable:""},model:{value:e.copyPKLCTableFormData.lc_name,callback:function(t){e.$set(e.copyPKLCTableFormData,"lc_name",t)},expression:"copyPKLCTableFormData.lc_name"}})],1),e._v(" "),a("el-form-item",{attrs:{prop:"lc_price",label:"价格(RMB)："}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请填写",step:1,precision:5,clearable:""},model:{value:e.copyPKLCTableFormData.lc_price,callback:function(t){e.$set(e.copyPKLCTableFormData,"lc_price",t)},expression:"copyPKLCTableFormData.lc_price"}})],1),e._v(" "),a("el-form-item",[e._v("\n            (包装统一报价，含全部包装工角单价)\n          ")])],1)],e._v(" "),a("el-form",{ref:"CPLCTableForm",staticStyle:{"text-align":"left"},attrs:{model:e.copyLCTableFormData,rules:e.cpLcRules,size:"mini",inline:""}},[a("el-form-item",{attrs:{prop:"lc_name",label:"人工工角："}},[a("el-autocomplete",{attrs:{"popper-append-to-body":!1,"fetch-suggestions":e.queryLCSearchAsync,placeholder:"请选择",clearable:""},on:{select:e.handleLCrSelect},model:{value:e.copyLCTableFormData.lc_name,callback:function(t){e.$set(e.copyLCTableFormData,"lc_name",t)},expression:"copyLCTableFormData.lc_name"}})],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"factoryShow",label:"所属工厂：","label-width":"100px"}},[e._v("\n          "+e._s(e.copyProductFormData.productFactory)+"\n        ")]),e._v(" "),a("el-form-item",{attrs:{prop:"lc_price",label:"价格(RMB)："}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请填写",step:1,precision:5,clearable:""},model:{value:e.copyLCTableFormData.lc_price,callback:function(t){e.$set(e.copyLCTableFormData,"lc_price",t)},expression:"copyLCTableFormData.lc_price"}})],1),e._v(" "),a("el-form-item",[e._v("\n          (人工统一报价，除了包装以外的工角单价)\n        ")])],1),e._v(" "),a("div",{staticStyle:{overflow:"hidden",margin:"36px 0 0 0"}},[a("el-form",{ref:"freightForm",attrs:{id:"copyShipping",model:e.copyFreightFormData,rules:e.fr_rules}},[a("el-form-item",{staticStyle:{position:"relative"},attrs:{prop:"goodsCount",label:"订单数量：","label-width":"100px"}},[a("el-input-number",{attrs:{size:"small",placeholder:"不确定订单数量可输入0","controls-position":"right"},on:{blur:function(t){return e.handleCalculateCopyCTN(e.copyFreightFormData,e.copyProductFormData,e.copyPackageFormData)}},model:{value:e.copyFreightFormData.goodsCount,callback:function(t){e.$set(e.copyFreightFormData,"goodsCount",t)},expression:"copyFreightFormData.goodsCount"}}),e._v(" "),a("span",{staticClass:"tips"},[e._v('不确定订单数量可输入"0"，整柜默认按950材积、散货按2000个彩盒进行计算')])],1),e._v(" "),a("el-form-item",{attrs:{prop:"ctn",label:"CTN：","label-width":"100px"}},[a("el-input-number",{attrs:{size:"small",placeholder:"CTN如果不满一箱按一箱进行计算","controls-position":"right"},model:{value:e.copyFreightFormData.ctn,callback:function(t){e.$set(e.copyFreightFormData,"ctn",t)},expression:"copyFreightFormData.ctn"}}),e._v(" "),a("span",{staticClass:"tips"},[e._v("CTN数如果不满一箱按一箱进行计算")])],1),e._v(" "),a("el-form-item",{attrs:{prop:"cmb",label:"CBM：","label-width":"100px"}},[a("el-input-number",{attrs:{size:"small",placeholder:"请输入货柜体积","controls-position":"right"},model:{value:e.copyFreightFormData.cmb,callback:function(t){e.$set(e.copyFreightFormData,"cmb",t)},expression:"copyFreightFormData.cmb"}})],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"sales_type",label:"销售类型：","label-width":"100px"}},[a("el-radio-group",{model:{value:e.copyFreightFormData.sales_type,callback:function(t){e.$set(e.copyFreightFormData,"sales_type",t)},expression:"copyFreightFormData.sales_type"}},[a("el-radio",{attrs:{label:"外销"}}),e._v(" "),a("el-radio",{attrs:{label:"内销"}})],1)],1),e._v(" "),e.copyFreightFormData.sales_type&&"外销"==e.copyFreightFormData.sales_type?[a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"factoryShow",label:"出货工厂：","label-width":"100px"}},[e._v("\n              "+e._s(e.copyProductFormData.productFactory)+"\n            ")]),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"port",label:"出货港口：","label-width":"100px"}},[a("el-radio-group",{model:{value:e.copyFreightFormData.port,callback:function(t){e.$set(e.copyFreightFormData,"port",t)},expression:"copyFreightFormData.port"}},[a("el-radio",{attrs:{label:"上海"}}),e._v(" "),a("el-radio",{attrs:{label:"武汉"}}),e._v(" "),a("el-radio",{attrs:{label:"深圳"}})],1)],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"terms",label:"贸易条件：","label-width":"100px"}},[a("el-radio-group",{on:{change:e.onTermsChange},model:{value:e.copyFreightFormData.terms,callback:function(t){e.$set(e.copyFreightFormData,"terms",t)},expression:"copyFreightFormData.terms"}},[a("el-radio",{attrs:{label:"FOB"}}),e._v(" "),a("el-radio",{attrs:{label:"Ex Works"}}),e._v(" "),a("el-radio",{attrs:{label:"To Door"}}),e._v(" "),a("el-radio",{attrs:{label:"CIF"}})],1)],1),e._v(" "),e.copyFreightFormData.terms&&"Ex Works"===e.copyFreightFormData.terms?[a("el-form-item",{staticStyle:{"text-align":"left",width:"100%"},attrs:{prop:"expense",label:"运输综合费用(RMB)：","label-width":"156px"}},[e._v("\n                0.00 (Exit Work不产生运费)\n              ")])]:e._e(),e._v(" "),e.copyFreightFormData.terms&&"To Door"===e.copyFreightFormData.terms?[a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"shippingType",label:"出货类别：","label-width":"100px"}},[a("el-radio-group",{model:{value:e.copyFreightFormData.shippingType,callback:function(t){e.$set(e.copyFreightFormData,"shippingType",t)},expression:"copyFreightFormData.shippingType"}},[a("el-radio",{attrs:{label:"整柜"}}),e._v(" "),"武汉"!==e.copyFreightFormData.port?a("el-radio",{attrs:{label:"散货"}}):e._e()],1)],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left",width:"100%"},attrs:{prop:"expense",label:"运输综合费用(RMB)：","label-width":"156px"}},[a("el-input-number",{attrs:{size:"small",placeholder:"请填写运费","controls-position":"right"},model:{value:e.copyFreightFormData.expense,callback:function(t){e.$set(e.copyFreightFormData,"expense",t)},expression:"copyFreightFormData.expense"}})],1)]:e._e(),e._v(" "),e.copyFreightFormData.terms&&"CIF"===e.copyFreightFormData.terms?[a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"shippingType",label:"出货类别：","label-width":"100px"}},[a("el-radio-group",{model:{value:e.copyFreightFormData.shippingType,callback:function(t){e.$set(e.copyFreightFormData,"shippingType",t)},expression:"copyFreightFormData.shippingType"}},[a("el-radio",{attrs:{label:"整柜"}}),e._v(" "),"武汉"!==e.copyFreightFormData.port?a("el-radio",{attrs:{label:"散货"}}):e._e()],1)],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left",width:"100%"},attrs:{prop:"expense",label:"运输综合费用(RMB)：","label-width":"156px"}},[a("el-input-number",{attrs:{size:"small",placeholder:"请填写运费","controls-position":"right"},model:{value:e.copyFreightFormData.expense,callback:function(t){e.$set(e.copyFreightFormData,"expense",t)},expression:"copyFreightFormData.expense"}}),e._v(" "),a("span",[e._v("(CIF包含运费及报关费)")])],1)]:e._e(),e._v(" "),e.copyFreightFormData.terms&&"FOB"===e.copyFreightFormData.terms?[a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"shippingType",label:"出货类别：","label-width":"100px"}},[a("el-radio-group",{model:{value:e.copyFreightFormData.shippingType,callback:function(t){e.$set(e.copyFreightFormData,"shippingType",t)},expression:"copyFreightFormData.shippingType"}},[a("el-radio",{attrs:{label:"整柜"}}),e._v(" "),"武汉"!==e.copyFreightFormData.port?a("el-radio",{attrs:{label:"散货"}}):e._e()],1)],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left",width:"100%"},attrs:{prop:"expense",label:"运输综合费用(RMB)：","label-width":"156px"}},[a("el-input-number",{attrs:{size:"small",placeholder:"请输入金额","controls-position":"right"},model:{value:e.copyFreightFormData.expense,callback:function(t){e.$set(e.copyFreightFormData,"expense",t)},expression:"copyFreightFormData.expense"}})],1)]:e._e()]:e._e(),e._v(" "),e.copyFreightFormData.sales_type&&"内销"==e.copyFreightFormData.sales_type?[a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"factoryShow",label:"出货工厂：","label-width":"100px"}},[e._v("\n              "+e._s(e.copyProductFormData.productFactory)+"\n            ")]),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"terms",label:"贸易条件：","label-width":"100px"}},[a("el-radio-group",{on:{change:e.onTermsChange},model:{value:e.copyFreightFormData.terms,callback:function(t){e.$set(e.copyFreightFormData,"terms",t)},expression:"copyFreightFormData.terms"}},[a("el-radio",{attrs:{label:"Ex Works"}}),e._v(" "),a("el-radio",{attrs:{label:"To Door"}})],1)],1),e._v(" "),e.copyFreightFormData.terms&&"Ex Works"===e.copyFreightFormData.terms?[a("el-form-item",{staticStyle:{"text-align":"left",width:"49%"},attrs:{prop:"expense",label:"运费(RMB):","label-width":"100px"}},[e._v("\n                0.00 (Exit Work不产生运费)\n              ")])]:e._e(),e._v(" "),e.copyFreightFormData.terms&&"To Door"===e.copyFreightFormData.terms?[a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"shippingType",label:"出货类别：","label-width":"100px"}},[a("el-radio-group",{model:{value:e.copyFreightFormData.shippingType,callback:function(t){e.$set(e.copyFreightFormData,"shippingType",t)},expression:"copyFreightFormData.shippingType"}},[a("el-radio",{attrs:{label:"整柜"}}),e._v(" "),a("el-radio",{attrs:{label:"散货"}})],1)],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left",width:"49%"},attrs:{prop:"expense",label:"运费(RMB):","label-width":"100px"}},[a("el-input-number",{attrs:{size:"small",placeholder:"请填写运费","controls-position":"right"},model:{value:e.copyFreightFormData.expense,callback:function(t){e.$set(e.copyFreightFormData,"expense",t)},expression:"copyFreightFormData.expense"}})],1)]:e._e()]:e._e()],2),e._v(" "),a("div",{staticStyle:{width:"40%",float:"left","text-align":"left",margin:"0 0 0 64px"}},[a("h3",{staticStyle:{margin:"0 0 36px 0"}},[e._v("货品信息")]),e._v(" "),a("p",{staticStyle:{margin:"0 0 36px 0"}},[e._v("单箱材积(cuft)："+e._s(e.cuft))]),e._v(" "),a("p",{staticStyle:{margin:"0 0 36px 0"}},[e._v("货品总体积(CBM)："+e._s(e.CBM_ALL))]),e._v(" "),a("p",{staticStyle:{margin:"0 0 36px 0"}},[e._v("20尺货柜容量(PCS)："+e._s(e.CTN_20INCHI))]),e._v(" "),a("p",{staticStyle:{margin:"0 0 36px 0"}},[e._v("40尺货柜容量(PCS)："+e._s(e.CTN_40INCHI))]),e._v(" "),"彩盒"===e.packageType?a("p",{staticStyle:{color:"#409EFF",margin:"0 0 36px 0"}},[e._v(e._s(e.alertSTR))]):e._e(),e._v(" "),e.copyFreightFormData.goodsCount>e.CTN_20INCHI?a("p",{staticStyle:{color:"#F56C6C",margin:"0 0 36px 0"}},[e._v("注意：当前订单数量已超过20尺货柜最大容量，请选择更大尺寸的货柜！")]):e._e()])],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:e.callOfCopy}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.postCopyMain}},[e._v("保 存")])],1)],2),e._v(" "),a("el-dialog",{attrs:{title:"新增包装",visible:e.drawer,"before-close":e.handleClose},on:{"update:visible":function(t){e.drawer=t}}},[a("el-form",{ref:"ruleForm",staticClass:"form-wrap",attrs:{model:e.pkFormData,rules:e.rules,size:"mini"}},[a("el-form-item",{attrs:{prop:"pk_material",label:"品名","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"品名命名规则请尽量遵循：总称：颜色+材质+尺寸+特性(+厂商)",clearable:""},model:{value:e.pkFormData.pk_material,callback:function(t){e.$set(e.pkFormData,"pk_material",t)},expression:"pkFormData.pk_material"}})],1),e._v(" "),a("el-form-item",{attrs:{prop:"pk_itemType",label:"种类","label-width":"100px"}},[a("el-select",{attrs:{placeholder:"请选择种类"},model:{value:e.pkFormData.pk_itemType,callback:function(t){e.$set(e.pkFormData,"pk_itemType",t)},expression:"pkFormData.pk_itemType"}},e._l(e.pk_itemType_options,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),"carton"===e.pkFormData.pk_itemType?[a("el-form-item",{attrs:{prop:"pk_length",label:"长","label-width":"100px"}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请输入（单位：英吋）",min:0,precision:2,clearable:""},model:{value:e.pkFormData.pk_length,callback:function(t){e.$set(e.pkFormData,"pk_length",t)},expression:"pkFormData.pk_length"}})],1),e._v(" "),a("el-form-item",{attrs:{prop:"pk_width",label:"宽","label-width":"100px"}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请输入（单位：英吋）单价",min:0,precision:2,clearable:""},model:{value:e.pkFormData.pk_width,callback:function(t){e.$set(e.pkFormData,"pk_width",t)},expression:"pkFormData.pk_width"}})],1),e._v(" "),a("el-form-item",{attrs:{prop:"pk_height",label:"高","label-width":"100px"}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请输入（单位：英吋）",min:0,precision:2,clearable:""},model:{value:e.pkFormData.pk_height,callback:function(t){e.$set(e.pkFormData,"pk_height",t)},expression:"pkFormData.pk_height"}})],1)]:e._e(),e._v(" "),a("el-form-item",{attrs:{prop:"pk_supplier",label:"厂商","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入厂商",clearable:""},model:{value:e.pkFormData.pk_supplier,callback:function(t){e.$set(e.pkFormData,"pk_supplier",t)},expression:"pkFormData.pk_supplier"}})],1),e._v(" "),a("el-form-item",{attrs:{prop:"pk_price",label:"单价","label-width":"100px"}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请输入单价",min:0,precision:5,clearable:""},model:{value:e.pkFormData.pk_price,callback:function(t){e.$set(e.pkFormData,"pk_price",t)},expression:"pkFormData.pk_price"}})],1),e._v(" "),a("el-form-item",{attrs:{prop:"pk_unit",label:"单位","label-width":"100px"}},[a("el-select",{attrs:{placeholder:"注意成品用量变化，并通知主品配置人员",filterable:"","allow-create":"","default-first-optionclearable":""},model:{value:e.pkFormData.pk_unit,callback:function(t){e.$set(e.pkFormData,"pk_unit",t)},expression:"pkFormData.pk_unit"}},e._l(e.unit_options,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1)],2),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:e.callOfdrawer}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.handlePostForm}},[e._v("保 存")])],1)],1)],1)},staticRenderFns:[]};var B=a("VU/8")(H,E,!1,function(e){a("yOmT")},"data-v-5e752a68",null).exports,A={name:"Succession",data:function(){return{globalIndex:0,itemOtions:[],currentRow:{},tableHeight:0,currentNum:1,pageSize:50,totalNum:0,material:"",power:b.getCookie("power"),tableList:[],activeNames:["rm","lc"],queryName:"",laborCostList:[],handleAddModal:!1,handleEditModal:!1,mainFormData:{},handleType:"",editTitle:"",detailObjData:{postForm:{},LaborCostTableData:[],rawMaterialTableData:[]},rules:{sc_name:[{required:!0,message:"请填写系列名称",trigger:"blur"}],sc_standard:[{required:!0,message:"请选择标准",trigger:"blur"}],sc_type:[{required:!0,message:"请选择型号",trigger:"blur"}]},rm_btn:!1,lc_btn:!1,lc_sum:0,lc_percentage:156,rmRules:{rm_itemType:[{required:!0,message:"请选择种类",trigger:"change"}],rm_exchangeRate:[{required:!0,type:"number",message:"请填写汇率",trigger:"blur"}],rm_price:[{required:!0,type:"number",message:"请填写单价",trigger:"blur"}],rm_consumption:[{required:!0,type:"number",message:"请填写用量",trigger:"blur"}],rm_loss:[{required:!0,type:"number",message:"请填写损耗",trigger:"blur"}],rm_material:[{required:!0,message:"请填写名称",trigger:"change"}],rm_number:[{required:!0,message:"请填写物料品号",trigger:"change"}],rm_unit:[{required:!0,message:"请选择单位",trigger:"change"}],rm_currencyType:[{required:!0,message:"请选择币种",trigger:"change"}]},lcRules:{lc_unit:[{required:!0,message:"请选择单位",trigger:"change"}],lc_exchangeRate:[{required:!0,type:"number",message:"请填写汇率",trigger:"blur"}],lc_price:[{required:!0,type:"number",message:"请填写单价",trigger:"blur"}],lc_consumption:[{required:!0,type:"number",message:"请填写用量",trigger:"blur"}],lc_name:[{required:!0,message:"请填写名称",trigger:"change"}]}}},computed:s()({},Object(D.c)(["CURRENCY_DIC","standard_options"])),mounted:function(){var e=this;this.$nextTick(function(){e.tableHeight=window.innerHeight-136});var t=this;window.onresize=function(){t.resizeFlag&&clearTimeout(t.resizeFlag),t.resizeFlag=setTimeout(function(){t.getTableHeight(),t.resizeFlag=null},100)}},activated:function(){this.getAllList(),this.getLaborCostList()},methods:{createFilter:function(e){return function(t){return 0===t.value.toLowerCase().indexOf(e.toLowerCase())}},successionSpanMethod:function(e){var t=e.row,a=e.column,r=e.rowIndex;if(0===e.columnIndex){var o=t[a.property],l=this.tableList[r-1];if(o===(l?l[a.property]:null))return{rowspan:0,colspan:0};for(var i=1,n=r+1;n<this.tableList.length;n++){if(this.tableList[n][a.property]!==o)break;i++}return{rowspan:i,colspan:1}}},getTableHeight:function(){var e=window.innerHeight-180;this.tableHeight=e<=300?300:window.innerHeight-180},seach:function(){this.current=1,this.pageSize=50,this.getAllList()},getAllList:function(){var e=this,t=new FormData;t.append("current",this.currentNum),t.append("size",this.pageSize),t.append("sc_name",this.queryName),k({method:"POST",url:"/api/selectSc",data:t}).then(function(t){e.totalNum=t.total,e.tableList=t.records})},handleSizeChange:function(e){this.pageSize=e,this.getAllList()},handleCurrentChange:function(e){this.currentNum=e,this.getAllList()},queryRawMaterialList:function(){var e=this;k({method:"POST",url:"/api/selectRm",data:{uuid:this.detailObjData.postForm.uuid}}).then(function(t){e.detailObjData.rawMaterialTableData=t,e.rm_btn=!1})},calculateRM:function(e){if(e.rm_price&&e.rm_exchangeRate&&e.rm_consumption&&e.rm_loss){var t=0;e.rm_consumption?e.rm_consumption>0&&e.rm_consumption<1?t=e.rm_price*e.rm_exchangeRate*e.rm_consumption/e.rm_loss:e.rm_consumption>=1&&(t=e.rm_price*e.rm_exchangeRate/e.rm_consumption/e.rm_loss):t=e.rm_price*e.rm_exchangeRate/e.rm_loss,e.rm_amounts=t.toFixed(5)}},queryNameSearchAsync:function(e,t){var a=new FormData;a.append("current",1),a.append("size",9999),a.append("rm_material",e),a.append("rm_number",""),k({method:"POST",url:"/api/GoodsAll",data:a}).then(function(e){var a=e.records.map(function(e){return s()({value:e.rm_material},e)});t(a)})},handleNameSelect:function(e){this.detailObjData.rawMaterialTableData[this.globalIndex].rm_number=e.rm_number,this.itemOtions=e.rm_itemType.split(","),1===this.itemOtions.length&&(this.detailObjData.rawMaterialTableData[this.globalIndex].rm_itemType=this.itemOtions[0]),this.detailObjData.rawMaterialTableData[this.globalIndex].rm_supplier=e.rm_supplier,this.detailObjData.rawMaterialTableData[this.globalIndex].rm_price=e.rm_price,this.detailObjData.rawMaterialTableData[this.globalIndex].rm_unit=e.rm_unit,this.detailObjData.rawMaterialTableData[this.globalIndex].rm_exchangeRate=e.rm_exchangeRate,this.detailObjData.rawMaterialTableData[this.globalIndex].rm_currencyType=e.rm_currencyType},handleSelectItemType:function(e){this.detailObjData.rawMaterialTableData[this.globalIndex].rm_itemType=e},queryRmNumberSearchAsync:function(e,t){var a=new FormData;a.append("current",1),a.append("size",9999),a.append("rm_material",""),a.append("rm_number",e),k({method:"POST",url:"/api/GoodsAll",data:a}).then(function(e){var a=e.records.map(function(e){return s()({value:e.rm_number},e)});t(a)})},handleRmNumberSelect:function(e){this.detailObjData.rawMaterialTableData[this.globalIndex].rm_material=e.rm_material,this.itemOtions=e.rm_itemType.split(","),1===this.itemOtions.length&&(this.detailObjData.rawMaterialTableData[this.globalIndex].rm_itemType=this.itemOtions[0]),this.detailObjData.rawMaterialTableData[this.globalIndex].rm_supplier=e.rm_supplier,this.detailObjData.rawMaterialTableData[this.globalIndex].rm_price=e.rm_price,this.detailObjData.rawMaterialTableData[this.globalIndex].rm_unit=e.rm_unit,this.detailObjData.rawMaterialTableData[this.globalIndex].rm_currencyType=e.rm_currencyType,this.detailObjData.rawMaterialTableData[this.globalIndex].rm_exchangeRate=e.rm_exchangeRate},addRawMaterialRow:function(){this.globalIndex=0,this.rm_btn=!0,this.detailObjData.rawMaterialTableData.unshift({showInput:!0,uuid:this.detailObjData.postForm.uuid,rm_material:"",rm_itemType:"",rm_price:0,rm_unit:"",rm_currencyType:"",rm_amounts:"",rm_exchangeRate:1,rm_loss:.97,rm_consumption:1,rm_number:""})},updateRawMaterialRow:function(e,t){this.globalIndex=t,this.itemOtions=e.rm_itemType?e.rm_itemType.split(","):[],e.showInput=!0,this.rm_btn=!0},cancelRawMaterialRow:function(e){e.rm_id?this.queryRawMaterialList():(this.detailObjData.rawMaterialTableData.splice(0,1),this.rm_btn=!1,this.itemOtions=[])},saveRawMaterialRow:function(e){var t=this;this.$refs.rmTableForm.validate(function(a){if(!a)return!1;(t.calculateRM(e),b.getCookie("userName"))?(k({method:"POST",url:"/api/updateSc",data:{sc_id:t.currentRow.sc_id,sc_user:b.getCookie("userName"),sc_date:v(Date.now(),"YYYY-MM-DD HH:mm:ss")}}).then(function(e){console.log(e)}),k({method:"POST",url:e.rm_id?"/api/updaterm":"/api/insertScRm",data:e}).then(function(a){t.$message({offset:80,message:"保存成功！",type:"success"}),e.showInput=!1,t.itemOtions=[],t.queryRawMaterialList()})):t.$message.warning("登录超时，请重新登录！")})},deleteRawMaterialRow:function(e){var t=this;this.$refs.rmTableForm.clearValidate(),e.showInput?(this.detailObjData.rawMaterialTableData.splice(0,1),this.rm_btn=!1):this.$confirm("是否删除”"+e.rm_material+"“信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){k({method:"POST",url:"/api/deleterm",data:{rm_id:e.rm_id}}).then(function(a){t.$message({offset:80,message:"删除成功！",type:"success"}),e.showInput=!1,t.itemOtions=[],t.queryRawMaterialList()})}).catch(function(){t.$message({offset:80,type:"info",message:"已取消删除！"})})},getLaborCostList:function(){var e=this,t=new FormData;t.append("current",1),t.append("size",9999),t.append("lc_name",""),k({method:"POST",url:"/api/LaborCostAll",data:t}).then(function(t){e.laborCostList=t.records})},queryLCNameSearchAsync:function(e,t){var a=this.laborCostList.map(function(e){return s()({value:e.lc_name+" ("+e.lc_factory+")"},e)});t(e?a.filter(this.createFilter(e)):a)},handleLCNameSelect:function(e){this.detailObjData.LaborCostTableData[0].lc_name=e.lc_name+"人工",this.detailObjData.LaborCostTableData[0].lc_price=e.lc_price,this.detailObjData.LaborCostTableData[0].lc_unit=e.lc_unit,this.detailObjData.LaborCostTableData[0].lc_factory=e.lc_factory},handleCreate:function(){this.mainFormData={},this.itemOtions=[],this.handleType="create",this.handleAddModal=!0},postAddMain:function(e){var t=this;this.$refs[e].validate(function(e){if(!e)return!1;b.getCookie("userName")?("create"===t.handleType&&k({method:"POST",url:"/api/ScAdd",data:s()({uuid:"",sc_id:""},t.mainFormData,{sc_user:b.getCookie("userName"),sc_date:v(Date.now(),"YYYY-MM-DD HH:mm:ss")})}).then(function(e){t.$message({offset:80,message:"保存成功！",type:"success"}),t.handleAddModal=!1,t.getAllList()}),"update"===t.handleType&&k({method:"POST",url:"/api/updateSc",data:s()({},t.mainFormData,{sc_user:b.getCookie("userName"),sc_date:v(Date.now(),"YYYY-MM-DD HH:mm:ss")})}).then(function(e){t.$message({offset:80,message:"保存成功！",type:"success"}),t.handleAddModal=!1,t.getAllList()})):t.$message.warning("登录超时，请重新登录！")})},updateProductForm:function(e){this.mainFormData=e,this.handleType="update",this.handleAddModal=!0},handleDetail:function(e){var t=this;this.currentRow=e,this.lc_percentage=156,k({method:"POST",url:"/api/selectScList",data:{sc_id:e.sc_id}}).then(function(a){t.editTitle=e.sc_type,t.detailObjData=a,t.handleEditModal=!0})},handleDelete:function(e){var t=this;this.$confirm("此操作将永久删除“"+e.sc_type+"”系列数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){k({method:"POST",url:"/api/deleteSc",data:{uuid:e.uuid}}).then(function(e){t.$message({offset:80,type:"success",message:"操作成功！"}),t.getAllList()})}).catch(function(){t.$message({offset:80,type:"info",message:"已取消删除！"})})},handleMainClose:function(e){var t=this;this.$confirm("确认关闭？").then(function(a){e(),t.$refs.ruleForm.resetFields()}).catch(function(e){})},callOf:function(e){this.handleAddModal=!1,this.$refs[e].resetFields(),this.getAllList()},handleDetailClose:function(e){var t=this;this.$confirm("确认关闭？").then(function(a){e(),t.resetBTNs(),t.getAllList()}).catch(function(e){})},resetBTNs:function(){this.handleEditModal=!1,this.rm_btn=!1,this.lc_btn=!1},queryLaborCostList:function(){var e=this;k({method:"POST",url:"/api/selectLc",data:{uuid:this.detailObjData.postForm.uuid}}).then(function(t){e.detailObjData.LaborCostTableData=t,e.lc_btn=!1})},addLaborRow:function(){this.lc_btn=!0,this.detailObjData.LaborCostTableData.unshift({showInput:!0,uuid:this.detailObjData.postForm.uuid,lc_name:"",lc_price:0,lc_unit:"",lc_exchangeRate:1,lc_consumption:1})},updateLaborRow:function(e){e.showInput=!0,this.lc_btn=!0},calculateLC:function(e){if(e.lc_price&&e.lc_exchangeRate&&!e.lc_consumption){var t=0;e.lc_consumption?e.lc_consumption>0&&e.lc_consumption<1?t=e.lc_price*e.lc_exchangeRate*e.lc_consumption:e.lc_consumption>=1&&(t=e.lc_price*e.lc_exchangeRate/e.lc_consumption):t=e.lc_price*e.lc_exchangeRate,e.lc_amounts=t.toFixed(5)}},cancelLaborRow:function(e){e.lc_id?this.queryLaborCostList():(this.detailObjData.LaborCostTableData.splice(0,1),this.lc_btn=!1)},deleteLaborRow:function(e){var t=this;this.$refs.lcTableForm.clearValidate(),e.showInput?(this.detailObjData.LaborCostTableData.splice(0,1),this.lc_btn=!1):this.$confirm("是否删除”"+e.lc_name+"“信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){k({method:"POST",url:"/api/deletelc",data:{lc_id:e.lc_id}}).then(function(a){t.$message({offset:80,message:"删除成功！",type:"success"}),e.showInput=!1,t.queryLaborCostList()})}).catch(function(){t.$message({offset:80,type:"info",message:"已取消删除！"})})},saveLaborCostRow:function(e){var t=this;this.$refs.lcTableForm.validate(function(a){if(!a)return!1;(t.calculateLC(e),b.getCookie("userName"))?(k({method:"POST",url:"/api/updateSc",data:{sc_id:t.currentRow.sc_id,sc_user:b.getCookie("userName"),sc_date:v(Date.now(),"YYYY-MM-DD HH:mm:ss")}}).then(function(e){console.log(e)}),k({method:"POST",url:e.lc_id?"/api/updatelc":"/api/lcadd",data:e}).then(function(a){t.$message({offset:80,message:"保存成功！",type:"success"}),e.showInput=!1,t.queryLaborCostList()})):t.$message.warning("登录超时，请重新登录！")})},getSummariesLC:function(e){var t=this,a=e.columns,r=e.data,o=[];return a.forEach(function(e,l){if(l!==a.length-2){if(l===a.length-1){var i=r.map(function(t){return Number(t[e.property])});i.every(function(e){return isNaN(e)})?o[l]="":(o[l]=i.reduce(function(e,t){var a=Number(t);return isNaN(a)?e:e+t},0),o[l]=(o[l]*t.lc_percentage/100).toFixed(5))}}else o[l]="合计"}),o[o.length-1]?this.lc_sum=o[o.length-1]:this.lc_sum=0,this.totalMethod(),o}}},Y={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"main"},[a("div",{staticClass:"query"},[a("div",{staticClass:"query-wrapper"},[a("div",{staticClass:"query-info"},[a("span",[e._v("系列：")]),e._v(" "),a("el-input",{attrs:{size:"mini",placeholder:"请输入系列查询",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.seach.apply(null,arguments)}},model:{value:e.queryName,callback:function(t){e.queryName=t},expression:"queryName"}})],1),e._v(" "),a("div",{staticClass:"query-info"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-search",type:"primary",plain:""},on:{click:e.seach}},[e._v("查询")])],1),e._v(" "),a("div",{staticClass:"query-info",staticStyle:{"text-align":"right"}},["1"===e.power||"8"===e.power?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.handleCreate}},[e._v("新增")]):e._e()],1)])]),e._v(" "),a("el-table",{staticClass:"mainTable",attrs:{data:e.tableList,"header-cell-style":{backgroundColor:"rgb(244, 244, 245)"},height:e.tableHeight,"span-method":e.successionSpanMethod,stripe:""}},[a("el-table-column",{attrs:{prop:"sc_name",label:"系列名称",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"sc_type",label:"型号",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"sc_standard",label:"标准",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"sc_discripte",label:"系列说明"}}),e._v(" "),a("el-table-column",{attrs:{prop:"sc_user",label:"最后更新人",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"sc_date",label:"最后更新日期"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return["1"===e.power||"8"===e.power?a("el-button",{attrs:{type:"text",icon:"el-icon-folder-add"},on:{click:function(a){return e.handleDetail(t.row)}}},[e._v("配置")]):e._e(),e._v(" "),"1"===e.power||"8"===e.power?a("el-button",{attrs:{type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.updateProductForm(t.row)}}},[e._v("修改")]):e._e(),e._v(" "),"1"===e.power||"8"===e.power?a("el-button",{attrs:{type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")]):e._e()]}}])})],1),e._v(" "),a("div",{staticClass:"page"},[a("el-pagination",{attrs:{"current-page":e.currentNum,"page-sizes":[50,100,150,200],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.totalNum},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),a("el-dialog",{attrs:{title:"create"===e.handleType?"新增":""+e.editTitle,visible:e.handleAddModal,"before-close":e.handleMainClose},on:{"update:visible":function(t){e.handleAddModal=t}}},[a("el-form",{ref:"ruleForm",staticClass:"form-wrap",attrs:{model:e.mainFormData,rules:e.rules}},[a("el-form-item",{attrs:{prop:"sc_name",label:"系列名称","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入系列名称",clearable:""},model:{value:e.mainFormData.sc_name,callback:function(t){e.$set(e.mainFormData,"sc_name",t)},expression:"mainFormData.sc_name"}})],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"sc_standard",label:"标准","label-width":"100px"}},[a("el-radio-group",{attrs:{disabled:"update"===e.handleType},model:{value:e.mainFormData.sc_standard,callback:function(t){e.$set(e.mainFormData,"sc_standard",t)},expression:"mainFormData.sc_standard"}},e._l(e.standard_options,function(e,t){return a("el-radio",{key:t,attrs:{label:e.label}})}),1)],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"sc_type",label:"型号","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入型号名称",clearable:""},model:{value:e.mainFormData.sc_type,callback:function(t){e.$set(e.mainFormData,"sc_type",t)},expression:"mainFormData.sc_type"}})],1),e._v(" "),a("el-form-item",{attrs:{prop:"sc_discripte",label:"系列说明","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请填写系列说明",type:"textarea",rows:2,clearable:""},model:{value:e.mainFormData.sc_discripte,callback:function(t){e.$set(e.mainFormData,"sc_discripte",t)},expression:"mainFormData.sc_discripte"}})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.callOf("ruleForm")}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.postAddMain("ruleForm")}}},[e._v("保 存")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"《"+e.editTitle+"》系列配置",width:"90%",visible:e.handleEditModal,"before-close":e.handleDetailClose},on:{"update:visible":function(t){e.handleEditModal=t}}},[a("el-collapse",{model:{value:e.activeNames,callback:function(t){e.activeNames=t},expression:"activeNames"}},[a("el-collapse-item",{attrs:{title:"物料",name:"rm"}},[a("el-button",{staticClass:"addBtn",attrs:{disabled:e.rm_btn,type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.addRawMaterialRow}},[e._v("新增")]),e._v(" "),a("el-form",{ref:"rmTableForm",attrs:{model:e.detailObjData,rules:e.rmRules,size:"small"}},[a("el-table",{staticClass:"list-table",staticStyle:{width:"100%"},attrs:{data:e.detailObjData.rawMaterialTableData,"header-cell-style":{backgroundColor:"rgb(217, 236, 255)"},"row-style":{backgroundColor:"rgb(236, 245, 255)"},"max-height":"540"}},[a("el-table-column",{attrs:{label:"#",type:"index",align:"center",width:"60"}}),e._v(" "),a("el-table-column",{attrs:{label:"品号",align:"left",prop:"rm_number","min-width":"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"rawMaterialTableData."+t.$index+".rm_number",rules:e.rmRules.rm_number}},[a("el-autocomplete",{attrs:{"popper-append-to-body":!1,"fetch-suggestions":e.queryRmNumberSearchAsync,placeholder:"请填品号",clearable:""},on:{select:e.handleRmNumberSelect},model:{value:t.row.rm_number,callback:function(a){e.$set(t.row,"rm_number",a)},expression:"scope.row.rm_number"}})],1):a("span",[e._v(e._s(t.row.rm_number))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"品名",align:"left",prop:"rm_material","min-width":"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"rawMaterialTableData."+t.$index+".rm_material",rules:e.rmRules.rm_material}},[a("el-autocomplete",{attrs:{"fetch-suggestions":e.queryNameSearchAsync,placeholder:"请填写物料名称","popper-append-to-body":!1,clearable:""},on:{select:e.handleNameSelect},model:{value:t.row.rm_material,callback:function(a){e.$set(t.row,"rm_material",a)},expression:"scope.row.rm_material"}})],1):a("span",[e._v(e._s(t.row.rm_material))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"种类",align:"center",prop:"rm_itemType"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"rawMaterialTableData."+t.$index+".rm_itemType",rules:e.rmRules.rm_itemType}},[a("el-select",{staticStyle:{width:"70%"},attrs:{size:"mini"},on:{change:e.handleSelectItemType},model:{value:t.row.rm_itemType,callback:function(a){e.$set(t.row,"rm_itemType",a)},expression:"scope.row.rm_itemType"}},e._l(e.itemOtions,function(e,t){return a("el-option",{key:t,attrs:{value:e}})}),1)],1):a("span",[e._v(e._s(t.row.rm_itemType))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"厂商",align:"center",prop:"rm_supplier"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.rm_supplier))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"单价",align:"center",prop:"rm_price"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"rawMaterialTableData."+t.$index+".rm_price",rules:e.rmRules.rm_price}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请填写",min:0,precision:5,clearable:""},model:{value:t.row.rm_price,callback:function(a){e.$set(t.row,"rm_price",a)},expression:"scope.row.rm_price"}})],1):a("span",[e._v(e._s(t.row.rm_price))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"单位",align:"center",prop:"rm_unit"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.rm_unit))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"用量",align:"center",prop:"rm_consumption"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"rawMaterialTableData."+t.$index+".rm_consumption",rules:e.rmRules.rm_consumption}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请填写",min:0,step:1,precision:5,clearable:""},model:{value:t.row.rm_consumption,callback:function(a){e.$set(t.row,"rm_consumption",a)},expression:"scope.row.rm_consumption"}})],1):a("span",[e._v(e._s(t.row.rm_consumption))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"汇率",align:"center",prop:"rm_exchangeRate"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"rawMaterialTableData."+t.$index+".rm_exchangeRate",rules:e.rmRules.rm_exchangeRate}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请填写",min:0,disabled:""},model:{value:t.row.rm_exchangeRate,callback:function(a){e.$set(t.row,"rm_exchangeRate",a)},expression:"scope.row.rm_exchangeRate"}})],1):a("span",[e._v(e._s(t.row.rm_exchangeRate))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"损耗",align:"center",prop:"rm_loss"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"rawMaterialTableData."+t.$index+".rm_loss",rules:e.rmRules.rm_loss}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请填写",step:.1,min:0,clearable:""},model:{value:t.row.rm_loss,callback:function(a){e.$set(t.row,"rm_loss",a)},expression:"scope.row.rm_loss"}})],1):a("span",[e._v(e._s(t.row.rm_loss))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"金额",align:"center",prop:"rm_amounts"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-button",{attrs:{type:"text",icon:"el-icon-search"},on:{click:function(a){return e.calculateRM(t.row)}}}):e._e(),e._v(" "),a("span",[e._v(e._s(t.row.rm_amounts))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-button",{attrs:{type:"text",icon:"el-icon-check"},nativeOn:{click:function(a){return a.preventDefault(),e.saveRawMaterialRow(t.row)}}},[e._v("提交")]):a("el-button",{attrs:{disabled:e.rm_btn,type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.updateRawMaterialRow(t.row,t.$index)}}},[e._v("修改")]),e._v(" "),t.row.showInput?a("el-button",{attrs:{type:"text",icon:"el-icon-close"},nativeOn:{click:function(a){return a.preventDefault(),e.cancelRawMaterialRow(t.row)}}},[e._v("取消")]):a("el-button",{attrs:{disabled:e.rm_btn&&!t.row.showInput,type:"text",icon:"el-icon-delete"},nativeOn:{click:function(a){return a.preventDefault(),e.deleteRawMaterialRow(t.row)}}},[e._v("删除")])]}}])})],1)],1)],1),e._v(" "),a("el-collapse-item",{attrs:{title:"工角",name:"lc"}},[a("el-button",{staticClass:"addBtn",attrs:{disabled:e.lc_btn,type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.addLaborRow}},[e._v("新增")]),e._v(" "),a("el-form",{ref:"lcTableForm",attrs:{model:e.detailObjData,rules:e.lcRules,size:"small"}},[a("el-table",{staticClass:"list-table",staticStyle:{width:"100%"},attrs:{data:e.detailObjData.LaborCostTableData,"header-cell-style":{backgroundColor:"rgb(225, 243, 216)"},"row-style":{backgroundColor:"rgb(240, 249, 235)"},"max-height":"540"}},[a("el-table-column",{attrs:{label:"#",type:"index",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{label:"工角名称",align:"left",prop:"lc_name"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"LaborCostTableData."+t.$index+".lc_name",rules:e.lcRules.lc_name}},[a("el-autocomplete",{attrs:{"popper-append-to-body":!1,"fetch-suggestions":e.queryLCNameSearchAsync,placeholder:"请填写工角名称",clearable:""},on:{select:e.handleLCNameSelect},model:{value:t.row.lc_name,callback:function(a){e.$set(t.row,"lc_name",a)},expression:"scope.row.lc_name"}})],1):a("span",[e._v(e._s(t.row.lc_name))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"所属工厂",align:"center",prop:"lc_factory"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.lc_factory))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"单价",align:"center",prop:"lc_price"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"LaborCostTableData."+t.$index+".lc_price",rules:e.lcRules.lc_price}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请填写",min:0,precision:5,clearable:""},model:{value:t.row.lc_price,callback:function(a){e.$set(t.row,"lc_price",a)},expression:"scope.row.lc_price"}})],1):a("span",[e._v(e._s(t.row.lc_price))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"用量",align:"center",prop:"lc_consumption"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"LaborCostTableData."+t.$index+".lc_consumption",rules:e.lcRules.lc_consumption}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请填写",min:1,step:1,precision:0,clearable:""},model:{value:t.row.lc_consumption,callback:function(a){e.$set(t.row,"lc_consumption",a)},expression:"scope.row.lc_consumption"}})],1):a("span",[e._v(e._s(t.row.lc_consumption))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"汇率",align:"center",prop:"lc_exchangeRate"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-form-item",{attrs:{prop:"LaborCostTableData."+t.$index+".lc_exchangeRate",rules:e.lcRules.lc_exchangeRate}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请填写",min:0,disabled:""},model:{value:t.row.lc_exchangeRate,callback:function(a){e.$set(t.row,"lc_exchangeRate",a)},expression:"scope.row.lc_exchangeRate"}})],1):a("span",[e._v(e._s(t.row.lc_exchangeRate))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showInput?a("el-button",{attrs:{type:"text",icon:"el-icon-check"},on:{click:function(a){return e.saveLaborCostRow(t.row)}}},[e._v("提交")]):a("el-button",{attrs:{disabled:e.lc_btn,type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.updateLaborRow(t.row)}}},[e._v("修改")]),e._v(" "),t.row.showInput?a("el-button",{attrs:{type:"text",icon:"el-icon-close"},on:{click:function(a){return e.cancelLaborRow(t.row)}}},[e._v("取消")]):a("el-button",{attrs:{disabled:e.lc_btn&&!t.row.showInput,type:"text",icon:"el-icon-delete"},nativeOn:{click:function(a){return a.preventDefault(),e.deleteLaborRow(t.row)}}},[e._v("删除")])]}}])})],1)],1)],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:e.resetBTNs}},[e._v("关 闭")])],1)],1)],1)},staticRenderFns:[]};var U=a("VU/8")(A,Y,!1,function(e){a("ucxN")},"data-v-d0e106ca",null).exports,K={name:"Material",data:function(){return{power:b.getCookie("power"),loading:!1,tableHeight:0,goodsTableData:[],current:1,pageSize:20,goodsName:"",goodsNum:"",itemType:"",total:0,handleType:"",handleModal:!1,formData:{rm_itemType:[]},editTitle:"",rules:{rm_material:[{required:!0,message:"请填写品名",trigger:"blur"}],rm_itemType:[{required:!0,message:"请填写种类",trigger:"blur"}],rm_price:[{required:!0,type:"number",message:"请填写单价",trigger:"blur"}],rm_unit:[{required:!0,message:"请填写单位",trigger:"blur"}],rm_consumption:[{required:!0,type:"number",message:"请填写用量",trigger:"blur"}],rm_currencyType:[{required:!0,message:"请选择币种",trigger:"change"}]}}},computed:s()({},Object(D.c)(["FXRates","ct_options","CURRENCY_DIC","standard_options","MODEL_DIC","itemType_options","ITEM_DIC","unit_options"])),mounted:function(){var e=this;this.$nextTick(function(){e.tableHeight=window.innerHeight-136});var t=this;window.onresize=function(){t.resizeFlag&&clearTimeout(t.resizeFlag),t.resizeFlag=setTimeout(function(){t.getTableHeight(),t.resizeFlag=null},100)}},activated:function(){this.getGoods()},methods:{getTableHeight:function(){var e=window.innerHeight-180;this.tableHeight=e<=300?300:window.innerHeight-180},seach:function(){this.current=1,this.pageSize=20,this.getGoods()},getGoods:function(){var e=this;this.loading=!0;var t=new FormData;t.append("current",this.current),t.append("size",this.pageSize),t.append("rm_material",this.goodsName),t.append("rm_number",this.goodsNum),t.append("rm_itemType",this.itemType),k({method:"POST",url:"/api/GoodsAll",data:t}).then(function(t){e.loading=!1,e.total=t.total,e.goodsTableData=t.records}).catch(function(){e.loading=!1})},handleSizeChange:function(e){this.pageSize=e,this.current=1,this.getGoods()},handleCurrentChange:function(e){this.current=e,this.getGoods()},fmtCurrencyType:function(e,t,a){return this.CURRENCY_DIC[a]},handleAddGood:function(){this.formData={rm_itemType:[],rm_currencyType:"CNY"},this.handleCTchange("CNY"),this.handleType="create",this.handleModal=!0},handleUpdateGood:function(e){this.editTitle=e.rm_material,this.formData=s()({},e,{rm_itemType:e.rm_itemType.split(",")}),this.handleType="update",this.handleModal=!0},handleCTchange:function(e){this.formData.rm_exchangeRate=this.FXRates[e]},handleModalClose:function(){this.$refs.ruleForm.resetFields()},callOf:function(e){this.handleModal=!1,this.$refs[e].resetFields(),this.current=1,this.getGoods()},calculateRM:function(){if(""!==!this.formData.rm_price&&""!==!this.formData.rm_consumption){var e=0,t=.97;"油墨"===this.formData.rm_material&&(console.log("油墨来了"),t=1),e=0===this.formData.rm_consumption?this.formData.rm_price/t:this.formData.rm_price/this.formData.rm_consumption/t,this.formData.rm_amounts=e.toFixed(3)}},handlePostForm:function(e){var t=this;this.$refs[e].validate(function(e){if(!e)return!1;var a=s()({},t.formData,{rm_itemType:t.formData.rm_itemType.toString()});"create"===t.handleType&&k({method:"POST",url:"/api/goodsadd",data:a}).then(function(e){t.$message({offset:80,message:"保存成功！",type:"success"}),t.handleModal=!1,t.current=1,t.getGoods()}),"update"===t.handleType&&k({method:"POST",url:"/api/updategoods",data:s()({},a,{updatePersonnel:b.getCookie("userName")})}).then(function(e){t.$message({offset:80,message:"保存成功！",type:"success"}),t.handleModal=!1,t.current=1,t.getGoods()})})},handleDelete:function(e){var t=this;this.$confirm("您是否要删除”"+e.rm_material+"“产品信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){k({method:"POST",url:"/api/deletegoods",data:{goodsid:e.goodsid}}).then(function(e){t.$message({offset:80,type:"success",message:"操作成功！"}),t.current=1,t.getGoods()})}).catch(function(){t.$message({offset:80,type:"info",message:"已取消删除！"})})}}},G={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"main"},[a("div",{staticClass:"query"},[a("div",{staticClass:"query-wrapper"},[a("div",{staticClass:"query-info"},[a("span",[e._v("品号：")]),e._v(" "),a("el-input",{attrs:{size:"mini",placeholder:"请输入品号查询",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.seach.apply(null,arguments)}},model:{value:e.goodsNum,callback:function(t){e.goodsNum=t},expression:"goodsNum"}})],1),e._v(" "),a("div",{staticClass:"query-info"},[a("span",[e._v("品名：")]),e._v(" "),a("el-input",{attrs:{size:"mini",placeholder:"请输入品名查询",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.seach.apply(null,arguments)}},model:{value:e.goodsName,callback:function(t){e.goodsName=t},expression:"goodsName"}})],1),e._v(" "),a("div",{staticClass:"query-info"},[a("span",[e._v("种类：")]),e._v(" "),a("el-select",{attrs:{size:"mini",placeholder:"请选择种类查询",clearable:""},on:{change:e.seach},model:{value:e.itemType,callback:function(t){e.itemType=t},expression:"itemType"}},e._l(e.itemType_options,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.label}})}),1)],1),e._v(" "),a("div",{staticClass:"query-info"},[a("el-button",{attrs:{size:"mini",type:"primary",plain:"",icon:"el-icon-search"},on:{click:e.seach}},[e._v("查询")])],1),e._v(" "),"1"===e.power||"2"===e.power?a("div",{staticClass:"query-info",staticStyle:{"text-align":"right"}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.handleAddGood}},[e._v("新增")])],1):e._e()])]),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",staticClass:"mainTable",attrs:{data:e.goodsTableData,"header-cell-style":{backgroundColor:"rgb(244, 244, 245)"},height:e.tableHeight,stripe:""}},[a("el-table-column",{attrs:{label:"#",type:"index",align:"center",width:"60"}}),e._v(" "),a("el-table-column",{attrs:{prop:"rm_number",label:"品号","min-width":"110"}}),e._v(" "),a("el-table-column",{attrs:{prop:"rm_material",label:"品名","min-width":"140"}}),e._v(" "),a("el-table-column",{attrs:{prop:"rm_itemType",label:"种类"}}),e._v(" "),a("el-table-column",{attrs:{prop:"rm_supplier",label:"厂商"}}),e._v(" "),a("el-table-column",{attrs:{prop:"rm_price",label:"价格"}}),e._v(" "),a("el-table-column",{attrs:{prop:"rm_unit",label:"单位"}}),e._v(" "),a("el-table-column",{attrs:{prop:"rm_currencyType",label:"币种",formatter:e.fmtCurrencyType}}),e._v(" "),a("el-table-column",{attrs:{prop:"rm_exchangeRate",label:"汇率"}}),e._v(" "),"1"===e.power||"2"===e.power||"8"===e.power?a("el-table-column",{attrs:{label:"操作",align:"center","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdateGood(t.row)}}},[e._v("修改")]),e._v(" "),a("el-button",{attrs:{type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}],null,!1,4031228013)}):e._e()],1),e._v(" "),a("div",{staticClass:"page"},[a("el-pagination",{attrs:{"current-page":e.current,"page-sizes":[20,50,100],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),a("el-dialog",{attrs:{title:"create"===e.handleType?"新增":"修改《"+e.editTitle+"》内容",visible:e.handleModal,"close-on-click-modal":!1,"show-close":!1},on:{"update:visible":function(t){e.handleModal=t},close:e.handleModalClose}},[a("el-form",{ref:"ruleForm",staticClass:"form-wrap",attrs:{model:e.formData,rules:e.rules,size:"mini"}},["update"===e.handleType?a("el-form-item",{attrs:{prop:"rm_number",label:"品号","label-width":"60px"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.formData.rm_number,callback:function(t){e.$set(e.formData,"rm_number",t)},expression:"formData.rm_number"}})],1):e._e(),e._v(" "),a("el-form-item",{attrs:{prop:"rm_material",label:"品名","label-width":"60px"}},[a("el-input",{attrs:{placeholder:"品名命名规则请尽量遵循：总称：颜色+材质+尺寸+特性(+厂商)",clearable:""},model:{value:e.formData.rm_material,callback:function(t){e.$set(e.formData,"rm_material",t)},expression:"formData.rm_material"}})],1),e._v(" "),a("el-form-item",{attrs:{prop:"rm_itemType",label:"种类","label-width":"60px"}},[a("el-checkbox-group",{model:{value:e.formData.rm_itemType,callback:function(t){e.$set(e.formData,"rm_itemType",t)},expression:"formData.rm_itemType"}},e._l(e.itemType_options,function(e,t){return a("el-checkbox",{key:t,attrs:{label:e.label}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{prop:"rm_supplier",label:"厂商","label-width":"60px"}},[a("el-input",{attrs:{placeholder:"请输入厂商",clearable:""},model:{value:e.formData.rm_supplier,callback:function(t){e.$set(e.formData,"rm_supplier",t)},expression:"formData.rm_supplier"}})],1),e._v(" "),a("el-form-item",{attrs:{prop:"rm_price",label:"价格","label-width":"60px"}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请输入单价",min:0,precision:5,clearable:""},model:{value:e.formData.rm_price,callback:function(t){e.$set(e.formData,"rm_price",t)},expression:"formData.rm_price"}})],1),e._v(" "),a("el-form-item",{staticStyle:{position:"relative"},attrs:{prop:"rm_unit",label:"单位","label-width":"60px"}},[a("el-select",{attrs:{placeholder:"注意成品用量变化，并通知主品配置人员",filterable:"","allow-create":"","default-first-optionclearable":""},model:{value:e.formData.rm_unit,callback:function(t){e.$set(e.formData,"rm_unit",t)},expression:"formData.rm_unit"}},e._l(e.unit_options,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),"update"===e.handleType?a("p",{staticStyle:{padding:"0",margin:"0","font-size":"12px",color:"#F56C6C",position:"absolute",top:"64%",left:"148px"}},[e._v("注意成品用量变化，并通知主品配置人员")]):e._e(),e._v(" "),a("el-form-item",{attrs:{prop:"rm_currencyType",label:"币种","label-width":"60px"}},[a("el-select",{attrs:{placeholder:"请选择币种"},on:{change:e.handleCTchange},model:{value:e.formData.rm_currencyType,callback:function(t){e.$set(e.formData,"rm_currencyType",t)},expression:"formData.rm_currencyType"}},e._l(e.ct_options,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{prop:"rm_exchangeRate",label:"汇率","label-width":"60px"}},[a("el-input-number",{attrs:{"controls-position":"right",min:0,clearable:""},model:{value:e.formData.rm_exchangeRate,callback:function(t){e.$set(e.formData,"rm_exchangeRate",t)},expression:"formData.rm_exchangeRate"}})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.callOf("ruleForm")}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.handlePostForm("ruleForm")}}},[e._v("保 存")])],1)],1)],1)},staticRenderFns:[]};var W=a("VU/8")(K,G,!1,function(e){a("NhfS")},"data-v-502a4771",null).exports,V={name:"LaborCost",data:function(){return{loading:!1,tableHeight:0,laborTableData:[],current:1,pageSize:20,laborName:"",factory:"",total:0,handleType:"",handleModal:!1,formData:{lc_name:"",lc_price:0},editTitle:"",rules:{lc_name:[{required:!0,message:"请填写名称",trigger:"blur"}],lc_factory:[{required:!0,message:"请选择",trigger:"blur"}],lc_price:[{required:!0,type:"number",message:"请填写单价",trigger:"blur"}]}}},computed:s()({},Object(D.c)(["FXRates","CURRENCY_DIC","ct_options","model_options"])),mounted:function(){var e=this;this.$nextTick(function(){e.tableHeight=window.innerHeight-136});var t=this;window.onresize=function(){t.resizeFlag&&clearTimeout(t.resizeFlag),t.resizeFlag=setTimeout(function(){t.getTableHeight(),t.resizeFlag=null},100)}},activated:function(){this.getLabors()},methods:{fmtCurrencyType:function(e,t,a){return this.CURRENCY_DIC[a]},getTableHeight:function(){var e=window.innerHeight-180;this.tableHeight=e<=300?300:window.innerHeight-180},handleCTchange:function(e){},seach:function(){this.current=1,this.pageSize=20,this.getLabors()},getLabors:function(){var e=this;this.loading=!0;var t=new FormData;t.append("current",this.current),t.append("size",this.pageSize),t.append("lc_name",this.laborName),t.append("lc_factory",this.factory),k({method:"POST",url:"/api/LaborCostAll",data:t}).then(function(t){e.loading=!1,e.total=t.total,e.laborTableData=t.records}).catch(function(){e.loading=!1})},handleSizeChange:function(e){this.pageSize=e,this.getLabors()},handleCurrentChange:function(e){this.current=e,this.getLabors()},handleAddLabor:function(){this.formData={lc_currencyType:"CNY"},this.handleType="create",this.handleModal=!0},handleUpdateLabor:function(e){this.editTitle=e.lc_name,this.formData=e,this.handleType="update",this.handleModal=!0},handleModalClose:function(e){var t=this;this.$confirm("确认关闭吗？").then(function(a){e(),t.formData={},t.$refs.ruleForm.resetFields()}).catch(function(e){})},callOf:function(e){this.handleModal=!1,this.formData={},this.$refs[e].resetFields(),this.getLabors()},calculateLC:function(){if(""!==!this.formData.lc_price&&""!==!this.formData.lc_exchangeRate&&""!==this.formData.lc_consumption){var e=0;e=0===this.formData.lc_consumption?this.formData.lc_price*this.formData.lc_exchangeRate:this.formData.lc_price*this.formData.lc_exchangeRate/this.formData.lc_consumption,this.formData.lc_amounts=e.toFixed(3)}},handlePostForm:function(e){var t=this;this.$refs[e].validate(function(e){if(!e)return!1;t.formData.lc_currencyType="CNY",t.formData.lc_exchangeRate=1,t.formData.lc_amounts=t.formData.lc_price*t.formData.lc_exchangeRate,"create"===t.handleType&&k({method:"POST",url:"/api/LaborCostadd",data:t.formData}).then(function(e){t.$message({offset:80,message:"保存成功！",type:"success"}),t.handleModal=!1,t.getLabors()}),"update"===t.handleType&&k({method:"POST",url:"/api/updateLaborCost",data:t.formData}).then(function(e){t.$message({offset:80,message:"保存成功！",type:"success"}),t.handleModal=!1,t.getLabors()})})},handleDelete:function(e){var t=this;this.$confirm("您是否要删除”"+e.lc_name+"“信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){k({method:"POST",url:"/api/deleteLaborCost",data:{laborCostid:e.laborCostid}}).then(function(e){t.$message({offset:80,type:"success",message:"操作成功！"}),t.current=1,t.getLabors()})}).catch(function(){t.$message({offset:80,type:"info",message:"已取消删除！"})})}}},X={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"main"},[a("div",{staticClass:"query"},[a("div",{staticClass:"query-wrapper"},[a("div",{staticClass:"query-info"},[a("span",[e._v("工角名称：")]),e._v(" "),a("el-input",{attrs:{size:"mini",placeholder:"请输入工角名称查询",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.seach.apply(null,arguments)}},model:{value:e.laborName,callback:function(t){e.laborName=t},expression:"laborName"}})],1),e._v(" "),a("div",{staticClass:"query-info",staticStyle:{"line-height":"32px"}},[a("span",[e._v("加工工厂：")]),e._v(" "),a("el-select",{attrs:{placeholder:"可选择工厂查询",size:"mini",clearable:""},on:{change:e.seach},model:{value:e.factory,callback:function(t){e.factory=t},expression:"factory"}},[a("el-option",{attrs:{value:"知腾"}}),e._v(" "),a("el-option",{attrs:{value:"迅安"}})],1)],1),e._v(" "),a("div",{staticStyle:{"text-align":"left",display:"inline"}},[a("el-button",{attrs:{size:"mini",icon:"el-icon-search",type:"primary",plain:""},on:{click:e.seach}},[e._v("查询")])],1),e._v(" "),a("div",{staticClass:"query-info",staticStyle:{"text-align":"right"}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.handleAddLabor}},[e._v("新增")])],1)])]),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",staticClass:"mainTable",attrs:{data:e.laborTableData,"header-cell-style":{backgroundColor:"rgb(244, 244, 245)"},height:e.tableHeight,stripe:""}},[a("el-table-column",{attrs:{label:"#",type:"index",align:"center",width:"60"}}),e._v(" "),a("el-table-column",{attrs:{prop:"lc_name",label:"工角名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"lc_factory",label:"所属工厂"}}),e._v(" "),a("el-table-column",{attrs:{prop:"lc_price",label:"单价(RMB)"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdateLabor(t.row)}}},[e._v("修改")]),e._v(" "),a("el-button",{attrs:{type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),e._v(" "),a("div",{staticClass:"page"},[a("el-pagination",{attrs:{"current-page":e.current,"page-sizes":[20,50,100],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),a("el-dialog",{attrs:{title:"create"===e.handleType?"新增":"修改《"+e.editTitle+"》内容",visible:e.handleModal,"before-close":e.handleModalClose},on:{"update:visible":function(t){e.handleModal=t}}},[a("el-form",{ref:"ruleForm",staticClass:"form-wrap",attrs:{model:e.formData,rules:e.rules,size:"mini"}},[a("el-form-item",{attrs:{prop:"lc_name",label:"工角名称：","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入工角对应的型号，如：9500-N95",clearable:""},model:{value:e.formData.lc_name,callback:function(t){e.$set(e.formData,"lc_name",t)},expression:"formData.lc_name"}})],1),e._v(" "),a("el-form-item",{attrs:{prop:"lc_factory",label:"所属工厂：","label-width":"100px"}},[a("el-radio-group",{model:{value:e.formData.lc_factory,callback:function(t){e.$set(e.formData,"lc_factory",t)},expression:"formData.lc_factory"}},[a("el-radio",{attrs:{label:"迅安"}}),e._v(" "),a("el-radio",{attrs:{label:"知腾"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{prop:"lc_price",label:"工角单价：","label-width":"100px"}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请输入单价",min:0,clearable:""},model:{value:e.formData.lc_price,callback:function(t){e.$set(e.formData,"lc_price",t)},expression:"formData.lc_price"}})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.callOf("ruleForm")}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.handlePostForm("ruleForm")}}},[e._v("保 存")])],1)],1)],1)},staticRenderFns:[]};var J=a("VU/8")(V,X,!1,function(e){a("WPuk")},"data-v-75ff9984",null).exports,Z={name:"Freight",data:function(){return{headerFilters:{},loading:!1,tableHeight:0,freightTableData:[],current:1,pageSize:20,total:0,handleType:"",handleModal:!1,formData:{fr_factory:"",fr_price:0,fr_cbm:0,fr_port:""},editTitle:"",rules:{fr_factory:[{required:!0,message:"请选择",trigger:"blur"}],shippingType:[{required:!0,message:"请选择",trigger:"blur"}],fr_price:[{required:!0,type:"number",message:"请填写",trigger:"blur"}],fr_port:[{required:!0,message:"请选择",trigger:"blur"}],fr_cbm:[{required:!0,message:"请填写",trigger:"blur"}]}}},mounted:function(){var e=this;this.$nextTick(function(){e.tableHeight=window.innerHeight-136});var t=this;window.onresize=function(){t.resizeFlag&&clearTimeout(t.resizeFlag),t.resizeFlag=setTimeout(function(){t.getTableHeight(),t.resizeFlag=null},100)}},activated:function(){this.getFreight()},methods:{getTableHeight:function(){var e=window.innerHeight-180;this.tableHeight=e<=300?300:window.innerHeight-180},getFreight:function(){var e=this;this.loading=!0;var t=new FormData;t.append("current",this.current),t.append("size",this.pageSize),t.append("fr_cbm",""),k({method:"POST",url:"/api/FreightAll",data:t}).then(function(t){var a,r;e.loading=!1,e.total=t.total,e.freightTableData=t.records,e.headerFilters=(a=e.freightTableData,r={},a.length&&(f()(a[0]).forEach(function(e){r[e]=[]}),a.forEach(function(e){var t=function(t){r.hasOwnProperty(t)&&!r[t].find(function(a){return a.text===e[t]})&&r[t].push({text:e[t],value:e[t]})};for(var a in e)t(a)})),r)}).catch(function(){e.loading=!1})},handleSizeChange:function(e){this.pageSize=e,this.getFreight()},handleCurrentChange:function(e){this.current=e,this.getFreight()},filterHeaders:function(e,t,a){return t[a.property]===e},handleAdd:function(){this.handleType="create",this.formData={},this.handleModal=!0},handleUpdateLabor:function(e){this.editTitle=e.fr_name,this.formData=e,this.handleType="update",this.handleModal=!0},handleModalClose:function(e){var t=this;this.$confirm("确认关闭吗？").then(function(a){e(),t.formData={},t.$refs.ruleForm.resetFields()}).catch(function(e){})},callOf:function(e){this.handleModal=!1,this.formData={},this.$refs[e].resetFields()},handlePostForm:function(e){var t=this;this.$refs[e].validate(function(e){if(!e)return!1;t.formData.updateTime=v(Date.now(),"YYYY-MM-DD HH:mm:ss"),"create"===t.handleType&&k({method:"POST",url:"/api/Freightadd",data:t.formData}).then(function(e){t.$message({offset:80,message:"保存成功！",type:"success"}),t.handleModal=!1,t.getFreight()}),"update"===t.handleType&&k({method:"POST",url:"/api/updateFreight",data:t.formData}).then(function(e){t.$message({offset:80,message:"保存成功！",type:"success"}),t.handleModal=!1,t.getFreight()})})},handleDelete:function(e){var t=this;this.$confirm("您是否要删除当前数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){k({method:"POST",url:"/api/deleteFreight",data:{freightid:e.freightid}}).then(function(e){t.$message({offset:80,type:"success",message:"操作成功！"}),t.current=1,t.getFreight()})}).catch(function(){t.$message({offset:80,type:"info",message:"已取消删除！"})})}}},Q={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"main"},[a("div",{staticClass:"query"},[a("div",{staticClass:"query-wrapper"},[a("div",{staticClass:"query-info",staticStyle:{"text-align":"right"}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")])],1)])]),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",staticClass:"mainTable",attrs:{data:e.freightTableData,"header-cell-style":{backgroundColor:"rgb(244, 244, 245)"},height:e.tableHeight,stripe:""}},[a("el-table-column",{attrs:{label:"#",type:"index",align:"center",width:"60"}}),e._v(" "),a("el-table-column",{attrs:{prop:"name",label:"港口"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n        "+e._s(t.row.fr_factory+"到"+t.row.fr_port)+"\n      ")]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"shippingType",label:"出货类别",filters:e.headerFilters.shippingType,"filter-method":e.filterHeaders},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n        "+e._s("深圳"===t.row.fr_port&&"整柜"===t.row.shippingType?t.row.shippingType+"("+t.row.routes+")":t.row.shippingType)+"\n      ")]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"fr_cbm",label:"体积(CBM)"}}),e._v(" "),a("el-table-column",{attrs:{prop:"fr_price",label:"价格(RMB)"}}),e._v(" "),a("el-table-column",{attrs:{prop:"others",label:"其它费用(RMB)"}}),e._v(" "),a("el-table-column",{attrs:{prop:"remark",label:"备注"}}),e._v(" "),a("el-table-column",{attrs:{prop:"updateTime",label:"更新时间"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdateLabor(t.row)}}},[e._v("修改")]),e._v(" "),a("el-button",{attrs:{type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),e._v(" "),a("div",{staticClass:"page"},[a("el-pagination",{attrs:{"current-page":e.current,"page-sizes":[20,50,100],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),a("el-dialog",{attrs:{title:"create"===e.handleType?"新增":"修改《"+e.editTitle+"》内容",width:"40%",visible:e.handleModal,"before-close":e.handleModalClose},on:{"update:visible":function(t){e.handleModal=t}}},[a("el-form",{ref:"ruleForm",staticClass:"form-wrap",attrs:{model:e.formData,rules:e.rules,size:"mini"}},[a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"fr_factory",label:"出货工厂: ","label-width":"100px"}},[a("el-radio-group",{model:{value:e.formData.fr_factory,callback:function(t){e.$set(e.formData,"fr_factory",t)},expression:"formData.fr_factory"}},[a("el-radio",{attrs:{label:"知腾"}}),e._v(" "),a("el-radio",{attrs:{label:"迅安"}})],1)],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"fr_port",label:"出货港口: ","label-width":"100px"}},[a("el-radio-group",{model:{value:e.formData.fr_port,callback:function(t){e.$set(e.formData,"fr_port",t)},expression:"formData.fr_port"}},[a("el-radio",{attrs:{label:"上海"}}),e._v(" "),a("el-radio",{attrs:{label:"武汉"}}),e._v(" "),a("el-radio",{attrs:{label:"深圳"}})],1)],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"shippingType",label:"出货类别: ","label-width":"100px"}},[a("el-radio-group",{model:{value:e.formData.shippingType,callback:function(t){e.$set(e.formData,"shippingType",t)},expression:"formData.shippingType"}},[a("el-radio",{attrs:{label:"整柜"}}),e._v(" "),a("el-radio",{attrs:{label:"散货"}})],1)],1),e._v(" "),"深圳"===e.formData.fr_port&&"整柜"===e.formData.shippingType?a("el-form-item",{staticStyle:{"text-align":"left"},attrs:{prop:"routes",label:"出货路线: ","label-width":"100px"}},[a("el-radio-group",{model:{value:e.formData.routes,callback:function(t){e.$set(e.formData,"routes",t)},expression:"formData.routes"}},[a("el-radio",{attrs:{label:"欧美线"}}),e._v(" "),a("el-radio",{attrs:{label:"亚洲线"}})],1)],1):e._e(),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left",width:"95%"},attrs:{prop:"fr_cbm",label:"CBM: ","label-width":"100px"}},[a("el-input",{attrs:{size:"small",placeholder:"请填写体积(CBM)"},model:{value:e.formData.fr_cbm,callback:function(t){e.$set(e.formData,"fr_cbm",t)},expression:"formData.fr_cbm"}})],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left",width:"95%"},attrs:{prop:"fr_price",label:"价格: ","label-width":"100px"}},[a("el-input-number",{attrs:{size:"small",placeholder:"请填写价格(RMB)","controls-position":"right"},model:{value:e.formData.fr_price,callback:function(t){e.$set(e.formData,"fr_price",t)},expression:"formData.fr_price"}})],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left",width:"95%"},attrs:{prop:"others",label:"其它费用: ","label-width":"100px"}},[a("el-input-number",{attrs:{size:"small",placeholder:"有无其它费用(RMB)","controls-position":"right"},model:{value:e.formData.others,callback:function(t){e.$set(e.formData,"others",t)},expression:"formData.others"}})],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left",width:"95%"},attrs:{prop:"remark",label:"备注: ","label-width":"100px"}},[a("el-input",{attrs:{size:"small",placeholder:"备注说明",type:"textarea"},model:{value:e.formData.remark,callback:function(t){e.$set(e.formData,"remark",t)},expression:"formData.remark"}})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.callOf("ruleForm")}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.handlePostForm("ruleForm")}}},[e._v("保 存")])],1)],1)],1)},staticRenderFns:[]};var ee=a("VU/8")(Z,Q,!1,function(e){a("YAVv")},"data-v-0c744012",null).exports,te={name:"User",data:function(){return{loading:!1,tableHeight:0,userTableData:[],current:1,pageSize:20,total:0,handleType:"",handleModal:!1,formData:{},editTitle:"",rules:{userName:[{required:!0,message:"请填写",trigger:"blur"}],password:[{required:!0,message:"请填写",trigger:"blur"}],company:[{required:!0,message:"请选择",trigger:"blur"}],power:[{required:!0,message:"请选择",trigger:"blur"}]}}},computed:s()({},Object(D.c)(["power_options","POWER_DIC","company_options"])),mounted:function(){var e=this;this.$nextTick(function(){e.tableHeight=window.innerHeight-136});var t=this;window.onresize=function(){t.resizeFlag&&clearTimeout(t.resizeFlag),t.resizeFlag=setTimeout(function(){t.getTableHeight(),t.resizeFlag=null},100)}},activated:function(){this.getUserList()},methods:{getTableHeight:function(){var e=window.innerHeight-180;this.tableHeight=e<=300?300:window.innerHeight-180},getUserList:function(){var e=this;this.loading=!0;var t=new FormData;t.append("current",this.current),t.append("size",this.pageSize),t.append("userName",""),k({method:"POST",url:"/api/selectUser",data:t}).then(function(t){e.loading=!1,e.total=t.total,e.userTableData=t.records}).catch(function(){e.loading=!1})},handleSizeChange:function(e){this.pageSize=e,this.getUserList()},handleCurrentChange:function(e){this.current=e,this.getUserList()},handleAddUser:function(){this.handleType="create",this.formData={},this.handleModal=!0},handleUpdateLabor:function(e){this.editTitle=e.userName,this.formData=e,this.handleType="update",this.handleModal=!0},handleModalClose:function(e){var t=this;this.$confirm("确认关闭吗？").then(function(a){e(),t.formData={},t.$refs.ruleForm.resetFields(),t.getUserList()}).catch(function(e){})},callOf:function(e){this.handleModal=!1,this.formData={},this.$refs[e].resetFields(),this.getUserList()},handlePostForm:function(e){var t=this;this.$refs[e].validate(function(e){if(!e)return!1;"create"===t.handleType&&k({method:"POST",url:"/api/addUserTo",data:t.formData}).then(function(e){t.$message({offset:80,message:"保存成功！",type:"success"}),t.handleModal=!1,t.getUserList()}),"update"===t.handleType&&k({method:"POST",url:"/api/updateUser",data:t.formData}).then(function(e){t.$message({offset:80,message:"保存成功！",type:"success"}),t.handleModal=!1,t.getUserList()})})},handleDelete:function(e){var t=this;this.$confirm("您是否要删除当前用户数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){k({method:"POST",url:"/api/deleteUser",data:{userid:e.userid}}).then(function(e){t.$message({offset:80,type:"success",message:"操作成功！"}),t.current=1,t.getUserList()})}).catch(function(){t.$message({offset:80,type:"info",message:"已取消删除！"})})}}},ae={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"main"},[a("div",{staticClass:"query"},[a("div",{staticClass:"query-wrapper"},[a("div",{staticClass:"query-info",staticStyle:{"text-align":"right"}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.handleAddUser}},[e._v("新增")])],1)])]),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",staticClass:"mainTable",attrs:{data:e.userTableData,"header-cell-style":{backgroundColor:"rgb(244, 244, 245)"},height:e.tableHeight,stripe:""}},[a("el-table-column",{attrs:{label:"#",type:"index",align:"center",width:"60"}}),e._v(" "),a("el-table-column",{attrs:{prop:"userName",label:"用户名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"company",label:"所属公司"}}),e._v(" "),a("el-table-column",{attrs:{prop:"power",label:"用户权限"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.POWER_DIC[t.row.power]))])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"registrationdate",label:"注册时间"}}),e._v(" "),a("el-table-column",{attrs:{prop:"last_login_time",label:"最后登录时间"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdateLabor(t.row)}}},[e._v("修改")]),e._v(" "),a("el-button",{attrs:{type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),e._v(" "),a("div",{staticClass:"page"},[a("el-pagination",{attrs:{"current-page":e.current,"page-sizes":[20,50,100],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),a("el-dialog",{attrs:{title:"create"===e.handleType?"新增":"修改《"+e.editTitle+"》内容",visible:e.handleModal,"before-close":e.handleModalClose},on:{"update:visible":function(t){e.handleModal=t}}},[a("el-form",{ref:"ruleForm",staticClass:"form-wrap",attrs:{model:e.formData,rules:e.rules,size:"mini"}},[a("el-form-item",{staticStyle:{"text-align":"left",width:"90%"},attrs:{prop:"userName",label:"用户名称: ","label-width":"100px"}},[a("el-input",{attrs:{size:"small",placeholder:"请填写"},model:{value:e.formData.userName,callback:function(t){e.$set(e.formData,"userName",t)},expression:"formData.userName"}})],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left",width:"90%"},attrs:{prop:"password",label:"登录密码: ","label-width":"100px"}},[a("el-input",{attrs:{type:"password",size:"small",placeholder:"请填写"},model:{value:e.formData.password,callback:function(t){e.$set(e.formData,"password",t)},expression:"formData.password"}})],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left",width:"90%"},attrs:{prop:"company",label:"所属公司: ","label-width":"100px"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.formData.company,callback:function(t){e.$set(e.formData,"company",t)},expression:"formData.company"}},e._l(e.company_options,function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})}),1)],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left",width:"90%"},attrs:{prop:"power",label:"权限分配: ","label-width":"100px"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.formData.power,callback:function(t){e.$set(e.formData,"power",t)},expression:"formData.power"}},e._l(e.power_options,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.callOf("ruleForm")}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.handlePostForm("ruleForm")}}},[e._v("保 存")])],1)],1)],1)},staticRenderFns:[]};var re=a("VU/8")(te,ae,!1,function(e){a("YVpH")},"data-v-581a76f0",null).exports,oe={name:"Rates",data:function(){return{power:b.getCookie("power"),loading:!1,tableHeight:0,rateTableData:[],handleModal:!1,formData:{},editTitle:"",rules:{currency:[{required:!0,message:"请填写",trigger:"blur"}],exchangerate:[{required:!0,type:"number",message:"请填写",trigger:"blur"}]}}},computed:s()({},Object(D.c)(["power_options","POWER_DIC"])),mounted:function(){var e=this;this.$nextTick(function(){e.tableHeight=window.innerHeight-136});var t=this;window.onresize=function(){t.resizeFlag&&clearTimeout(t.resizeFlag),t.resizeFlag=setTimeout(function(){t.getTableHeight(),t.resizeFlag=null},100)}},activated:function(){this.getRateList()},methods:{fmtExchangerate:function(e,t,a){return a.toFixed(2)},getTableHeight:function(){var e=window.innerHeight-180;this.tableHeight=e<=300?300:window.innerHeight-180},getRateList:function(){var e=this;this.loading=!0,k({method:"POST",url:"/api/selectMoney"}).then(function(t){e.loading=!1,e.rateTableData=t}).catch(function(){e.loading=!1})},handleUpdateLabor:function(e){this.editTitle=e.name,this.formData=e,this.handleModal=!0},handleModalClose:function(e){var t=this;this.$confirm("确认关闭吗？").then(function(a){e(),t.formData={},t.$refs.ruleForm.resetFields(),t.getRateList()}).catch(function(e){})},callOf:function(e){this.handleModal=!1,this.formData={},this.$refs[e].resetFields(),this.getRateList()},handlePostForm:function(e){var t=this;this.$refs[e].validate(function(e){e&&(t.formData.modifyuser=b.getCookie("userName"),k({method:"POST",url:"/api/updateMoney",data:t.formData}).then(function(e){t.$message({offset:80,message:"保存成功！",type:"success"}),t.handleModal=!1,t.getRateList()}))})},handleDelete:function(e){var t=this;this.$confirm("您是否要删除当前汇率?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){k({method:"POST",url:"/api/deleteUser",data:{userid:e.userid}}).then(function(e){t.$message({offset:80,type:"success",message:"操作成功！"}),t.current=1,t.getRateList()})}).catch(function(){t.$message({offset:80,type:"info",message:"已取消删除！"})})}}},le={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"main"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",staticClass:"mainTable",attrs:{data:e.rateTableData,"header-cell-style":{backgroundColor:"rgb(244, 244, 245)"},height:e.tableHeight,stripe:""}},[a("el-table-column",{attrs:{label:"#",type:"index",align:"center",width:"60"}}),e._v(" "),a("el-table-column",{attrs:{prop:"name",label:"名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"currency",label:"币种"}}),e._v(" "),a("el-table-column",{attrs:{prop:"exchangerate",label:"汇率",formatter:e.fmtExchangerate}}),e._v(" "),a("el-table-column",{attrs:{prop:"modifyuser",label:"维护人员"}}),e._v(" "),a("el-table-column",{attrs:{prop:"mtime",label:"维护时间"}}),e._v(" "),"1"===e.power||"5"===e.power?a("el-table-column",{attrs:{label:"操作",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdateLabor(t.row)}}},[e._v("修改")])]}}],null,!1,174385452)}):e._e()],1),e._v(" "),a("el-dialog",{attrs:{title:"修改《"+e.editTitle+"》汇率",visible:e.handleModal,"before-close":e.handleModalClose},on:{"update:visible":function(t){e.handleModal=t}}},[a("el-form",{ref:"ruleForm",staticClass:"form-wrap",attrs:{model:e.formData,rules:e.rules,size:"mini"}},[a("el-form-item",{staticStyle:{"text-align":"left",width:"90%"},attrs:{prop:"currency",label:"币种: ","label-width":"100px"}},[a("el-input",{attrs:{size:"small",disabled:""},model:{value:e.formData.currency,callback:function(t){e.$set(e.formData,"currency",t)},expression:"formData.currency"}})],1),e._v(" "),a("el-form-item",{staticStyle:{"text-align":"left",width:"90%"},attrs:{prop:"exchangerate",label:"汇率: ","label-width":"100px"}},[a("el-input-number",{attrs:{size:"small",placeholder:"请填写","controls-position":"right"},model:{value:e.formData.exchangerate,callback:function(t){e.$set(e.formData,"exchangerate",t)},expression:"formData.exchangerate"}})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.callOf("ruleForm")}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.handlePostForm("ruleForm")}}},[e._v("保 存")])],1)],1)],1)},staticRenderFns:[]};var ie=a("VU/8")(oe,le,!1,function(e){a("x+pM")},"data-v-23dd7c08",null).exports,ne={name:"Package",data:function(){return{power:b.getCookie("power"),loading:!1,tableHeight:0,packTableData:[],current:1,pageSize:20,packName:"",packNum:"",total:0,handleType:"",handleModal:!1,formData:{pk_number:0,pk_material:"",pk_itemType:"",pk_supplier:"",pk_price:0,pk_unit:"",pk_currencyType:"",pk_exchangeRate:1},editTitle:"",rules:{pk_number:[{required:!0,message:"请填写品号",trigger:"blur"}],pk_material:[{required:!0,message:"请填写品名",trigger:"blur"}],pk_itemType:[{required:!0,message:"请选择种类",trigger:"change"}],pk_length:[{required:!0,message:"请填写",trigger:"blur"}],pk_width:[{required:!0,message:"请填写",trigger:"blur"}],pk_height:[{required:!0,message:"请填写",trigger:"blur"}],pk_price:[{required:!0,type:"number",message:"请填写单价",trigger:"blur"}],pk_unit:[{required:!0,message:"请填写单位",trigger:"blur"}],pk_currencyType:[{required:!0,message:"请选择币种",trigger:"change"}]}}},computed:s()({},Object(D.c)(["FXRates","ct_options","CURRENCY_DIC","pk_itemType_options","PK_ITEM_DIC","unit_options"])),mounted:function(){var e=this;this.$nextTick(function(){e.tableHeight=window.innerHeight-136});var t=this;window.onresize=function(){t.resizeFlag&&clearTimeout(t.resizeFlag),t.resizeFlag=setTimeout(function(){t.getTableHeight(),t.resizeFlag=null},100)}},activated:function(){this.getPackage()},methods:{fmtType:function(e,t,a){return this.PK_ITEM_DIC[a]},getTableHeight:function(){var e=window.innerHeight-180;this.tableHeight=e<=300?300:window.innerHeight-180},seach:function(){this.current=1,this.pageSize=20,this.getPackage()},getPackage:function(){var e=this;this.loading=!0;var t=new FormData;t.append("current",this.current),t.append("size",this.pageSize),t.append("pk_material",this.packName),t.append("pk_number",this.packNum),k({method:"POST",url:"/api/PackAll",data:t}).then(function(t){e.loading=!1,e.total=t.total,e.packTableData=t.records}).catch(function(){e.loading=!1})},handleSizeChange:function(e){this.pageSize=e,this.getPackage()},handleCurrentChange:function(e){this.current=e,this.getPackage()},fmtCurrencyType:function(e,t,a){return this.CURRENCY_DIC[a]},handleAddPack:function(){this.formData={},this.handleType="create",this.handleModal=!0},handleUpdateGood:function(e){this.editTitle=e.pk_material,this.formData=e,this.handleType="update",this.handleModal=!0},handleCTchange:function(e){this.formData.pk_exchangeRate=this.FXRates[e]},handleModalClose:function(e){var t=this;this.$confirm("确认关闭吗？").then(function(a){e(),t.formData={},t.$refs.ruleForm.resetFields()}).catch(function(e){})},callOf:function(){this.handleModal=!1,this.formData={pk_number:0,pk_material:"",pk_itemType:"",pk_supplier:"",pk_price:0,pk_unit:"",pk_currencyType:"",pk_exchangeRate:1},this.$refs.ruleForm.resetFields(),this.getPackage()},handlePostForm:function(e){var t=this;this.$refs[e].validate(function(e){if(!e)return!1;"create"===t.handleType&&k({method:"POST",url:"/api/packadd",data:t.formData}).then(function(e){t.$message({offset:80,message:"保存成功！",type:"success"}),t.handleModal=!1,t.getPackage()}),"update"===t.handleType&&(delete t.formData.pk_amounts,delete t.formData.pk_consumption,k({method:"POST",url:"/api/updatepack",data:s()({},t.formData,{updatePersonnel:b.getCookie("userName")})}).then(function(e){t.$message({offset:80,message:"保存成功！",type:"success"}),t.callOf()}))})},handleDelete:function(e){var t=this;this.$confirm("您是否要删除”"+e.pk_material+"“产品信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){k({method:"POST",url:"/api/deletepack",data:{packid:e.packid}}).then(function(e){t.$message({offset:80,type:"success",message:"操作成功！"}),t.current=1,t.getPackage()})}).catch(function(){t.$message({offset:80,type:"info",message:"已取消删除！"})})}}},se={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"main"},[a("div",{staticClass:"query"},[a("div",{staticClass:"query-wrapper"},[a("div",{staticClass:"query-info"},[a("span",[e._v("品号：")]),e._v(" "),a("el-input",{attrs:{size:"mini",placeholder:"请输入品号查询",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.seach.apply(null,arguments)}},model:{value:e.packNum,callback:function(t){e.packNum=t},expression:"packNum"}})],1),e._v(" "),a("div",{staticClass:"query-info"},[a("span",[e._v("品名：")]),e._v(" "),a("el-input",{attrs:{size:"mini",placeholder:"请输入品名查询",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.seach.apply(null,arguments)}},model:{value:e.packName,callback:function(t){e.packName=t},expression:"packName"}})],1),e._v(" "),a("div",{staticClass:"query-info"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-search",type:"primary",plain:""},on:{click:e.seach}},[e._v("查询")])],1),e._v(" "),"1"===e.power||"2"===e.power?a("div",{staticClass:"query-info",staticStyle:{"text-align":"right"}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.handleAddPack}},[e._v("新增")])],1):e._e()])]),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",staticClass:"mainTable",attrs:{data:e.packTableData,"header-cell-style":{backgroundColor:"rgb(244, 244, 245)"},height:e.tableHeight,stripe:""}},[a("el-table-column",{attrs:{label:"#",type:"index",align:"center",width:"60"}}),e._v(" "),a("el-table-column",{attrs:{prop:"pk_number",label:"品号","min-width":"120"}}),e._v(" "),a("el-table-column",{attrs:{prop:"pk_material",label:"品名"}}),e._v(" "),a("el-table-column",{attrs:{label:"尺寸（英吋）",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return["carton"===t.row.pk_itemType?a("div",[a("span",[e._v(e._s(t.row.pk_length+'" x '+t.row.pk_width+'" x '+t.row.pk_height+'"'))])]):a("div",[a("span",[e._v("/")])])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"pk_itemType",label:"种类",formatter:e.fmtType}}),e._v(" "),a("el-table-column",{attrs:{prop:"pk_supplier",label:"厂商"}}),e._v(" "),a("el-table-column",{attrs:{prop:"pk_price",label:"价格"}}),e._v(" "),a("el-table-column",{attrs:{prop:"pk_unit",label:"单位"}}),e._v(" "),a("el-table-column",{attrs:{prop:"pk_currencyType",label:"币种",formatter:e.fmtCurrencyType}}),e._v(" "),a("el-table-column",{attrs:{prop:"pk_exchangeRate",label:"汇率"}}),e._v(" "),"1"===e.power||"2"===e.power||"8"===e.power?a("el-table-column",{attrs:{label:"操作",align:"center","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdateGood(t.row)}}},[e._v("修改")]),e._v(" "),a("el-button",{attrs:{type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}],null,!1,4031228013)}):e._e()],1),e._v(" "),a("div",{staticClass:"page"},[a("el-pagination",{attrs:{"current-page":e.current,"page-sizes":[20,50,100],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),a("el-dialog",{attrs:{title:"create"===e.handleType?"新增":"修改《"+e.editTitle+"》内容",visible:e.handleModal,"before-close":e.handleModalClose},on:{"update:visible":function(t){e.handleModal=t}}},[a("el-form",{ref:"ruleForm",staticClass:"form-wrap",attrs:{model:e.formData,rules:e.rules,size:"mini"}},["update"===e.handleType?a("el-form-item",{attrs:{prop:"pk_number",label:"品号","label-width":"100px"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.formData.pk_number,callback:function(t){e.$set(e.formData,"pk_number",t)},expression:"formData.pk_number"}})],1):e._e(),e._v(" "),a("el-form-item",{attrs:{prop:"pk_material",label:"品名","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"品名命名规则请尽量遵循：总称：颜色+材质+尺寸+特性(+厂商)",clearable:""},model:{value:e.formData.pk_material,callback:function(t){e.$set(e.formData,"pk_material",t)},expression:"formData.pk_material"}})],1),e._v(" "),a("el-form-item",{attrs:{prop:"pk_itemType",label:"种类","label-width":"100px"}},[a("el-select",{attrs:{placeholder:"请选择种类"},model:{value:e.formData.pk_itemType,callback:function(t){e.$set(e.formData,"pk_itemType",t)},expression:"formData.pk_itemType"}},e._l(e.pk_itemType_options,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),"carton"===e.formData.pk_itemType?[a("el-form-item",{attrs:{prop:"pk_length",label:"长","label-width":"100px"}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请输入（单位：英吋）",min:0,precision:2,clearable:""},model:{value:e.formData.pk_length,callback:function(t){e.$set(e.formData,"pk_length",t)},expression:"formData.pk_length"}})],1),e._v(" "),a("el-form-item",{attrs:{prop:"pk_width",label:"宽","label-width":"100px"}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请输入（单位：英吋）单价",min:0,precision:2,clearable:""},model:{value:e.formData.pk_width,callback:function(t){e.$set(e.formData,"pk_width",t)},expression:"formData.pk_width"}})],1),e._v(" "),a("el-form-item",{attrs:{prop:"pk_height",label:"高","label-width":"100px"}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请输入（单位：英吋）",min:0,precision:2,clearable:""},model:{value:e.formData.pk_height,callback:function(t){e.$set(e.formData,"pk_height",t)},expression:"formData.pk_height"}})],1)]:e._e(),e._v(" "),a("el-form-item",{attrs:{prop:"pk_supplier",label:"厂商","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入厂商",clearable:""},model:{value:e.formData.pk_supplier,callback:function(t){e.$set(e.formData,"pk_supplier",t)},expression:"formData.pk_supplier"}})],1),e._v(" "),a("el-form-item",{attrs:{prop:"pk_price",label:"价格","label-width":"100px"}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:"请输入单价",min:0,precision:5,clearable:""},model:{value:e.formData.pk_price,callback:function(t){e.$set(e.formData,"pk_price",t)},expression:"formData.pk_price"}})],1),e._v(" "),a("el-form-item",{staticStyle:{position:"relative"},attrs:{prop:"pk_unit",label:"单位","label-width":"100px"}},[a("el-select",{attrs:{placeholder:"注意成品用量变化，并通知主品配置人员",filterable:"","allow-create":"","default-first-optionclearable":""},model:{value:e.formData.pk_unit,callback:function(t){e.$set(e.formData,"pk_unit",t)},expression:"formData.pk_unit"}},e._l(e.unit_options,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),"update"===e.handleType?a("p",{staticStyle:{padding:"0",margin:"0","font-size":"12px",color:"#F56C6C",position:"absolute",top:"64%",left:"148px"}},[e._v("注意成品用量变化，并通知主品配置人员")]):e._e(),e._v(" "),a("el-form-item",{attrs:{prop:"pk_currencyType",label:"币种","label-width":"100px"}},[a("el-select",{attrs:{placeholder:"请选择币种"},on:{change:e.handleCTchange},model:{value:e.formData.pk_currencyType,callback:function(t){e.$set(e.formData,"pk_currencyType",t)},expression:"formData.pk_currencyType"}},e._l(e.ct_options,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{prop:"pk_exchangeRate",label:"汇率","label-width":"100px"}},[a("el-input-number",{attrs:{"controls-position":"right",min:0,clearable:""},model:{value:e.formData.pk_exchangeRate,callback:function(t){e.$set(e.formData,"pk_exchangeRate",t)},expression:"formData.pk_exchangeRate"}})],1)],2),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:e.callOf}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.handlePostForm("ruleForm")}}},[e._v("保 存")])],1)],1)],1)},staticRenderFns:[]};var ce=a("VU/8")(ne,se,!1,function(e){a("ZIVT")},"data-v-08bda11a",null).exports,pe={name:"Login",data:function(){return{formInline:{userNName:"",password:""},rules:{userName:[{required:!0,message:"用户名不能为空",trigger:"blur"}],password:[{required:!0,message:"密码不能为空",trigger:"blur"}]}}},methods:{submitForm:function(e){var t=this;this.$refs[e].validate(function(e){if(!e)return!1;t.loginRequest(t.formInline.userName,t.formInline.password)})},loginRequest:function(e,t){var a=this,r=new FormData;r.append("userName",e),r.append("password",t),r.append("last_login_tiem",v(Date.now(),"YYYY-MM-DD HH:mm:ss")),k({method:"POST",url:"/api/userdo",data:r}).then(function(e){e.data&&200!==e.data.code?a.$message({type:"error",message:e.data.message}):(b.setCookie("userName",e.userName),b.setCookie("userid",e.userid),b.setCookie("power",e.power),a.$router.replace({path:"/"}),a.$message({type:"success",message:"登录成功！"}))}).catch(function(e){console.log("error",e)})}}},ue={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"login-page"},[a("div",{staticClass:"mask aside-login"},[a("div",{staticClass:"formLogin"},[a("p",{staticClass:"welcome"},[e._v("知勉工业股份有限公司")]),e._v(" "),a("p",{staticClass:"title",staticStyle:{"font-size":"38px"}},[e._v("成 本 核 算 系 统")]),e._v(" "),a("p",{staticClass:"title"},[e._v("欢迎登陆")]),e._v(" "),a("el-form",{ref:"login",staticClass:"demo-ruleForm",attrs:{model:e.formInline,"status-icon":"",rules:e.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{prop:"userName"}},[a("el-input",{attrs:{"prefix-icon":"el-icon-user",placeholder:"请输入您的账号"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.submitForm("login")}},model:{value:e.formInline.userName,callback:function(t){e.$set(e.formInline,"userName",t)},expression:"formInline.userName"}})],1),e._v(" "),a("el-form-item",{attrs:{prop:"password"}},[a("el-input",{attrs:{"prefix-icon":"el-icon-lock",placeholder:"请输入您的密码",type:"password","auto-complete":"off"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.submitForm("login")}},model:{value:e.formInline.password,callback:function(t){e.$set(e.formInline,"password",t)},expression:"formInline.password"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("login")}}},[e._v("登录")])],1)],1)],1)])])},staticRenderFns:[]};var me=a("VU/8")(pe,ue,!1,function(e){a("V4b/")},"data-v-03260cae",null).exports;r.default.use(D.a);var de={FXRates:{},ct_options:[{value:"CNY",label:"人民币(CNY)"},{value:"USD",label:"美元(USD)"},{value:"USDP",label:"美元(利润)"},{value:"EURP",label:"欧元(利润)"},{value:"AUDP",label:"澳币(利润)"},{value:"CADP",label:"加币(利润)"},{value:"GBPP",label:"英镑(利润)"},{value:"100JPYP",label:"一百日元(利润)"}],CURRENCY_DIC:{CNY:"人民币(CNY)",USD:"美元(USD)",USDP:"美元(利润)",EURP:"欧元(利润)",AUDP:"澳币(利润)",CADP:"加币(利润)",GBPP:"英镑(利润)","100JPYP":"一百日元(利润)"},standard_options:[{value:"niosh",label:"美规"},{value:"eu",label:"欧规"},{value:"la",label:"国标"},{value:"ds",label:"日规"},{value:"others",label:"其它"}],type_options:[{value:"NONE",label:"无"},{value:"V",label:"气阀"},{value:"OV",label:"活性碳"},{value:"VOV",label:"气阀+活性碳"}],TYPE_DIC:{NONE:"无",V:"气阀",OV:"活性碳",VOV:"气阀+活性碳"},model_options:[{value:"1",label:"一片式"},{value:"2",label:"折叠式"},{value:"3",label:"杯状式"}],MODEL_DIC:{1:"一片式",2:"折叠式",3:"杯状式"},itemType_options:[{value:"out",label:"外层"},{value:"mid",label:"中层"},{value:"in",label:"内层"},{value:"nasal",label:"鼻夹"},{value:"binding",label:"松紧带"},{value:"foam",label:"鼻垫"},{value:"others",label:"其它"}],ITEM_DIC:{out:"外层",mid:"中层",in:"内层",nasal:"鼻夹",binding:"松紧带",foam:"鼻垫",others:"其它"},power_options:[{value:1,label:"开发者"},{value:2,label:"物料维护员"},{value:6,label:"工角维护员"},{value:7,label:"业务员"},{value:8,label:"管理员"}],company_options:["C1台北","C2知腾","C4上海","C5迅安"],POWER_DIC:{1:"开发者",2:"物料维护员",6:"工角维护员",7:"业务员",8:"管理员"},unit_options:[{value:"KG",lable:"KG"},{value:"㎡",lable:"㎡"},{value:"PCS",lable:"PCS"},{value:"件",lable:"件"},{value:"卷",lable:"卷"}],pk_itemType_options:[{value:"plastic",label:"胶袋"},{value:"colorbox",label:"彩盒"},{value:"card",label:"彩/吊卡"},{value:"certificate",label:"合格证"},{value:"instructions",label:"说明书"},{value:"listershell",label:"泡壳"},{value:"tape",label:"封箱胶带"},{value:"carton",label:"外箱"},{value:"innerbox",label:"内箱"},{value:"paperboard",label:"纸盖/纸板"},{value:"paperpallet",label:"纸栈板"},{value:"coil",label:"卷料"},{value:"paperlining",label:"白纸衬"},{value:"seal",label:"绑封口"},{value:"labeling",label:"贴标"},{value:"others",label:"其它"}],PK_ITEM_DIC:{paperlining:"白纸衬",seal:"绑封口",tape:"封箱胶带",labeling:"贴标",instructions:"说明书",card:"彩/吊卡",plastic:"胶袋",coil:"卷料",colorbox:"彩盒",carton:"外箱",innerbox:"内箱",paperboard:"纸盖/纸板",paperpallet:"纸栈板",listershell:"泡壳",certificate:"合格证",others:"其它"},historiesList:[{content:["复制功能优化人工工角选项功能；","工角配置页面增加生产工厂条件查询；","其它细节优化。"],timestamp:"2025-07-21",color:"#0bbd87",size:"large"},{content:["报价单运费计算、对比功能价格计算、报价单剔除功能计算公式优化；","优化基础模版数据与主品数据展现形式；","增加多币种利润汇率。"],timestamp:"2025-07-04"},{content:["主品页面增加基础模版置顶功能；","主品页面优化多条件查询；","增加默认分页显示条数。"],timestamp:"2025-06-09"},{content:["报价单新增运费剔除管销功能；","增加业务员删除权限；","报价单增加日元汇率；","已知bug修复及代码优化。"],timestamp:"2025-05-20"},{content:["支持利润手输入精确到小数十分位；","对比功能增加包装信息；","新增品名输入命名规范提示。"],timestamp:"2025-03-10"},{content:["系列配置增加物料种类选择功能；","对比列表名称增加工厂、销售类别显示；","规范物料种类名称。"],timestamp:"2025-01-15"},{content:["主品新增完成人工工角校验功能；","复制功能优化：复制工角下拉远程查询；","运输配置区分内外销计算，并支持运费修改。"],timestamp:"2025-01-02"},{content:["人工工角按生产工厂区分；","复制功能优化：剥离包装工角和人工工角；","调整开发者和管理员权限。"],timestamp:"2024-12-19"},{content:["修复主品新增步骤bug；","新增美元（利润）币种字典；","物料和包装配置优化单位选择功能；","主品页面增加查询条件；","列表后台排序优化。"],timestamp:"2024-12-6"},{content:["修复彩盒包装散货数量计算异常的bug；","报价单和对比功能美元采用手动汇率(6.9)，与物料汇率区分使用；","增加物料和包装单位修改提示；增加主品名称命名提示","新增按物料品号查询主品信息功能；","修复繁简体模糊搜索功能。"],timestamp:"2024-11-22"},{content:["新增或复制中，包装材料单价可以修改；","完成包装材料单价变更提醒功能。"],timestamp:"2024-10-25"},{content:["修复成本计算公式bug；","完善修改时间刷新功能；","新增查看历史版本记录功能。"],timestamp:"2024-09-18"},{content:["物料和包装单项新增剔除管销功能；","修复运费获取失败的bug；","主品列表增加修改时间。"],timestamp:"2024-09-10"},{content:["材料配置增加未填写提示；","复制功能优化：选择是否包含包装数据；","修改主品弹窗bug修复；","成本计算公式bug修复。"],timestamp:"2024-08-27"},{content:["运输配置功能细节优化；","导出格式异常修复；","增加货柜容量提醒；","报价单内外销默认币种切换显示。"],timestamp:"2024-08-12"},{content:["新增主品物料配置新增单选及重置功能；","新增包装常用材料用量自动计算功能；","运输配置贸易条件整合，去除货柜尺寸选择。"],timestamp:"2024-07-31"}]},he={FX_RATES:function(e,t){e.FXRates=s()({CNY:"1.00"},t)}},_e={getFXRates:function(e){var t=e.commit;return k({method:"POST",url:"/api/selectMoney"}).then(function(e){var a={};e.forEach(function(e){a[e.currency]=Number(e.exchangerate).toFixed(2)}),t("FX_RATES",a),console.log("state.FXRates",de.FXRates)})}},fe=new D.a.Store({state:de,mutations:he,actions:_e}),be=new i.a({routes:[{name:"login",path:"/login",component:me},{name:"index",path:"/",component:F,children:[{name:"product",path:"/product",component:B,beforeEnter:function(e,t,a){if("1"!==b.getCookie("power")&&"7"!==b.getCookie("power")&&"8"!==b.getCookie("power"))return!1;a()}},{name:"succession",path:"/succession",component:U,beforeEnter:function(e,t,a){if("1"!==b.getCookie("power")&&"8"!==b.getCookie("power"))return!1;a()}},{name:"material",path:"/material",component:W,beforeEnter:function(e,t,a){if("1"!==b.getCookie("power")&&"2"!==b.getCookie("power")&&"7"!==b.getCookie("power")&&"8"!==b.getCookie("power"))return!1;a()}},{name:"laborCost",path:"/laborCost",component:J,beforeEnter:function(e,t,a){if("1"!==b.getCookie("power")&&"6"!==b.getCookie("power")&&"8"!==b.getCookie("power"))return!1;a()}},{name:"package",path:"/package",component:ce,beforeEnter:function(e,t,a){if("1"!==b.getCookie("power")&&"2"!==b.getCookie("power")&&"7"!==b.getCookie("power")&&"8"!==b.getCookie("power"))return!1;a()}},{name:"freight",path:"/freight",component:ee,beforeEnter:function(e,t,a){if("1"!==b.getCookie("power")&&"3"!==b.getCookie("power"))return!1;a()}},{name:"user",path:"/user",component:re,beforeEnter:function(e,t,a){if("1"!==b.getCookie("power"))return!1;a()}},{name:"rates",path:"/rates",component:ie,beforeEnter:function(e,t,a){if("1"!==b.getCookie("power")&&"5"!==b.getCookie("power")&&"7"!==b.getCookie("power")&&"8"!==b.getCookie("power"))return!1;a()}}]}]});fe.dispatch("getFXRates"),be.beforeEach(function(e,t,a){"/login"===e.path||b.getCookie("userName")?"/login"===e.path&&b.getCookie("userName")?a({path:"/"}):a():a({path:"login"})});var ge=be;a("tvR6");r.default.config.productionTip=!1,r.default.use(i.a),r.default.use(h.a),new r.default({el:"#app",components:{App:l},router:ge,store:fe,template:"<App/>"}),r.default.directive("removeAriaHidden",{bind:function(e,t){e.querySelectorAll(".el-radio__original").forEach(function(e){e.removeAttribute("aria-hidden")})}})},NhfS:function(e,t){},"V4b/":function(e,t){},WPuk:function(e,t){},YAVv:function(e,t){},YVpH:function(e,t){},ZIVT:function(e,t){},jpBW:function(e,t){},siHd:function(e,t){},tvR6:function(e,t){},ucxN:function(e,t){},"x+pM":function(e,t){},yOmT:function(e,t){}},["NHnr"]);
//# sourceMappingURL=app.3bb77470200e0ca442c1.js.map