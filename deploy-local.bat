@echo off
echo ========================================
echo    Makrite 成本核算系统 - 本地部署脚本
echo ========================================

echo.
echo [1/4] 检查环境...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 Node.js，请先安装 Node.js
    pause
    exit /b 1
)

where java >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 Java，请先安装 JDK 1.8+
    pause
    exit /b 1
)

echo Node.js 和 Java 环境检查通过

echo.
echo [2/4] 构建前端项目...
cd mak-costa-web-dev-230308
if not exist node_modules (
    echo 安装前端依赖...
    npm install
    if %errorlevel% neq 0 (
        echo 错误: npm install 失败
        pause
        exit /b 1
    )
)

echo 构建前端项目...
npm run build
if %errorlevel% neq 0 (
    echo 错误: 前端构建失败
    pause
    exit /b 1
)

echo.
echo [3/4] 集成前端到后端...
cd ..
if exist cost-accounting-system-master\src\main\resources\static (
    rmdir /s /q cost-accounting-system-master\src\main\resources\static
)
mkdir cost-accounting-system-master\src\main\resources\static
xcopy mak-costa-web-dev-230308\dist\* cost-accounting-system-master\src\main\resources\static\ /e /y

echo.
echo [4/4] 启动后端服务...
cd cost-accounting-system-master
echo 正在启动服务器，请稍候...
echo 服务启动后可通过以下地址访问：
echo   - 系统首页: http://localhost:7000
echo   - API接口: http://localhost:7000/api
echo.
echo 按 Ctrl+C 可停止服务
echo ========================================

java -jar target\yw-0.0.1-SNAPSHOT.jar 2>nul || (
    echo 未找到jar包，正在编译...
    call mvnw.cmd clean package -DskipTests
    if %errorlevel% neq 0 (
        echo 错误: Maven 构建失败
        pause
        exit /b 1
    )
    java -jar target\yw-0.0.1-SNAPSHOT.jar
)
