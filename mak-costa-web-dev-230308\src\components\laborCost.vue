<template>
  <div class="main">
    <div class="query">
      <div class="query-wrapper">
        <div class="query-info">
          <span>工角名称：</span>
          <el-input
            v-model="laborName"
            size="mini"
            placeholder="请输入工角名称查询"
            @keyup.enter.native="seach"
            clearable>
          </el-input>
        </div>
        <div class="query-info" style="line-height: 32px;">
          <span>加工工厂：</span>
          <el-select v-model="factory" placeholder="可选择工厂查询" size="mini" @change="seach" clearable>
            <el-option value="知腾"></el-option>
            <el-option value="迅安"></el-option>
          </el-select>
        </div>
        <div style="text-align:left; display: inline;">
          <el-button size="mini" @click="seach" icon="el-icon-search" type="primary" plain>查询</el-button>
        </div>
        <div class="query-info" style="text-align:right;">
          <el-button type="primary" size="mini" icon="el-icon-plus" @click="handleAddLabor">新增</el-button>
        </div>
      </div>
    </div>
    <el-table
      v-loading="loading"
      ref="table"
      class="mainTable"
      :data="laborTableData"
      :header-cell-style='{"backgroundColor": "rgb(244, 244, 245)"}'
      :height="tableHeight"
      stripe
    >
      <el-table-column
        label="#"
        type="index"
        align="center"
        width="60"
      />
      <el-table-column
        prop="lc_name"
        label="工角名称"
      />
      <el-table-column
        prop="lc_factory"
        label="所属工厂"
      />
      <el-table-column
        prop="lc_price"
        label="单价(RMB)"
      />
      <!-- <el-table-column
        prop="lc_unit"
        label="单位"
      /> -->
      <!-- <el-table-column
        prop="lc_consumption"
        label="用量"
      /> -->
      <!-- <el-table-column
        prop="lc_currencyType"
        label="币种"
        :formatter="fmtCurrencyType"
      /> -->
      <!-- <el-table-column
        prop="lc_exchangeRate"
        label="汇率"
      /> -->
      <!-- <el-table-column
        prop="lc_amounts"
        label="金额"
      /> -->
      <el-table-column label="操作" align="center" min-width="130">
        <template slot-scope="scope">
          <el-button type="text" icon="el-icon-edit" @click="handleUpdateLabor(scope.row)">修改</el-button>
          <el-button type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="page">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="current"
        :page-sizes="[20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
    <!-- 新增/修改 -->
    <el-dialog :title="handleType==='create'?'新增':`修改《${editTitle}》内容`" :visible.sync="handleModal" :before-close="handleModalClose">
      <el-form :model="formData" class="form-wrap" :rules="rules" ref="ruleForm" size="mini">
        <el-form-item prop="lc_name" label="工角名称：" label-width="100px">
          <el-input v-model="formData.lc_name" placeholder="请输入工角对应的型号，如：9500-N95" clearable>
          </el-input>
        </el-form-item>
        <el-form-item prop="lc_factory" label="所属工厂：" label-width="100px">
          <el-radio-group v-model="formData.lc_factory">
              <el-radio label="迅安"></el-radio>
              <el-radio label="知腾"></el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item prop="lc_price" label="工角单价：" label-width="100px">
          <el-input-number v-model="formData.lc_price" controls-position="right" placeholder="请输入单价" :min="0" clearable>
          </el-input-number>
        </el-form-item>
        <!-- <el-form-item prop="lc_model" label="型号" label-width="60px">
          <el-checkbox-group v-model="formData.lc_model">
            <el-checkbox v-for="(op, i) in model_options" :key="i" :label="op.label"></el-checkbox>
          </el-checkbox-group>
        </el-form-item> -->
        <!-- <el-form-item prop="lc_unit" label="单位" label-width="60px">
          <el-input v-model="formData.lc_unit" placeholder="请输入单位" clearable>
          </el-input>
        </el-form-item> -->
        <!-- <el-form-item prop="lc_consumption" label="用量" label-width="60px">
          <el-input-number v-model="formData.lc_consumption" controls-position="right" placeholder="请输入用量" :min="0" clearable>
          </el-input-number>
        </el-form-item> -->
        <!-- <el-form-item prop="lc_currencyType" label="币种" label-width="60px">
          <el-select v-model="formData.lc_currencyType" @change="handleCTchange" placeholder="请选择币种">
            <el-option
              v-for="item in ct_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item> -->
        <!-- <el-form-item prop="lc_exchangeRate" label="汇率" label-width="60px">
          <el-input-number v-model="formData.lc_exchangeRate" controls-position="right" placeholder="请输入汇率" :min="0" clearable>
          </el-input-number>
        </el-form-item> -->
        <!-- <el-form-item prop="lc_amounts" label="单价" label-width="60px">
          <el-button type="text" icon="el-icon-search" @click="calculateLC" />
          <span>{{formData.lc_amounts}}</span>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="callOf('ruleForm')">取 消</el-button>
        <el-button type="primary" size="mini" @click="handlePostForm('ruleForm')">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import ajax from '../common/axiosHttp'
export default {
  name: 'LaborCost',
  data () {
    return {
      loading: false,
      tableHeight: 0,
      laborTableData: [],
      current: 1,
      pageSize: 20,
      laborName: '',
      factory: '',
      total: 0,
      handleType: '',
      handleModal: false,
      formData: {
        lc_name: '',
        lc_price: 0
        // lc_unit: '',
        // lc_currencyType: 'CNY',
        // lc_consumption: 0,
        // lc_exchangeRate: 1
        // lc_amounts: 0
        // lc_model: [],
        // lc_succession: []
      },
      editTitle: '',
      rules: {
        lc_name: [
          {
            required: true,
            message: '请填写名称',
            trigger: 'blur'
          }
        ],
        lc_factory: [
          {
            required: true,
            message: '请选择',
            trigger: 'blur'
          }
        ],
        lc_price: [
          {
            required: true,
            type: 'number',
            message: '请填写单价',
            trigger: 'blur'
          }
        ]
        // lc_model: [
        //   {
        //     required: true,
        //     message: '请填写型号',
        //     trigger: 'blur'
        //   }
        // ],
        // lc_succession: [
        //   {
        //     required: true,
        //     message: '请填写系列',
        //     trigger: 'blur'
        //   }
        // ],
        // lc_unit: [
        //   {
        //     required: true,
        //     message: '请填写单位',
        //     trigger: 'blur'
        //   }
        // ],
        // lc_exchangeRate: [
        //   {
        //     required: true,
        //     type: 'number',
        //     message: '请填写汇率',
        //     trigger: 'blur'
        //   }
        // ],
        // lc_consumption: [
        //   {
        //     required: true,
        //     type: 'number',
        //     message: '请填写用量',
        //     trigger: 'blur'
        //   }
        // ],
        // lc_currencyType: [
        //   {
        //     required: true,
        //     message: '请选择币种',
        //     trigger: 'change'
        //   }
        // ]
        // lc_amounts: [
        //   {
        //     required: true,
        //     message: '请填写单价',
        //     trigger: 'blur'
        //   }
        // ]
      }
    }
  },
  computed: {
    ...mapState(['FXRates', 'CURRENCY_DIC', 'ct_options', 'model_options'])
  },
  mounted () {
    // 挂载window.onresize事件(动态设置table高度)
    this.$nextTick(() => {
      this.tableHeight = window.innerHeight - 136
    })
    let _this = this
    window.onresize = () => {
      if (_this.resizeFlag) {
        clearTimeout(_this.resizeFlag)
      }
      _this.resizeFlag = setTimeout(() => {
        _this.getTableHeight()
        _this.resizeFlag = null
      }, 100)
    }
  },
  activated () {
    this.getLabors()
  },
  methods: {
    // 格式化币种
    fmtCurrencyType (row, column, cellValue) {
      return this.CURRENCY_DIC[cellValue]
    },
    // 计算table高度(动态设置table高度)
    getTableHeight () {
      let tableH = 180 // 距离页面下方的高度
      let tableHeightDetil = window.innerHeight - tableH
      if (tableHeightDetil <= 300) {
        this.tableHeight = 300
      } else {
        this.tableHeight = window.innerHeight - tableH
      }
    },
    // 选择币种
    handleCTchange (val) {
      // 自动带出汇率值
      // this.formData.lc_exchangeRate = this.FXRates[val]
    },
    // 搜索
    seach () {
      this.current = 1
      this.pageSize = 20
      this.getLabors()
    },
    // 获取货品列表
    getLabors () {
      this.loading = true
      let form = new FormData()
      form.append('current', this.current)
      form.append('size', this.pageSize)
      form.append('lc_name', this.laborName)
      form.append('lc_factory', this.factory)
      ajax({
        method: 'POST',
        url: '/api/LaborCostAll',
        data: form
      }).then(res => {
        this.loading = false
        this.total = res.total
        this.laborTableData = res.records
      }).catch(() => {
        this.loading = false
      })
    },
    // 页码分页
    handleSizeChange (val) {
      this.pageSize = val
      this.getLabors()
    },
    // 分页
    handleCurrentChange (val) {
      this.current = val
      this.getLabors()
    },
    // 新增
    handleAddLabor () {
      this.formData = {
        // lc_model: [],
        // lc_succession: [],
        lc_currencyType: 'CNY'
      }
      // this.handleCTchange('CNY')
      this.handleType = 'create'
      this.handleModal = true
    },
    // 修改
    handleUpdateLabor (row) {
      this.editTitle = row.lc_name
      this.formData = row
      this.handleType = 'update'
      this.handleModal = true
    },
    // 关闭提醒
    handleModalClose (done) {
      this.$confirm('确认关闭吗？').then(_ => {
        done()
        this.formData = {}
        this.$refs['ruleForm'].resetFields()
      })
        .catch(_ => {})
    },
    // 关闭模态框
    callOf (formName) {
      this.handleModal = false
      this.formData = {}
      this.$refs[formName].resetFields()
      this.getLabors()
    },
    // 计算金额
    calculateLC () {
      if (!this.formData.lc_price === '') return
      if (!this.formData.lc_exchangeRate === '') return
      if (this.formData.lc_consumption === '') return
      let AMOUNTS = 0
      if (this.formData.lc_consumption === 0) {
        AMOUNTS = this.formData.lc_price * this.formData.lc_exchangeRate
      } else {
        AMOUNTS = this.formData.lc_price * this.formData.lc_exchangeRate / this.formData.lc_consumption
      }
      this.formData.lc_amounts = AMOUNTS.toFixed(3)
    },
    // 提交保存
    handlePostForm (formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          // this.calculateLC()
          this.formData.lc_currencyType = 'CNY'
          this.formData.lc_exchangeRate = 1
          this.formData.lc_amounts = this.formData.lc_price * this.formData.lc_exchangeRate
          if (this.handleType === 'create') {
            ajax({
              method: 'POST',
              url: '/api/LaborCostadd',
              data: this.formData
            }).then(res => {
              this.$message({
                offset: 80,
                message: '保存成功！',
                type: 'success'
              })
              this.handleModal = false
              this.getLabors()
            })
          }
          if (this.handleType === 'update') {
            ajax({
              method: 'POST',
              url: '/api/updateLaborCost',
              data: this.formData
            }).then(res => {
              this.$message({
                offset: 80,
                message: '保存成功！',
                type: 'success'
              })
              this.handleModal = false
              this.getLabors()
            })
          }
        } else {
          return false
        }
      })
    },
    // 删除
    handleDelete (row) {
      this.$confirm(`您是否要删除”${row.lc_name}“信息?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ajax({
          method: 'POST',
          url: '/api/deleteLaborCost',
          data: {
            laborCostid: row.laborCostid
          }
        }).then(res => {
          this.$message({
            offset: 80,
            type: 'success',
            message: '操作成功！'
          })
          this.current = 1
          this.getLabors()
        })
      }).catch(() => {
        this.$message({
          offset: 80,
          type: 'info',
          message: '已取消删除！'
        })
      })
    }
  }
}
</script>
<style lang="less" scoped>
.main {
  height:100vh;
  overflow:hidden;
  /deep/ .el-select, .el-input, .el-input-number, .el-input_inner {
    width: 100%;
  }
  /deep/ .el-input__inner {
    text-align: left;
  }
  /deep/ .el-form-item--mini .el-form-item__content, .el-form-item--mini .el-form-item__label {
    text-align: left;
  }
  .query {
    // margin-bottom: 20px;
    &-wrapper {
      display: inline-block;
      width: 100%;
      margin: 0 0 16px 0;
      text-align: left;
      button {
        margin: 5px;
      }
    }
    &-info {
      display: inline-block;
      width: 30%;
      vertical-align: bottom;
      >span {
        font-size: 13px;
        float: left;
        width: 80px;
        line-height: 32px;
      }
      div {
        float: left;
        width: 70%;
      }
    }
  }
  .page {
    margin: 20px 0;
    text-align: center;
  }
  .topBtn {
    float: left;
    margin: 0 0 24px 0;
  }
  .addBtn {
    float: right;
    margin: 0 24px 32px;
  }
}
</style>
