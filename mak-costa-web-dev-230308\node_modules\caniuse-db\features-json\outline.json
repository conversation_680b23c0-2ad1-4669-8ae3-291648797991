{"title": "CSS outline properties", "description": "The CSS outline properties draw a border around an element that does not affect layout, making it ideal for highlighting. This covers the `outline` shorthand, as well as `outline-width`, `outline-style`, `outline-color` and `outline-offset`. ", "spec": "https://drafts.csswg.org/css-ui/", "status": "cr", "links": [{"url": "https://drafts.csswg.org/css-ui-3/#outline", "title": "CSS Basic User Interface Module Level 3"}, {"url": "https://developer.mozilla.org/en-US/docs/CSS/outline", "title": "MDN Web Docs - CSS outline"}], "bugs": [], "categories": ["CSS3"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "a #2", "9": "a #1 #2", "10": "a #1 #2", "11": "a #1 #2"}, "edge": {"12": "a #1 #2", "13": "a #1 #2", "14": "a #1 #2", "15": "y", "16": "y", "17": "y", "18": "y", "79": "y", "80": "y", "81": "y", "83": "y", "84": "y", "85": "y", "86": "y", "87": "y", "88": "y", "89": "y", "90": "y", "91": "y", "92": "y", "93": "y", "94": "y", "95": "y", "96": "y", "97": "y", "98": "y", "99": "y", "100": "y", "101": "y", "102": "y", "103": "y", "104": "y", "105": "y"}, "firefox": {"2": "y", "3": "y", "3.5": "y", "3.6": "y", "4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y", "70": "y", "71": "y", "72": "y", "73": "y", "74": "y", "75": "y", "76": "y", "77": "y", "78": "y", "79": "y", "80": "y", "81": "y", "82": "y", "83": "y", "84": "y", "85": "y", "86": "y", "87": "y", "88": "y", "89": "y", "90": "y", "91": "y", "92": "y", "93": "y", "94": "y", "95": "y", "96": "y", "97": "y", "98": "y", "99": "y", "100": "y", "101": "y", "102": "y", "103": "y", "104": "y", "105": "y", "106": "y"}, "chrome": {"4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y", "70": "y", "71": "y", "72": "y", "73": "y", "74": "y", "75": "y", "76": "y", "77": "y", "78": "y", "79": "y", "80": "y", "81": "y", "83": "y", "84": "y", "85": "y", "86": "y", "87": "y", "88": "y", "89": "y", "90": "y", "91": "y", "92": "y", "93": "y", "94": "y", "95": "y", "96": "y", "97": "y", "98": "y", "99": "y", "100": "y", "101": "y", "102": "y", "103": "y", "104": "y", "105": "y", "106": "y", "107": "y", "108": "y"}, "safari": {"3.1": "y", "3.2": "y", "4": "y", "5": "y", "5.1": "y", "6": "y", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "12": "y", "12.1": "y", "13": "y", "13.1": "y", "14": "y", "14.1": "y", "15": "y", "15.1": "y", "15.2-15.3": "y", "15.4": "y", "15.5": "y", "15.6": "y", "16.0": "y", "TP": "y"}, "opera": {"9": "a #2", "9.5-9.6": "a #2", "10.0-10.1": "a #2", "10.5": "a #2", "10.6": "a #2", "11": "a #2", "11.1": "a #2", "11.5": "a #2", "11.6": "y", "12": "y", "12.1": "y #1", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "60": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y", "70": "y", "71": "y", "72": "y", "73": "y", "74": "y", "75": "y", "76": "y", "77": "y", "78": "y", "79": "y", "80": "y", "81": "y", "82": "y", "83": "y", "84": "y", "85": "y", "86": "y", "87": "y", "88": "y", "89": "y", "90": "y"}, "ios_saf": {"3.2": "y", "4.0-4.1": "y", "4.2-4.3": "y", "5.0-5.1": "y", "6.0-6.1": "y", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11.0-11.2": "y", "11.3-11.4": "y", "12.0-12.1": "y", "12.2-12.5": "y", "13.0-13.1": "y", "13.2": "y", "13.3": "y", "13.4-13.7": "y", "14.0-14.4": "y", "14.5-14.8": "y", "15.0-15.1": "y", "15.2-15.3": "y", "15.4": "y", "15.5": "y", "15.6": "y", "16.0": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "y", "2.2": "y", "2.3": "y", "3": "y", "4": "y", "4.1": "y", "4.2-4.3": "y", "4.4": "y", "4.4.3-4.4.4": "y", "105": "y"}, "bb": {"7": "y", "10": "y"}, "op_mob": {"10": "a #2", "11": "a #2", "11.1": "a #2", "11.5": "a #2", "12": "y", "12.1": "y", "64": "y"}, "and_chr": {"105": "y"}, "and_ff": {"104": "y"}, "ie_mob": {"10": "a #1 #2", "11": "a #1 #2"}, "and_uc": {"12.12": "y"}, "samsung": {"4": "y", "5.0-5.4": "y", "6.2-6.4": "y", "7.2-7.4": "y", "8.2": "y", "9.2": "y", "10.1": "y", "11.1-11.2": "y", "12.0": "y", "13.0": "y", "14.0": "y", "15.0": "y", "16.0": "y", "17.0": "y", "18.0": "y"}, "and_qq": {"10.4": "y"}, "baidu": {"7.12": "y"}, "kaios": {"2.5": "y"}}, "notes": "Firefox also supports the non-standard `-moz-outline-radius` property that acts similar to `border-radius`.", "notes_by_num": {"1": "Also supports the value of `invert` for `outline-color`. (support of this value is optional for browsers)", "2": "Does not support `outline-offset`."}, "usage_perc_y": 98.14, "usage_perc_a": 0.68, "ucprefix": false, "parent": "", "keywords": "-moz-outline,outline-width,outline-style,outline-color,outline-radius", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}