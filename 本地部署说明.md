# Makrite 成本核算系统 - 本地部署指南

## 环境要求

- **Node.js**: 6.0.0+ (推荐 14.x 或更高版本)
- **Java**: JDK 1.8+
- **MySQL**: 5.7+ 或 8.0+

## 快速部署

### 1. 数据库初始化
```bash
# Windows
init-database.bat

# Linux/Mac
chmod +x init-database.sh && ./init-database.sh
```

### 2. 完整部署（首次运行）
```bash
# Windows
deploy-local.bat

# Linux/Mac  
chmod +x deploy-local.sh && ./deploy-local.sh
```

### 3. 快速启动（后续运行）
```bash
# Windows
start.bat

# Linux/Mac
chmod +x start.sh && ./start.sh
```

## 访问地址

- **系统首页**: http://localhost:7000
- **API接口**: http://localhost:7000/api

## 项目结构

```
cost/
├── cost-accounting-system-master/    # 后端项目 (Spring Boot)
├── mak-costa-web-dev-230308/        # 前端项目 (Vue.js)
├── deploy-local.bat                 # Windows 部署脚本
├── deploy-local.sh                  # Linux/Mac 部署脚本
├── start.bat                        # Windows 快速启动
├── init-database.bat               # 数据库初始化脚本
└── 本地部署说明.md                  # 本文档
```

## 部署流程说明

1. **前端构建**: 执行 `npm run build` 生成 dist 目录
2. **文件集成**: 将 dist 内容复制到后端 static 目录
3. **后端编译**: 使用 Maven 编译生成 jar 包
4. **服务启动**: 运行 jar 包启动内嵌 Tomcat 服务器

## 常见问题

### Q: 端口被占用怎么办？
A: 修改 `cost-accounting-system-master/src/main/resources/application.yml` 中的端口号

### Q: 数据库连接失败？
A: 检查 MySQL 服务是否启动，确认用户名密码正确

### Q: 前端构建失败？
A: 删除 `node_modules` 目录，重新执行 `npm install`

### Q: 如何停止服务？
A: 在命令行窗口按 `Ctrl+C`

## 开发模式

如需前后端分离开发：

1. **启动后端**:
   ```bash
   cd cost-accounting-system-master
   ./mvnw spring-boot:run
   ```

2. **启动前端**:
   ```bash
   cd mak-costa-web-dev-230308
   npm run dev
   ```

前端开发地址: http://localhost:8080
后端API地址: http://localhost:7000
