package com.zt.yw.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor //有参构造器
@NoArgsConstructor  //无参构造器
@TableName("lc_table")
public class Lctable implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "lc_id", type = IdType.AUTO)//如数据库为自增id添加该行可解决数据库自增序号突然变大的问题
    private  Integer lc_id;
    @TableField("uuid")
    private  String uuid;
    @TableField("lc_name")
    private String lc_name;
    @TableField("lc_price")
    private double lc_price;
    @TableField("lc_unit")
    private String lc_unit;
    @TableField("lc_consumption")
    private double lc_consumption;
    @TableField("lc_exchangeRate")
    private double lc_exchangeRate;
    @TableField("lc_amounts")
    private double lc_amounts;
    @TableField("lc_currencyType")
    private String lc_currencyType;
    @TableField(exist = false)
    private String updatePersonnel;
    @TableField("lc_factory")
    private String lc_factory; //工角所属工厂
}
