package com.zt.yw.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor //有参构造器
@NoArgsConstructor  //无参构造器
@TableName("rm_table")//原材料表
public class RmTable implements Serializable {
  private static final long serialVersionUID = 1L;
  @TableId(value = "rm_id", type = IdType.AUTO)
  private Integer rm_id;
  @TableField("uuid")
  private String uuid;
  @TableField("rm_number")
  private String rm_number;
  @TableField("rm_material")
  private String rm_material;
  @TableField("rm_price")
  private double rm_price;
  @TableField("rm_unit")
  private String rm_unit;
  @TableField("rm_consumption")
  private double rm_consumption;
  @TableField("rm_currencyType")
  private String rm_currencyType;
  @TableField("rm_exchangeRate")
  private double rm_exchangeRate;
  @TableField("rm_loss")
  private double rm_loss;
  @TableField("rm_prop")
  private String rm_prop;
  @TableField("rm_amounts")
  private double rm_amounts;
  @TableField("rm_itemType")
  private String rm_itemType;
  @TableField("rm_supplier")
  private String rm_supplier;
  @TableField(exist = false)
  private String updatePersonnel;
}
