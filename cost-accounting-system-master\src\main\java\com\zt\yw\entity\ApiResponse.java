package com.zt.yw.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor //有参构造器
@NoArgsConstructor  //无参构造器
public class ApiResponse implements Serializable {

    private int code;
    private String msg;
    private List<CurrencyRate> data;

    // Getters and setters

    @Data
    @AllArgsConstructor //有参构造器
    @NoArgsConstructor
    public static class CurrencyRate {
        private String name;
        private String nameDesc;
        private double price;
        private String from;
        private String to;





        // Getters and setters
    }
}

