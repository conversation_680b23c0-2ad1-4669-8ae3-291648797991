import VueRouter from 'vue-router'
import Index from '../components/index.vue'
import Product from '../components/product.vue'
import Succession from '../components/succession.vue'
import Material from '../components/material.vue'
import LaborCost from '../components/laborCost.vue'
import Freight from '../components/freight.vue'
import User from '../components/user.vue'
import Rates from '../components/rates.vue'
import Package from '../components/package.vue'
import Login from '../components/login.vue'
import { cookies } from '../common/utils'
import store from '../store'

const router = new VueRouter({
  routes: [
    {
      name: 'login',
      path: '/login',
      component: Login
    },
    {
      name: 'index',
      path: '/',
      // redirect: '/product',
      component: Index,
      children: [
        {
          name: 'product',
          path: '/product',
          component: Product,
          beforeEnter: (to, from, next) => {
            if (cookies.getCookie('power') === '1' || cookies.getCookie('power') === '7' || cookies.getCookie('power') === '8') {
              next()
            } else {
              return false
            }
          }
        },
        {
          name: 'succession',
          path: '/succession',
          component: Succession,
          beforeEnter: (to, from, next) => {
            if (cookies.getCookie('power') === '1' || cookies.getCookie('power') === '8') {
              next()
            } else {
              return false
            }
          }
        },
        {
          name: 'material',
          path: '/material',
          component: Material,
          beforeEnter: (to, from, next) => {
            if (cookies.getCookie('power') === '1' || cookies.getCookie('power') === '2' || cookies.getCookie('power') === '7' || cookies.getCookie('power') === '8') {
              next()
            } else {
              return false
            }
          }
        },
        {
          name: 'laborCost',
          path: '/laborCost',
          component: LaborCost,
          beforeEnter: (to, from, next) => {
            if (cookies.getCookie('power') === '1' || cookies.getCookie('power') === '6' || cookies.getCookie('power') === '8') {
              next()
            } else {
              return false
            }
          }
        },
        {
          name: 'package',
          path: '/package',
          component: Package,
          beforeEnter: (to, from, next) => {
            if (cookies.getCookie('power') === '1' || cookies.getCookie('power') === '2' || cookies.getCookie('power') === '7' || cookies.getCookie('power') === '8') {
              next()
            } else {
              return false
            }
          }
        },
        {
          name: 'freight',
          path: '/freight',
          component: Freight,
          beforeEnter: (to, from, next) => {
            if (cookies.getCookie('power') === '1' || cookies.getCookie('power') === '3') {
              next()
            } else {
              return false
            }
          }
        },
        {
          name: 'user',
          path: '/user',
          component: User,
          beforeEnter: (to, from, next) => {
            if (cookies.getCookie('power') === '1') {
              next()
            } else {
              return false
            }
          }
        },
        {
          name: 'rates',
          path: '/rates',
          component: Rates,
          beforeEnter: (to, from, next) => {
            if (cookies.getCookie('power') === '1' || cookies.getCookie('power') === '5' || cookies.getCookie('power') === '7' || cookies.getCookie('power') === '8') {
              next()
            } else {
              return false
            }
          }
        }
      ]
    }
  ]
})
store.dispatch('getFXRates')
router.beforeEach((to, from, next) => {
  if (to.path !== '/login' && !cookies.getCookie('userName')) {
    next({path: 'login'})
  } else if (to.path === '/login' && cookies.getCookie('userName')) {
    next({path: '/'})
  } else {
    next()
  }
})
export default router
