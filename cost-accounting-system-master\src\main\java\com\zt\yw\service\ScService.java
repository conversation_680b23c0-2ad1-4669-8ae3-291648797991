package com.zt.yw.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zt.yw.entity.R;
import com.zt.yw.entity.RmTable;
import com.zt.yw.entity.Succession;
import org.springframework.stereotype.Service;

import java.util.Map;

//系列配置
@Service
public interface ScService {
    //新增
    boolean savaSc (Succession succession);
    //新增配置物料
    boolean insertScRm (RmTable rmTable);

    // 分页查询
    Page<Succession> selectSc(Page<Succession> page, Succession succession);

    //修改
    Integer updateSc(Succession succession);

    //删除
    R<String> deleteSc(Succession succession);

    //按id查询
    Map<String, Object> selectScList(Succession succession);
}
