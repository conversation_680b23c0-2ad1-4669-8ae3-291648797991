package com.zt.yw.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.liuyueyi.quick.transfer.ChineseUtils;
import com.zt.yw.common.ResultCode;
import com.zt.yw.dao.*;
import com.zt.yw.entity.*;
import com.zt.yw.service.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import static org.springframework.util.StringUtils.*;

@Slf4j
@RequestMapping(value = "/api")
@RestController
public class ApiController {
    @Resource
    private MainMapper mainMapper;
    @Resource
    private RmMapper rmMapper;
    @Resource
    private LcMapper lcMapper;
    @Resource
    private FrMapper frMapper;
    @Resource
    private PkMapper pkMapper;
    @Resource
    private UserMapper userMapper;
    @Resource
    private GoodsMapper goodsMapper;
    @Resource
    private LaborCostMapper laborCostMapper;
    @Resource
    private FreightMapper freightMapper;
    @Resource
    private PackMapper packMapper;
    @Resource
    private MoneyMapper moneyMapper;
    @Resource
    private MaintableService maintableService;
    @Resource
    private AdminLcService adminLcService;
    @Resource
    private ScService scService;
    @Resource
    private ScMapper scMapper;
    @Resource
    private RecordRmMapper recordRmMapper;
    @Resource
    private RecordLcMapper recordLcMapper;
    @Resource
    private RecordPkMapper recordPkMapper;
    @Resource
    private UserService userService;
    @Resource
    private MoneyService moneyService;

    private static final DateFormat dataFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    @Resource
    private AdminLcMapper adminLcMapper;

    /**
     * 查询修改历史记录
     *
     * @小布学长
     */
    @PostMapping("/selectRc")
    public R selectRc(@RequestBody Maintable maintable){

        return R.ok(maintableService.selectRc(maintable));
    }



    /**
     * 新增main_lc
     *
     * @小布学长
     */
    public R admin_lcadd(@RequestBody AdminLc adminLc){
        return R.ok(adminLcService.savaadmin_lc(adminLc));
    }

    /**
     * 新增系列
     *
     * @小布学长
     */
    @RequestMapping("/ScAdd")
    public R ScAdd(@RequestBody Succession succession){
        return R.ok(scService.savaSc(succession));
    }
    /**
     * 新增系列物料
     *
     * @小布学长
     */
    @RequestMapping("/insertScRm")
    public R insertScRm(@RequestBody RmTable rmTable){
        return R.ok(scService.insertScRm(rmTable));
    }

    /**
     * 汇率查询
     *
     * @小布学长
     */
    @PostMapping("/selectMoney")
    public R<List> selectMoney(MoneyApi moneyApi) {
        return R.ok(moneyService.selectMoney(moneyApi));
    }

    /**
     * 汇率修改
     *
     * @小布学长
     */
    @PostMapping("/updateMoney")
    public R<Integer> updateMoney(@RequestBody MoneyApi moneyApi) {
        return R.ok(moneyService.updateMoney(moneyApi));
    }

    /**
     * 用户查询
     *
     * @小布学长
     */
    @PostMapping("/selectUser")
    public R<Page<User>> selectUser(Page<User> page, User user) {
        return R.ok(userService.selectUser(page, user));
    }

    /**
     * 修改用户
     *
     * @小布学长
     */
    @PostMapping("/updateUser")
    public R<Integer> updateUser(@RequestBody User user) {
        return R.ok(userService.updateUser(user));
    }

    /**
     * 删除用户
     *
     * @小布学长
     */
    @PostMapping("/deleteUser")
    public R<Integer> deleteUser(@RequestBody User user) {
        return R.ok(userService.deleteUser(user));
    }
    /**
     * 修改系列
     *
     * @小布学长
     */
    @PostMapping("/updateSc")
    public R<Integer> updateSc(@RequestBody Succession succession) {
        return R.ok(scService.updateSc(succession));
    }

    /**
     * 修改系列
     *
     * @小布学长
     */
    @PostMapping("/updateadminFr")
    public R<Integer> updateadminFr(@RequestBody Frtable frtable) {
        return R.ok(maintableService.updateadminFr(frtable));
    }

    /**
     * 查询系列
     *
     * @小布学长
     */
    @PostMapping("/selectSc")
    public R<Page<Succession>> selectSc(Page<Succession> page, Succession succession){
        return R.ok(scService.selectSc(page,succession));
    }
    /**
     * 删除系列
     *
     * @小布学长
     */
    @PostMapping("/deleteSc")
    public R<R<String>> deleteSc(@RequestBody Succession succession) {
        return R.ok(scService.deleteSc(succession));
    }

    /**
     * 配置查询全部 系列
     *
     * @小布学长
     */
    @PostMapping("/selectScList")
    public  R<Map<String, Object>>  selectScList(@RequestBody Succession succession) {
        return R.ok(scService.selectScList(succession));
    }

    /**
     * 主品查询物料，根据uuid并且模糊查询种类
     *
     * @小布学长
     */
    @PostMapping("/selectRmType")
    public  R<List>  selectRmType(RmTable rmTable) {
        return R.ok(adminLcService.selectRmType(rmTable));
    }

    /**
     * 新增main
     *
     * @小布学长
     */
    @RequestMapping("/mainadd")
    public R mainadd(@RequestBody Maintable maintable){
        //打印接收的JSON格式数据
        log.info("品号 === " + maintable.getUuid() + " 品名 === " + maintable.getProductName() +"更新人员=== "+maintable.getUpdatePersonnel() +" 最后更新时间 = ===" + maintable.getUpdateTime());
        Date now1 = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        log.info("获取当前时间变成uuid===="+format.format(now1));
        maintable.setUuid(format.format(now1));
        return R.ok(mainMapper.insert(maintable));
    }


    /**
     * 新增main,新版
     *
     * @小布学长
     */
    @RequestMapping("/updateadmin2")
    public R<Boolean> updateadmin2(@RequestBody Maintable maintable){
          return R.ok(maintableService.updateadmin(maintable));
    }

    /**
     * 修改main,直接先删除 然后再调用新增接口
     *
     * @小布学长
     */
    @RequestMapping("/mainadd2")
    public R<Boolean> mainadd2(@RequestBody Maintable maintable){
        return R.ok(maintableService.savamain(maintable));
    }

    /**
     *
     * 新增原材料
     *
     * @小布学长
     */
    @RequestMapping("/rmadd")
    public R rmadd(@RequestBody RmTable rmTable){
        //打印接收的JSON格式数据
       log.info("材料新增"+rmTable);
        return R.ok(rmMapper.insert(rmTable));
    }
    /**
     * 新增LC人工
     *
     * @小布学长
     */
    @RequestMapping("/lcadd")
    public R lcadd(@RequestBody Lctable lctable){
        //打印接收的JSON格式数据
        log.info("人工新增"+lctable);
        return R.ok(lcMapper.insert(lctable));
    }
    /**
     * 新增pk包装材料
     *
     * @小布学长
     */
    @RequestMapping("/pkadd")
    public R pkadd(@RequestBody PkTable pkTable){
        //打印接收的JSON格式数据
        log.info("包装材料新增"+pkTable);


        return R.ok(pkMapper.insert(pkTable));
    }
    /**
     * 新增fr运费
     *
     * @小布学长
     */
    @RequestMapping("/fradd")
    public R fradd(@RequestBody Frtable frtable){
        //打印接收的JSON格式数据
        log.info("包装材料新增"+frtable);
        return R.ok(frMapper.insert(frtable));
    }
    /**
     * 新增物料配置goods
     *
     * @小布学长
     */
    @RequestMapping("/goodsadd")
    public R goodsadd(@RequestBody Goodslist goodslist){
        //打印接收的JSON格式数据
        Date now1 = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        log.info("获取当前时间变成uuid===="+format.format(now1));
        QueryWrapper<Goodslist> queryWrapper = new QueryWrapper<>();
        boolean rm_material= goodsMapper.exists(queryWrapper.eq("rm_material",goodslist.getRm_material()));;
        if (!rm_material) {
            goodslist.setRm_number(format.format(now1));
            return R.ok(goodsMapper.insert(goodslist));
        }
        return R.fail(ResultCode.FAILURE);

    }

    @RequestMapping("/goodsadd2")
    public R goodsadd2(@RequestBody Goodslist goodslist){
        //打印接收的JSON格式数据
        Date now1 = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        log.info("获取当前时间变成uuid===="+format.format(now1));
        goodslist.setRm_number(format.format(now1));
        log.info("新增物料配置"+goodslist);

        return R.ok(goodsMapper.insert(goodslist));
    }



    /**
     * 新增包装物料配置packinglist
     *
     * @小布学长
     */
    @RequestMapping("/packadd")
    public R packadd(@RequestBody Packinglist packinglist){
        //打印接收的JSON格式数据
        Date now1 = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        log.info("获取当前时间变成uuid===="+format.format(now1));
        QueryWrapper<Packinglist> queryWrapper = new QueryWrapper<>();
        boolean pk_material= packMapper.exists(queryWrapper.eq("pk_material",packinglist.getPk_material()));;
        if (!pk_material) {
            packinglist.setPk_number(format.format(now1));
            return R.ok(packMapper.insert(packinglist));
        }

        return R.fail(ResultCode.FAILURE);
    }

    /**
     * 新增人工配置LaborCost
     *
     * @小布学长
     */
    @RequestMapping("/LaborCostadd")
    public R LaborCostadd(@RequestBody LaborCost laborCost){
        QueryWrapper<LaborCost> queryWrapper = new QueryWrapper<>();
        boolean lc_name= laborCostMapper.exists(queryWrapper.eq("lc_name",laborCost.getLc_name()));;
        if (!lc_name) {
            return R.ok(laborCostMapper.insert(laborCost));
        }
        //打印接收的JSON格式数据
        log.info("新增人工配置"+laborCost);
        return R.fail(ResultCode.FAILURE);
    }

    /**
     * 新增运费配置Freight
     *
     * @小布学长
     */
    @RequestMapping("/Freightadd")
    public R Freightadd(@RequestBody Freight freight){
        //打印接收的JSON格式数据
        log.info("新增运费配置"+freight);
        return R.ok(freightMapper.insert(freight));
    }


    /**
     * 删除admin,并删除所有该UUID数据
     *
     *
     * @小布学长
     */
    @PostMapping("/deleteadmin")
    public R<String> deleteadmin(@RequestBody Maintable maintable) {
        if(hasText(maintable.getUuid())){
          R.fail(ResultCode.FAILURE);
        }
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("uuid",maintable.getUuid());//相当于where uuid="xxxx"
        try{
            rmMapper.delete(wrapper); //附表删除 原材料
            lcMapper.delete(wrapper);//附表删除
            pkMapper.delete(wrapper);//附表删除
            frMapper.delete(wrapper);//附表删除
            recordPkMapper.delete(wrapper); //附表删除 修改记录
            recordRmMapper.delete(wrapper);
            recordLcMapper.delete(wrapper);
            mainMapper.delete(wrapper);

            return R.ok("删除成功");
        }catch(Exception e){
            log.info("LOG: 成本核算系统：删除操作：发生异常：{}", e.toString());
            return R.fail(ResultCode.FAILURE);
        }
    }

    /**
     * 删除rm原材料
     *
     *
     * @小布学长
     */
    @PostMapping("/deleterm")
    public R<Integer> deleterm(@RequestBody RmTable rmTable) {
         log.info("删除原材料资料==="+rmTable);

        return R.ok(rmMapper.deleteById(rmTable.getRm_id()));

    }
    /**
     * 删除lc人工
     *
     *
     * @小布学长
     */
    @PostMapping("/deletelc")
    public R<Integer> deletelc(@RequestBody Lctable lctable) {
        log.info("删除人工资料==="+lctable);
        return R.ok(lcMapper.deleteById(lctable.getLc_id()));
    }

    /**
     * 删除pk包装
     *
     *
     * @小布学长
     */
    @PostMapping("/deletepk")
    public R<Integer> deletepk(@RequestBody PkTable pkTable) {
        log.info("删除包装材料==="+pkTable);
        return R.ok(pkMapper.deleteById(pkTable.getPk_id()));
    }

    /**
     * 删除fr运费
     *
     *
     * @小布学长
     */
    @PostMapping("/deletefr")
    public R<Integer> deletefr(@RequestBody Frtable frtable) {
        log.info("删除fr运费==="+frtable);
        return R.ok(frMapper.deleteById(frtable.getFr_id()));
    }
    /**
     * 删除物料goods材料表
     *
     *
     *
     * @小布学长
     */
    @PostMapping("/deletegoods")
    public R<Integer> deletegoods(@RequestBody Goodslist goodslist) {
        log.info("删除goods材料表==="+goodslist);
        return R.ok(goodsMapper.deleteById(goodslist.getGoodsid()));
    }

    /**
     * 删除物料packinglist包装材料表
     *
     *
     *
     * @小布学长
     */
    @PostMapping("/deletepack")
    public R<Integer> deletpack(@RequestBody Packinglist packinglist) {
        log.info("删除goods材料表==="+packinglist);
        return R.ok(packMapper.deleteById(packinglist.getPackid()));
    }


    /**
     * 删除人工LaborCost表
     *
     *
     *
     * @小布学长
     */
    @PostMapping("/deleteLaborCost")
    public R<Integer> deleteLaborCost(@RequestBody LaborCost laborCost) {
        log.info("删除goods材料表==="+laborCost);
        return R.ok(laborCostMapper.deleteById(laborCost.getLaborCostid()));
    }

    /**
     * 删除人工Freight表
     *
     *
     *
     * @小布学长
     */
    @PostMapping("/deleteFreight")
    public R<Integer> deleteLaborCost(@RequestBody Freight freight) {
        log.info("删除goods材料表==="+freight);
        return R.ok(freightMapper.deleteById(freight.getFreightid()));
    }


    /**
     * 修改admin
     *
     * @小布学长
     */
    @PostMapping("/updateadmin")
    public R<Integer> updateadmin(@RequestBody Maintable maintable) {
        log.info("更新主表数据==="+maintable);
        return R.ok(mainMapper.updateById(maintable));
    }

    /**
     * 历史修改Rm
     *
     * @小布学长
     * @return
     */
    @PostMapping("/updaterm")
    public R<Integer> updaterm(@RequestBody RmTable rmTable ) {
        RecordRm record=new RecordRm();
        Date now1 = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        QueryWrapper<RmTable> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uuid",rmTable.getUuid());
        queryWrapper.eq("rm_number",rmTable.getRm_number());
        List<RmTable> recordList= rmMapper.selectList(queryWrapper);
        recordList.forEach(rmTable1 -> {
            BeanUtils.copyProperties(rmTable1,record);//使用BeanUtils复制
            record.setId(null);
            record.setUpdateTime(format.format(now1));
            record.setUpdatePersonnel(rmTable.getUpdatePersonnel());
            recordRmMapper.insert(record);
        });
       // rmMapper.update(rmTable,queryWrapper);
       return R.ok(rmMapper.updateById(rmTable));
    }
    /**
     * 修改Lc
     *
     * @小布学长
     * @return
     */
    @PostMapping("/updatelc")
    public R<Integer> updatelc(@RequestBody Lctable lctable) {
        RecordLc record=new RecordLc();
        Date now1 = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        QueryWrapper<Lctable> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uuid",lctable.getUuid());
        queryWrapper.eq("lc_name",lctable.getLc_name());
        List<Lctable> recordList= lcMapper.selectList(queryWrapper);
        recordList.forEach(lcTable1 -> {
            BeanUtils.copyProperties(lcTable1,record);//使用BeanUtils复制 把数据复制到修改记录表
            record.setId(null);
            record.setUpdateTime(format.format(now1));
            record.setUpdatePersonnel(lctable.getUpdatePersonnel());
            recordLcMapper.insert(record);
        });

        return R.ok(lcMapper.updateById(lctable));
    }

    /**
     * 修改pk包装
     *
     * @小布学长
     * @return
     */
    @PostMapping("/updatepk")
    public R<Integer> updatepk(@RequestBody PkTable pkTable) {
        RecordPk record=new RecordPk();
        Date now1 = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        QueryWrapper<PkTable> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uuid",pkTable.getUuid());
        queryWrapper.eq("Pk_number",pkTable.getPk_number());
        List<PkTable> recordList= pkMapper.selectList(queryWrapper);
        recordList.forEach(pkTable1 -> {
            BeanUtils.copyProperties(pkTable1,record);//使用BeanUtils复制
            record.setId(null);
            record.setUpdateTime(format.format(now1));
            record.setUpdatePersonnel(pkTable.getUpdatePersonnel());
            recordPkMapper.insert(record);
        });
        return R.ok(pkMapper.updateById(pkTable));
    }

    /**
     * 修改fr运费
     *
     * @小布学长
     * @return
     */
    @PostMapping("/updatefr")
    public R<Integer> updatefr(@RequestBody Frtable frtable) {

        return R.ok(frMapper.updateById(frtable));
    }

    /**
     * 修改goods物料配置材料 修改的同时修改主品内信息
     * @小布学长
     * @return
     */
    @PostMapping("/updategoods")
    public R<Integer> updategoods(@RequestBody Goodslist goodslist) {
        QueryWrapper<RmTable> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rm_number",goodslist.getRm_number());
        List<RmTable> rmTableList = rmMapper.selectList(queryWrapper);
        rmTableList.forEach(rmTable1 -> {
            BeanUtils.copyProperties(goodslist,rmTable1);//使用BeanUtils复制
            double amounts;
            if (rmTable1.getRm_consumption() <= 1) {
                //重新计算金额计算公式
                amounts = goodslist.getRm_price()*rmTable1.getRm_exchangeRate()*rmTable1.getRm_consumption()/rmTable1.getRm_loss();
            }else {
                amounts = goodslist.getRm_price()*rmTable1.getRm_exchangeRate()/rmTable1.getRm_consumption()/rmTable1.getRm_loss();
            }
            rmTable1.setRm_amounts(amounts);
            rmMapper.updateById(rmTable1);
        });

        return R.ok(goodsMapper.updateById(goodslist));
    }

    /**
     * 修改Packinglist包装物料配置材料
     *
     * @小布学长
     * @return
     */
    @PostMapping("/updatepack")
    public R<Integer> updatepack(@RequestBody Packinglist packinglist) {
        QueryWrapper<PkTable> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pk_number",packinglist.getPk_number());
        List<PkTable> pkTableList = pkMapper.selectList(queryWrapper);
        pkTableList.forEach(pkTable1 -> {

            BeanUtils.copyProperties(packinglist,pkTable1);//使用BeanUtils复制
            double amounts;
            if (pkTable1.getPk_consumption() <= 1) {
                amounts = packinglist.getPk_price()*pkTable1.getPk_exchangeRate()*pkTable1.getPk_consumption();
            }else {
                amounts = packinglist.getPk_price()*pkTable1.getPk_exchangeRate()/pkTable1.getPk_consumption();
            }
            pkTable1.setPk_amounts(amounts);
            pkMapper.updateById(pkTable1);
        });

        return R.ok(packMapper.updateById(packinglist));


    }


    /**
     * 修改LaborCost人工表，并同步修改系列主品中内人工单价
     *
     * @小布学长
     * @return
     */
    @PostMapping("/updateLaborCost")
    public R<Integer> updateLaborCost(@RequestBody LaborCost laborCost) {
        QueryWrapper<Lctable> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("lc_name",laborCost.getLc_name());
        List<Lctable> lcTableList = lcMapper.selectList(queryWrapper);
        lcTableList.forEach(lcTable1 -> {
            BeanUtils.copyProperties(laborCost,lcTable1,"lc_consumption");//使用BeanUtils复制,并忽略lc_consumption字段的复制

            lcMapper.updateById(lcTable1);
        });

        return R.ok(laborCostMapper.updateById(laborCost));

    }


    /**
     * 修改Freight运费表
     *
     * @小布学长
     * @return
     */
    @PostMapping("/updateFreight")
    public R<Integer> updateFreight(@RequestBody Freight freight) { return R.ok(freightMapper.updateById(freight));}


    /**
     * 查询全部主品号
     *
     * @小布学长
     */
    @GetMapping("/getAllTo")
    public R<Page<Maintable>> allUser(Page<Maintable> page,String productName, String productConnection, String number, String creator) {
        // 检查输入是否为 null，并进行处理
        String twOutProductName = StringUtils.isBlank(productName) ? null : ChineseUtils.tw2s(productName);
        String twOutproductConnection = StringUtils.isBlank(productConnection) ? null : ChineseUtils.tw2s(productConnection);
        String twOutcreator = StringUtils.isBlank(creator) ? null : ChineseUtils.tw2s(creator);

        LambdaQueryWrapper<Maintable> queryWrapper = new LambdaQueryWrapper<>();
        QueryWrapper<RmTable> qw = new QueryWrapper<>();
       // 根据rm_number查询出所有的z主品uuid
        qw.eq("rm_number", number);
        List<RmTable> uuidLists = rmMapper.selectList(qw);

        // 收集所有的uuid
        List<String> uuidList = uuidLists.stream()
                .map(RmTable::getUuid)
                .collect(Collectors.toList());

        //判断品号有没有值，执行并拼接根据品号查询的sql
        if (StringUtils.isNotBlank(number) ) {

            if (!uuidLists.isEmpty()) {
                queryWrapper.and(wrapper -> wrapper
                        .in(Maintable::getUuid, uuidList));
            } else {
                queryWrapper.eq(Maintable::getUuid, number);
            }

        }

        // 判断物料品名有没有值，执行并拼接查询的sql
        if (StringUtils.isNotBlank(productName) || StringUtils.isNotBlank(twOutProductName)) {
            queryWrapper.and(wrapper -> wrapper
                    .like(StringUtils.isNotBlank(productName), Maintable::getProductName, productName)
                    .or()
                    .like(StringUtils.isNotBlank(twOutProductName), Maintable::getProductName, twOutProductName));
        }

        // 判断公司有没有值，执行并拼接查询的sql
        if (StringUtils.isNotBlank(productConnection) || StringUtils.isNotBlank(twOutproductConnection)) {
            queryWrapper.and(wrapper -> wrapper
                    .like(StringUtils.isNotBlank(productConnection), Maintable::getProductConnection, productConnection)
                    .or()
                    .like(StringUtils.isNotBlank(twOutproductConnection), Maintable::getProductConnection, twOutproductConnection));
        }

        // 判断人员有没有值，执行并拼接查询的sql
        if (StringUtils.isNotBlank(creator) || StringUtils.isNotBlank(twOutcreator)) {
            queryWrapper.and(wrapper -> wrapper
                    .like(StringUtils.isNotBlank(creator), Maintable::getCreator, creator)
                    .or()
                    .like(StringUtils.isNotBlank(twOutcreator), Maintable::getCreator, twOutcreator));
        }
        //最后进行排序

        return R.ok(mainMapper.selectPage(page, queryWrapper));
    }

    /**
     *  查询全部主品号,并可以分页与模糊查询
     *
     * @小布学长
     * @return
     */
    @SneakyThrows
    @PostMapping("/PostAllTo")
    public R<Page<Maintable>> PostAllTo(Page<Maintable> page, String productName, String productConnection, String number, String creator) {
        // 检查输入是否为 null，并进行简体转繁体处理
        String twOutProductName = StringUtils.isBlank(productName) ? null : ChineseUtils.tw2s(productName);
        String twOutproductConnection = StringUtils.isBlank(productConnection) ? null : ChineseUtils.tw2s(productConnection);
        String twOutcreator = StringUtils.isBlank(creator) ? null : ChineseUtils.tw2s(creator);

        LambdaQueryWrapper<Maintable> queryWrapper = new LambdaQueryWrapper<>(); //复杂sql查询条件的方法
        QueryWrapper<RmTable> qw = new QueryWrapper<>();//简单sql查询条件的办法
        // 根据rm_number查询出所有的z主品uuid
        qw.eq("rm_number", number);
        List<RmTable> uuidLists = rmMapper.selectList(qw);

        // 收集所有的uuid
        List<String> uuidList = uuidLists.stream()
                .map(RmTable::getUuid)
                .collect(Collectors.toList());

        //判断品号有没有值，执行并拼接根据品号查询的sql
        if (StringUtils.isNotBlank(number) ) {

            if (!uuidLists.isEmpty()) {
                queryWrapper.and(wrapper -> wrapper
                        .in(Maintable::getUuid, uuidList));
            } else {
                queryWrapper.eq(Maintable::getUuid, number);
            }

        }

        // 判断物料品名有没有值，执行并拼接查询的sql
        if (StringUtils.isNotBlank(productName) || StringUtils.isNotBlank(twOutProductName)) {
            queryWrapper.and(wrapper -> wrapper
                    .like(StringUtils.isNotBlank(productName), Maintable::getProductName, productName)
                    .or()
                    .like(StringUtils.isNotBlank(twOutProductName), Maintable::getProductName, twOutProductName));
        }

        // 判断公司有没有值，执行并拼接查询的sql
        if (StringUtils.isNotBlank(productConnection) || StringUtils.isNotBlank(twOutproductConnection)) {
            queryWrapper.and(wrapper -> wrapper
                    .like(StringUtils.isNotBlank(productConnection), Maintable::getProductConnection, productConnection)
                    .or()
                    .like(StringUtils.isNotBlank(twOutproductConnection), Maintable::getProductConnection, twOutproductConnection));
        }

        // 判断人员有没有值，执行并拼接查询的sql
        if (StringUtils.isNotBlank(creator) || StringUtils.isNotBlank(twOutcreator)) {
            queryWrapper.and(wrapper -> wrapper
                    .like(StringUtils.isNotBlank(creator), Maintable::getCreator, creator)
                    .or()
                    .like(StringUtils.isNotBlank(twOutcreator), Maintable::getCreator, twOutcreator));
        }
        //排序
        queryWrapper.orderByDesc(Maintable::getCreationDate).orderByDesc(Maintable::getUpdateTime);


        // 执行分页查询并返回结果
        return R.ok(mainMapper.selectPage(page, queryWrapper));
    }


    /**
     * 新增： 查询全部主品号,模糊查询 根据型号与种类
     *
     * @小布学长
     * @return
     */
    @SneakyThrows
    @PostMapping("/SelectType_Model")
    public R<List>SelectType_Model(Succession succession) {
        QueryWrapper<Succession> queryWrapper = new QueryWrapper<>();
        //sql：型号模糊查询 and 类型模糊查询
        queryWrapper.like("sc_standard",succession.getSc_standard()).like("sc_name",succession.getSc_name());

        return R.ok(scMapper.selectList(queryWrapper));
    }

    /**
     * 根据UUid多表联合查询-原材料
     *
     * @小布学长
     * @return
     */
    @PostMapping("/selectRm")
    public R<List<RmTable>> selectRm(@RequestBody Maintable maintable) {
        // 创建查询包装器
        QueryWrapper<RmTable> wrapper = new QueryWrapper<>();

        // 添加条件：按照主表的uuid匹配记录
        wrapper.eq("uuid", maintable.getUuid());
        wrapper.orderByDesc("rm_id");

        // 添加排序规则：按照create_time字段倒序排序
    //    wrapper.orderByDesc("rm_id");

        // 执行查询并返回结果
        return R.ok(rmMapper.selectList(wrapper));
    }
    /**
     * 根据UUid多表联合查询-人工
     *
     * @小布学长
     * @return
     */
    @PostMapping("/selectLc")
    public R<List> selectLc(@RequestBody Maintable maintable) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("uuid",maintable.getUuid());//相当于where uuid="xxxx"
        wrapper.orderByDesc("lc_id");
        log.info("获取原材料uuid"+ maintable.getUuid());
        log.info("aaaaaaaaaaaaaaaaaaaaaaaaaaaa"+lcMapper.selectList(wrapper));
        return R.ok(lcMapper.selectList(wrapper));
    }

    /**
     * 根据UUid多表联合查询-包装
     *
     * @小布学长
     * @return
     */
    @PostMapping("/selectPk")
    public R<List> selectPk(@RequestBody Maintable maintable) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("uuid",maintable.getUuid());//相当于where uuid="xxxx"
        wrapper.orderByDesc("pk_id");
        return R.ok(pkMapper.selectList(wrapper));
    }

    /**
     * 根据UUid多表联合查询-运费
     *
     * @小布学长
     * @return
     */
    @PostMapping("/selectFr")
    public R<List> selectFr(@RequestBody Maintable maintable) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("uuid",maintable.getUuid());//相当于where uuid="xxxx"
        wrapper.orderByDesc("fr_id");
        return R.ok(frMapper.selectList(wrapper));
    }

    /**
     * 查询详情页多表联合查询返回指定id的用户
     *
     * @小布学长
     */

    @PostMapping("/getById")
    public R<Map<String, Object>> getById(@RequestBody Maintable maintable) {
        maintable =  mainMapper.selectById(maintable.getAdmin_id());
        // 创建 QueryWrapper 实例并设置排序条件

        // 对 RmTable 按照某个字段（例如 rm_id）降序排序
        QueryWrapper<RmTable> rmWrapper = new QueryWrapper<>();
        rmWrapper.eq("uuid",maintable.getUuid());//相当于where uuid="xxxx"
        rmWrapper.orderByDesc("rm_id");

        // 对 Lctable 按照某个字段（例如 lc_id）降序排序
        QueryWrapper<Lctable> lcWrapper = new QueryWrapper<>();
        lcWrapper.eq("uuid",maintable.getUuid());//相当于where uuid="xxxx"
        lcWrapper.orderByDesc("lc_id");

        // 对 PkTable 按照某个字段（例如 pk_id）降序排序
        QueryWrapper<PkTable> pkWrapper = new QueryWrapper<>();
        pkWrapper.eq("uuid",maintable.getUuid());//相当于where uuid="xxxx"
        pkWrapper.orderByDesc("pk_id");

        // 对 Frtable 按照某个字段（例如 fr_id）降序排序
        QueryWrapper<Frtable> frWrapper = new QueryWrapper<>();
        frWrapper.eq("uuid",maintable.getUuid());//相当于where uuid="xxxx"
        frWrapper.orderByDesc("fr_id");

        // 执行查询
        List<RmTable> rmTableList = rmMapper.selectList(rmWrapper);
        List<Lctable> lctableList = lcMapper.selectList(lcWrapper);
        List<PkTable> pkTableList = pkMapper.selectList(pkWrapper);
        List<Frtable> frtableList = frMapper.selectList(frWrapper);
        //多表查询结果list传到map赋值数组名
        Map<String,Object> map = new HashMap<>();
        map.put( "postForm",maintable);
        map.put("rawMaterialTableData", rmTableList);
        map.put("LaborCostTableData", lctableList);
        map.put("packageTableData", pkTableList);
        map.put("freightTableData", frtableList);
        return R.ok(map);




    }


    /**
     * 查询详情页多表联合查询返回指定id的用户(可一次性查询多个UUID版本)
     *
     * @小布学长
     */

    @PostMapping("/ByIdlist")
    public R<List<Map<String, Object>>> ByIdlist( @RequestBody MaintableQuery maintainable) {
        log.info("查询详情ids===="+maintainable);
        List<Map<String, Object>> data = new ArrayList<>();
        List<Integer> adminIds = maintainable.getAdmin_id();
        if(CollectionUtils.isEmpty(adminIds)){
            return R.ok();
        }
        adminIds.forEach(adminId->{

            Maintable maintable =  mainMapper.selectById(adminId);
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.eq("uuid",maintable.getUuid());//相当于where uuid="xxxx"
            log.info("获取到关键值UUID==="+maintable.getUuid());

            List<RmTable> rmTableList = rmMapper.selectList(wrapper); //附表查询 原材料
            List<Lctable> lctableList = lcMapper.selectList(wrapper);//附表查询
            List<PkTable> pkTableList = pkMapper.selectList(wrapper);//附表查询
            List<Frtable> frtableList = frMapper.selectList(wrapper);//附表查询

            //多表查询结果list传到map赋值数组名
            Map<String,Object> map = new HashMap<>();
            map.put( "postForm",maintable);
            map.put("rawMaterialTableData", rmTableList);
            map.put("LaborCostTableData", lctableList);
            map.put("packageTableData", pkTableList);
            map.put("freightTableData", frtableList);
            data.add(map);
        });
        return R.ok(data);
    }

    /**
     * 添加用户
     *
     * @param
     * @return
     */
    @SneakyThrows
    @PostMapping("/addUserTo")
    public R<Object> addUser(@RequestBody User user) {
        String username = user.getUserName();
        String password = user.getPassword();
        Date now1 = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        user.setRegistrationdate(format.format(now1));
        if (username == null || password == null) {
            return R.fail(500, "错误！用户名或者密码为空！");
        }
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("userName", username);
        User tu = userMapper.selectOne(queryWrapper);
        if (tu != null) {
            return R.fail(500, "用户名已被注册");

        }

        int rows = userMapper.insert(user);
        if (rows != 1) {
            return  R.fail(500, "注册失败");
        }
        return R.ok("注册成功");
    }



    /**
     * 登录
     *
     *
     * @小布学长
     * @return
     */
    @PostMapping("/userdo")
    public R<User> PostUser(User user, HttpServletResponse response) {
        if(null == user){

            return R.fail(500,"错误！用户名为空！");
        }
        if(!hasText(user.getUserName())){

            return R.fail(500,"错误！请检查用户名或者密码！");
        }
        if(!hasText(user.getUserName())){


            return R.fail(500,"错误！请检查用户名或者密码！");
        }
        User record = userMapper.selectOne(Wrappers.<User>lambdaQuery().eq(User::getUserName, user.getUserName()).eq(User::getPassword, user.getPassword()));
        if(null == record){
            return R.fail(500,"用户名或密码错误");
        }
        log.info("登录账户:"+user.getUserName()+"--登录成功");
        Date now1 = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        QueryWrapper queryWrapper = new QueryWrapper<>();
        record.setLast_login_time(format.format(now1));
        queryWrapper.eq("last_login_time",user.getLast_login_time());
        userMapper.updateById(record);//添加最后登录时间
        return R.ok(record);
    }
    /**
     * 物料配置查询 已重写暂未使用
     *
     * @小布学长
     * @return
     */
    @SneakyThrows
    @PostMapping("/GoodsAll123")
    public R<Page<Goodslist>> GoodsAll123(Page<Goodslist> page, Goodslist goodslist) {
        // 简单分页查询
        QueryWrapper<Goodslist> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().orderByDesc(Goodslist::getGoodsid);//进行排序查询
        //if判断 如果用户没输入品号以及品名条件就执行无条件全部查询，如有输入就按照值来查询，如果用户同时输入品号以及品名那么会if优先执行品号查询
        if ((goodslist.getRm_number() != null)&(!goodslist.getRm_number().isEmpty())&(!"null".equals(goodslist.getRm_number()))){
            queryWrapper.eq("rm_number", goodslist.getRm_number());//eq代表只能是对于==
            log.info("物料执行按照品号条件查询");
        }else {
            if ((goodslist.getRm_material()!=null)&(!goodslist.getRm_material().isEmpty())&(!"null".equals(goodslist.getRm_material()))){
                // 简体转台湾繁体
                String twOut = ChineseUtils.s2tw(goodslist.getRm_material());
                log.info("s2tw --> {}", twOut);
                //进行简繁同时查询(lc_name LIKE ? OR lc_name LIKE ?)
                queryWrapper.like("rm_material", goodslist.getRm_material()).or().like("rm_material",twOut).or().like("rm_succession",goodslist.getRm_succession()).or().like("rm_itemType", goodslist.getRm_itemType()); //like代表前后的可模糊查询 %aaa%
                log.info("物料执行按照品名模糊查询");
            }else {
                log.info("物料执行查询全部");
            }
        }
        return  R.ok(goodsMapper.selectPage(page, queryWrapper));

    }

    /**
     * 包装物料配置查询
     *
     *
     * @小布学长
     * @return
     */
    @SneakyThrows
    @PostMapping("/PackAll")
    public R<Page<Packinglist>> PackAll(Page<Packinglist> page, Packinglist packinglist) {
        // 简单分页查询
        QueryWrapper<Packinglist> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().orderByDesc(Packinglist::getPackid);//进行排序查询
        //if判断 如果用户没输入品号以及品名条件就执行无条件全部查询，如有输入就按照值来查询，如果用户同时输入品号以及品名那么会if优先执行品号查询
        if ((packinglist.getPk_number() != null)&(!packinglist.getPk_number().isEmpty())&(!"null".equals(packinglist.getPk_number()))){
            queryWrapper.eq("pk_number", packinglist.getPk_number());//eq代表只能是对于==
            log.info("物料执行按照品号条件查询");
        }else {
            if ((packinglist.getPk_material()!=null)&(!packinglist.getPk_material().isEmpty())){
                // 简体转台湾繁体
                String twOut = ChineseUtils.tw2s(packinglist.getPk_material());
                log.info("s2tw --> {}", twOut);
                //进行简繁同时查询(lc_name LIKE ? OR lc_name LIKE ?)
                queryWrapper.like("pk_material", packinglist.getPk_material()).or().like("pk_material",twOut); //like代表前后的可模糊查询 %aaa%
                log.info("物料执行按照品名模糊查询");
            }else {
                log.info("物料执行查询全部");
            }
        }
        return  R.ok(packMapper.selectPage(page, queryWrapper));

    }



    /**
     * 人工配置查询
     *
     * @小布学长
     * @return
     */
    @SneakyThrows
    @PostMapping("/LaborCostAll")
    public R<Page<LaborCost>> LaborCostAll(Page<LaborCost> page, LaborCost laborCost) {
        // 简单分页查询
        QueryWrapper<LaborCost> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().orderByDesc(LaborCost::getLaborCostid);//进行排序查询
        //if判断 如果用户没输入品号以及品名条件就执行无条件全部查询，如有输入就按照值来查询，如果用户同时输入品号以及品名那么会if优先执行品号查询
            if ((laborCost.getLc_name()!=null)&(!laborCost.getLc_name().isEmpty())){

                // 简体转台湾繁体
                String twOut = ChineseUtils.s2tw(laborCost.getLc_name());
                log.info("s2tw --> {}", twOut);

                //进行简繁同时查询(lc_name LIKE ? OR lc_name LIKE ?)
                queryWrapper.like("lc_name", laborCost.getLc_name()).or().like("lc_name",twOut); //like代表前后的可模糊查询 %aaa%

                log.info("人工执行按照品名模糊查询");
            }else {
                log.info("人工执行查询全部");
            }
        return  R.ok(laborCostMapper.selectPage(page, queryWrapper));
    }
    /**
     * 运费配置查询
     *
     * @小布学长
     * @return
     */
    @SneakyThrows
    @PostMapping("/FreightAll")
    public R<Page<Freight>> FreightAll(Page<Freight> page, Freight freight) {
        // 简单分页查询
        QueryWrapper<Freight> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().orderByDesc(Freight::getUpdateTime);//进行排序查询
        //if判断 如果用户没输入品号以及品名条件就执行无条件全部查询，如有输入就按照值来查询，如果用户同时输入品号以及品名那么会if优先执行品号查询
        if ((freight.getFr_cbm()!=null)&(!freight.getFr_cbm().isEmpty())){
            // 简体转台湾繁体
            String twOut = ChineseUtils.s2tw(freight.getFr_cbm());
            log.info("s2tw --> {}", twOut);
            //进行简繁同时查询(lc_name LIKE ? OR lc_name LIKE ?)
            queryWrapper.like("fr_cbm", freight.getFr_cbm()).or().like("fr_cbm",twOut); //like代表前后的可模糊查询 %aaa%
            log.info("运费执行按照品名模糊查询");
        }else {
            log.info("运费执行查询全部");
        }
        return  R.ok(freightMapper.selectPage(page, queryWrapper));
    }

    /**
     * 物料按照种类筛选查询
     *
     * @小布学长
     * @return
     */
    @SneakyThrows
    @PostMapping("/GoodsAll")
    public R<Page<Goodslist>> GoodsAll(Page<Goodslist> page, Goodslist goodslist) {
        // 简单分页查询
        QueryWrapper<Goodslist> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().orderByDesc(Goodslist::getGoodsid); // 进行排序查询

        if (hasText(goodslist.getRm_number())) {
            // 判断品号是否有值，如果有就进行品号筛选
            queryWrapper.eq("rm_number", goodslist.getRm_number());
        }

        if (hasText(goodslist.getRm_material())) {
            // 简体转台湾繁体
            String twOut = ChineseUtils.tw2s(goodslist.getRm_material());
            log.info("tw2s --> {}", twOut);
            // 判断品名是否有值，如果有就进行品名筛选
            queryWrapper.and(wrapper -> wrapper
                    .like("rm_material", goodslist.getRm_material())
                    .or()
                    .like("rm_material", twOut)); // 用户输入简体繁体都可以查询
        }

        if (hasText(goodslist.getRm_itemType())) {
            // 判断种类是否有值，如果有就进行种类筛选
            queryWrapper.like("rm_itemType", goodslist.getRm_itemType());
        }

        return R.ok(goodsMapper.selectPage(page, queryWrapper));
    }






}

