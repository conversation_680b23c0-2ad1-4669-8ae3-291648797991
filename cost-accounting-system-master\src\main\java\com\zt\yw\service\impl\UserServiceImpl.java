package com.zt.yw.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.liuyueyi.quick.transfer.ChineseUtils;
import com.zt.yw.dao.ScMapper;
import com.zt.yw.dao.UserMapper;

import com.zt.yw.entity.User;
import com.zt.yw.service.UserService;
import org.springframework.stereotype.Service;


import javax.annotation.Resource;

@Service
public class UserServiceImpl implements UserService {
    @Resource
    private UserMapper userMapper;

    @Override
    public Page<User> selectUser(Page<User> page, User user) {
        QueryWrapper<com.zt.yw.entity.User> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().orderByDesc(User::getRegistrationdate);//进行排序查询
        //if判断 如果用户没输入品号以及品名条件就执行无条件全部查询，如有输入就按照值来查询，如果用户同时输入品号以及品名那么会if优先执行品号查询
        if ((user.getUserName()!=null)&(!user.getUserName().isEmpty())){   //该判断适合字符串对比
            // 简体转台湾繁体
            String twOut = ChineseUtils.s2tw(user.getUserName());
            System.out.println("s2tw --> " + twOut);
            //进行简繁同时查询(lc_name LIKE ? OR lc_name LIKE ?)
            queryWrapper.like("userName", user.getUserName()).or().like("userName",twOut); //like代表前后的可模糊查询 %aaa%
            //  log.info("主品名执行按照品名模糊查询");
        }else {
            // log.info("主品名执行查询全部");
        }
        return userMapper.selectPage(page,queryWrapper);
    }

    @Override
    public Integer updateUser(User user)  {
        return userMapper.updateById(user);
    }

    @Override
    public Integer deleteUser(User user) {
        return userMapper.deleteById(user);
    }

    @Override
    public Integer updatePass(User user) {
        QueryWrapper queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("password",user.getPassword());
        return userMapper.updateById(user);
    }
}
