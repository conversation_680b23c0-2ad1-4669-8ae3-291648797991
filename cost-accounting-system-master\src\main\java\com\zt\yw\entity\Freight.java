package com.zt.yw.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor //有参构造器
@NoArgsConstructor  //无参构造器
@TableName("freight") //运费配置库
public class Freight implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "freightid", type = IdType.AUTO)  //如数据库为自增id添加该行可解决数据库自增序号突然变大的问题，或者数据库id类型使用Long类型
    private  Integer freightid;
    @TableField("fr_factory")
    private  String fr_factory;
    @TableField("fr_port")
    private  String fr_port;
    @TableField("fr_cbm")
    private String fr_cbm;
    @TableField("fr_price")
    private  double fr_price;
    @TableField("cmb")
    private String cmb;
    @TableField("terms")
    private String terms;
    @TableField("updateTime")
    private String updateTime; //更新时间
    @TableField("remark")
    private String remark;//备注
    @TableField("shippingType")
    private String shippingType;//出货类型
    @TableField("routes")
    private String routes;//出货线路，欧美，亚洲
    @TableField("others")
    private Integer others;//其他费用

}
