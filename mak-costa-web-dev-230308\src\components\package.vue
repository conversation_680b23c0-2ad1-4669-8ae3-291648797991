<template>
  <div class="main">
    <div class="query">
      <div class="query-wrapper">
        <div class="query-info">
          <span>品号：</span>
          <el-input
            v-model="packNum"
            size="mini"
            placeholder="请输入品号查询"
            @keyup.enter.native="seach"
            clearable>
          </el-input>
        </div>
        <div class="query-info">
          <span>品名：</span>
          <el-input
            v-model="packName"
            size="mini"
            placeholder="请输入品名查询"
            @keyup.enter.native="seach"
            clearable>
          </el-input>
        </div>
        <div class="query-info">
          <el-button size="mini" @click="seach" icon="el-icon-search" type="primary" plain>查询</el-button>
        </div>
        <div class="query-info" style="text-align:right" v-if="power === '1' || power === '2'">
          <el-button type="primary" size="mini" icon="el-icon-plus" @click="handleAddPack">新增</el-button>
        </div>
      </div>
    </div>
    <el-table
      v-loading="loading"
      ref="table"
      class="mainTable"
      :data="packTableData"
      :header-cell-style='{"backgroundColor": "rgb(244, 244, 245)"}'
      :height="tableHeight"
      stripe
    >
      <el-table-column
        label="#"
        type="index"
        align="center"
        width="60"
      />
      <el-table-column
        prop="pk_number"
        label="品号"
        min-width="120"
      />
      <el-table-column
        prop="pk_material"
        label="品名"
      />
      <el-table-column
        label="尺寸（英吋）"
        align="left"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.pk_itemType === 'carton'">
            <span>{{ scope.row.pk_length + '" x ' + scope.row.pk_width + '" x ' + scope.row.pk_height + '"' }}</span>
          </div>
          <div v-else>
            <span>/</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="pk_itemType"
        label="种类"
        :formatter="fmtType"
      />
      <el-table-column
        prop="pk_supplier"
        label="厂商"
      />
      <el-table-column
        prop="pk_price"
        label="价格"
      />
      <el-table-column
        prop="pk_unit"
        label="单位"
      />
      <el-table-column
        prop="pk_currencyType"
        label="币种"
        :formatter="fmtCurrencyType"
      />
      <el-table-column
        prop="pk_exchangeRate"
        label="汇率"
      />
      <!-- <el-table-column
        prop="pk_consumption"
        label="用量"
      /> -->
      <!-- <el-table-column
        prop="pk_amounts"
        label="金额"
      /> -->
      <el-table-column v-if="power === '1' || power === '2' || power === '8'" label="操作" align="center" min-width="130">
        <template slot-scope="scope">
          <el-button type="text" icon="el-icon-edit" @click="handleUpdateGood(scope.row)">修改</el-button>
          <el-button type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="page">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="current"
        :page-sizes="[20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
    <!-- 新增/修改 -->
    <el-dialog :title="handleType==='create'?'新增':`修改《${editTitle}》内容`" :visible.sync="handleModal" :before-close="handleModalClose">
      <el-form :model="formData" class="form-wrap" :rules="rules" ref="ruleForm" size="mini">
        <el-form-item v-if="handleType==='update'" prop="pk_number" label="品号" label-width="100px">
          <el-input v-model="formData.pk_number" :disabled="true">
          </el-input>
        </el-form-item>
        <el-form-item prop="pk_material" label="品名" label-width="100px">
          <el-input v-model="formData.pk_material" placeholder="品名命名规则请尽量遵循：总称：颜色+材质+尺寸+特性(+厂商)" clearable>
          </el-input>
        </el-form-item>
        <el-form-item prop="pk_itemType" label="种类" label-width="100px">
          <el-select v-model="formData.pk_itemType" placeholder="请选择种类">
            <el-option
              v-for="item in pk_itemType_options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <template v-if="formData.pk_itemType === 'carton'">
          <el-form-item prop="pk_length" label="长" label-width="100px">
            <el-input-number v-model="formData.pk_length" controls-position="right" placeholder="请输入（单位：英吋）" :min="0" :precision="2" clearable>
            </el-input-number>
          </el-form-item>
          <el-form-item prop="pk_width" label="宽" label-width="100px">
            <el-input-number v-model="formData.pk_width" controls-position="right" placeholder="请输入（单位：英吋）单价" :min="0" :precision="2" clearable>
            </el-input-number>
          </el-form-item>
          <el-form-item prop="pk_height" label="高" label-width="100px">
            <el-input-number v-model="formData.pk_height" controls-position="right" placeholder="请输入（单位：英吋）" :min="0" :precision="2" clearable>
            </el-input-number>
          </el-form-item>
        </template>
        <el-form-item prop="pk_supplier" label="厂商" label-width="100px">
          <el-input v-model="formData.pk_supplier" placeholder="请输入厂商" clearable>
          </el-input>
        </el-form-item>
        <el-form-item prop="pk_price" label="价格" label-width="100px">
          <el-input-number v-model="formData.pk_price" controls-position="right" placeholder="请输入单价" :min="0" :precision="5" clearable>
          </el-input-number>
        </el-form-item>
        <el-form-item prop="pk_unit" label="单位" label-width="100px" style="position: relative;">
          <el-select v-model="formData.pk_unit" placeholder="注意成品用量变化，并通知主品配置人员" filterable allow-create default-first-optionclearable>
            <el-option
              v-for="item in unit_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <p v-if="handleType==='update'" style="padding: 0;margin: 0;font-size: 12px; color: #F56C6C;position: absolute;top: 64%;left:148px;">注意成品用量变化，并通知主品配置人员</p>
        <!-- <el-form-item prop="pk_consumption" label="用量" label-width="100px">
          <el-input-number v-model="formData.pk_consumption" placeholder="请输入用量" :min="0" clearable>
          </el-input-number>
        </el-form-item> -->
        <el-form-item prop="pk_currencyType" label="币种" label-width="100px">
          <el-select v-model="formData.pk_currencyType" @change="handleCTchange" placeholder="请选择币种">
            <el-option
              v-for="item in ct_options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="pk_exchangeRate" label="汇率" label-width="100px">
          <el-input-number v-model="formData.pk_exchangeRate" controls-position="right" :min="0" clearable />
        </el-form-item>
        <!-- <el-form-item prop="pk_amounts" label="金额" label-width="100px">
          <el-button type="text" icon="el-icon-search" @click="calculateRM" />
          <span>{{formData.pk_amounts}}</span>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="callOf">取 消</el-button>
        <el-button type="primary" size="mini" @click="handlePostForm('ruleForm')">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { cookies } from '../common/utils'
import ajax from '../common/axiosHttp'
export default {
  name: 'Package',
  data () {
    return {
      power: cookies.getCookie('power'),
      loading: false,
      tableHeight: 0,
      packTableData: [],
      current: 1,
      pageSize: 20,
      packName: '',
      packNum: '',
      total: 0,
      handleType: '',
      handleModal: false,
      formData: {
        pk_number: 0,
        pk_material: '',
        pk_itemType: '',
        pk_supplier: '',
        pk_price: 0,
        pk_unit: '',
        pk_currencyType: '',
        pk_exchangeRate: 1
        // pk_consumption: 0,
        // pk_amounts: 0
      },
      editTitle: '',
      rules: {
        pk_number: [
          {
            required: true,
            message: '请填写品号',
            trigger: 'blur'
          }
        ],
        pk_material: [
          {
            required: true,
            message: '请填写品名',
            trigger: 'blur'
          }
        ],
        pk_itemType: [
          {
            required: true,
            message: '请选择种类',
            trigger: 'change'
          }
        ],
        pk_length: [
          {
            required: true,
            message: '请填写',
            trigger: 'blur'
          }
        ],
        pk_width: [
          {
            required: true,
            message: '请填写',
            trigger: 'blur'
          }
        ],
        pk_height: [
          {
            required: true,
            message: '请填写',
            trigger: 'blur'
          }
        ],
        pk_price: [
          {
            required: true,
            type: 'number',
            message: '请填写单价',
            trigger: 'blur'
          }
        ],
        pk_unit: [
          {
            required: true,
            message: '请填写单位',
            trigger: 'blur'
          }
        ],
        // pk_consumption: [
        //   {
        //     required: true,
        //     type: 'number',
        //     message: '请填写用量',
        //     trigger: 'blur'
        //   }
        // ],
        pk_currencyType: [
          {
            required: true,
            message: '请选择币种',
            trigger: 'change'
          }
        ]
        // pk_amounts: [
        //   {
        //     required: true,
        //     message: '请填写金额',
        //     trigger: 'blur'
        //   }
        // ]
      }
    }
  },
  computed: {
    ...mapState(['FXRates', 'ct_options', 'CURRENCY_DIC', 'pk_itemType_options', 'PK_ITEM_DIC', 'unit_options'])
  },
  mounted () {
    // 挂载window.onresize事件(动态设置table高度)
    this.$nextTick(() => {
      this.tableHeight = window.innerHeight - 136
    })
    let _this = this
    window.onresize = () => {
      if (_this.resizeFlag) {
        clearTimeout(_this.resizeFlag)
      }
      _this.resizeFlag = setTimeout(() => {
        _this.getTableHeight()
        _this.resizeFlag = null
      }, 100)
    }
  },
  activated () {
    this.getPackage()
  },
  methods: {
    // 格式化种类
    fmtType (row, column, cellValue) {
      return this.PK_ITEM_DIC[cellValue]
    },
    // 计算table高度(动态设置table高度)
    getTableHeight () {
      let tableH = 180 // 距离页面下方的高度
      let tableHeightDetil = window.innerHeight - tableH
      if (tableHeightDetil <= 300) {
        this.tableHeight = 300
      } else {
        this.tableHeight = window.innerHeight - tableH
      }
    },
    // 搜索
    seach () {
      this.current = 1
      this.pageSize = 20
      this.getPackage()
    },
    // 获取货品列表
    getPackage () {
      this.loading = true
      let form = new FormData()
      form.append('current', this.current)
      form.append('size', this.pageSize)
      form.append('pk_material', this.packName)
      form.append('pk_number', this.packNum)
      ajax({
        method: 'POST',
        url: '/api/PackAll',
        data: form
      }).then(res => {
        this.loading = false
        this.total = res.total
        this.packTableData = res.records
      }).catch(() => {
        this.loading = false
      })
    },
    // 页码分页
    handleSizeChange (val) {
      this.pageSize = val
      this.getPackage()
    },
    // 分页
    handleCurrentChange (val) {
      this.current = val
      this.getPackage()
    },
    // 格式化币种
    fmtCurrencyType (row, column, cellValue) {
      return this.CURRENCY_DIC[cellValue]
    },
    // 新增
    handleAddPack () {
      this.formData = {}
      this.handleType = 'create'
      this.handleModal = true
    },
    // 修改
    handleUpdateGood (row) {
      this.editTitle = row.pk_material
      this.formData = row
      this.handleType = 'update'
      this.handleModal = true
    },
    // 选择币种
    handleCTchange (val) {
      this.formData.pk_exchangeRate = this.FXRates[val]
    },
    // 关闭提醒
    handleModalClose (done) {
      this.$confirm('确认关闭吗？').then(_ => {
        done()
        this.formData = {}
        this.$refs['ruleForm'].resetFields()
      })
        .catch(_ => {})
    },
    // 关闭模态框
    callOf () {
      this.handleModal = false
      this.formData = {
        pk_number: 0,
        pk_material: '',
        pk_itemType: '',
        pk_supplier: '',
        pk_price: 0,
        pk_unit: '',
        pk_currencyType: '',
        pk_exchangeRate: 1
      }
      this.$refs['ruleForm'].resetFields()
      this.getPackage()
    },
    // 计算金额
    // calculateRM () {
    //   if (!this.formData.pk_price === '') return
    //   if (!this.formData.pk_consumption === '') return
    //   let AMOUNTS = 0
    //   let loss = 0.97
    //   if (this.formData.pk_material === '油墨') {
    //     console.log('油墨来了')
    //     loss = 1
    //   }
    //   if (this.formData.pk_consumption === 0) {
    //     AMOUNTS = this.formData.pk_price / loss
    //   } else {
    //     AMOUNTS = this.formData.pk_price / this.formData.pk_consumption / loss
    //   }
    //   this.formData.pk_amounts = AMOUNTS.toFixed(5)
    // },
    // 提交保存
    handlePostForm (formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          // this.calculateRM()
          if (this.handleType === 'create') {
            ajax({
              method: 'POST',
              url: '/api/packadd',
              data: this.formData
            }).then(res => {
              this.$message({
                offset: 80,
                message: '保存成功！',
                type: 'success'
              })
              this.handleModal = false
              this.getPackage()
            })
          }
          if (this.handleType === 'update') {
            delete this.formData.pk_amounts
            delete this.formData.pk_consumption
            ajax({
              method: 'POST',
              url: '/api/updatepack',
              data: {
                ...this.formData,
                updatePersonnel: cookies.getCookie('userName')
              }
            }).then(res => {
              this.$message({
                offset: 80,
                message: '保存成功！',
                type: 'success'
              })
              this.callOf()
            })
          }
        } else {
          return false
        }
      })
    },
    // 删除
    handleDelete (row) {
      this.$confirm(`您是否要删除”${row.pk_material}“产品信息?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ajax({
          method: 'POST',
          url: '/api/deletepack',
          data: {
            packid: row.packid
          }
        }).then(res => {
          this.$message({
            offset: 80,
            type: 'success',
            message: '操作成功！'
          })
          this.current = 1
          this.getPackage()
        })
      }).catch(() => {
        this.$message({
          offset: 80,
          type: 'info',
          message: '已取消删除！'
        })
      })
    }
  }
}
</script>
<style lang="less" scoped>
.main {
  height:100vh;
  overflow:hidden;
  /deep/ .el-select, .el-input, .el-input-number, .el-input_inner {
    width: 100%;
  }
  /deep/ .el-input__inner {
    text-align: left;
  }
  /deep/ .el-form-item--mini .el-form-item__content, .el-form-item--mini .el-form-item__label {
    text-align: left;
  }
  .query {
    // margin-bottom: 20px;
    &-wrapper {
      display: inline-block;
      width: 100%;
      margin: 0 0 16px 0;
      text-align: left;
      button {
        margin: 5px;
      }
    }
    &-info {
      display: inline-block;
      width: 24%;
      vertical-align: bottom;
      >span {
        font-size: 13px;
        float: left;
        width: 64px;
        line-height: 32px;
      }
      div {
        float: left;
        width: 70%;
      }
    }
  }
  .page {
    margin: 20px 0;
    text-align: center;
  }
  .topBtn {
    float: left;
    margin: 0 0 24px 0;
  }
  .addBtn {
    float: right;
    margin: 0 24px 32px;
  }
}
</style>
