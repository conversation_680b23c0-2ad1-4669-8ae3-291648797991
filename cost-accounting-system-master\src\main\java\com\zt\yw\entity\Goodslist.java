package com.zt.yw.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor //有参构造器
@NoArgsConstructor  //无参构造器
@TableName("goodslist")
public class Goodslist  implements Serializable {
  private static final long serialVersionUID = 1L;
  @TableId(value = "goodsid", type = IdType.AUTO)//如数据库为自增id添加该行可解决数据库自增序号突然变大的问题
  private Integer goodsid;
  @TableField("rm_itemType")
  private String rm_itemType;
  @TableField("rm_model")
  private String rm_model;
  @TableField("rm_standard")
  private String rm_standard;
  @TableField("rm_succession")
  private String rm_succession;
  @TableField("rm_supplier")
  private String rm_supplier;
  @TableField("rm_number")
  private String rm_number;
  @TableField("rm_material")
  private String rm_material;
  @TableField("rm_price")
  private double rm_price;
  @TableField("rm_unit")
  private String rm_unit;
 // @TableField("rm_consumption")   //不需要了
 // private double rm_consumption;
  @TableField("rm_currencyType")
  private String rm_currencyType;
  //@TableField("rm_loss")
  //private double rm_loss;
  @TableField("rm_exchangeRate")
  private double rm_exchangeRate;
 // @TableField("rm_amounts")
 // private double rm_amounts;
  @TableField(exist = false)
  private  String updatePersonnel;


}
