#!/bin/bash

echo "========================================"
echo "   Makrite 成本核算系统 - 本地部署脚本"
echo "========================================"

echo
echo "[1/4] 检查环境..."
if ! command -v node &> /dev/null; then
    echo "错误: 未找到 Node.js，请先安装 Node.js"
    exit 1
fi

if ! command -v java &> /dev/null; then
    echo "错误: 未找到 Java，请先安装 JDK 1.8+"
    exit 1
fi

echo "Node.js 和 Java 环境检查通过"

echo
echo "[2/4] 构建前端项目..."
cd mak-costa-web-dev-230308

if [ ! -d "node_modules" ]; then
    echo "安装前端依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "错误: npm install 失败"
        exit 1
    fi
fi

echo "构建前端项目..."
npm run build
if [ $? -ne 0 ]; then
    echo "错误: 前端构建失败"
    exit 1
fi

echo
echo "[3/4] 集成前端到后端..."
cd ..
rm -rf cost-accounting-system-master/src/main/resources/static
mkdir -p cost-accounting-system-master/src/main/resources/static
cp -r mak-costa-web-dev-230308/dist/* cost-accounting-system-master/src/main/resources/static/

echo
echo "[4/4] 启动后端服务..."
cd cost-accounting-system-master
echo "正在启动服务器，请稍候..."
echo "服务启动后可通过以下地址访问："
echo "  - 系统首页: http://localhost:7000"
echo "  - API接口: http://localhost:7000/api"
echo
echo "按 Ctrl+C 可停止服务"
echo "========================================"

if [ -f "target/yw-0.0.1-SNAPSHOT.jar" ]; then
    java -jar target/yw-0.0.1-SNAPSHOT.jar
else
    echo "未找到jar包，正在编译..."
    ./mvnw clean package -DskipTests
    if [ $? -ne 0 ]; then
        echo "错误: Maven 构建失败"
        exit 1
    fi
    java -jar target/yw-0.0.1-SNAPSHOT.jar
fi
