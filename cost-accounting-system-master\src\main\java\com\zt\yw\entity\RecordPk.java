package com.zt.yw.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor //有参构造器
@NoArgsConstructor  //无参构造器
@TableName("record_pk")//用于记录修改原材料表
public class RecordPk implements Serializable {
  private static final long serialVersionUID = 1L;
  @TableId(value = "id", type = IdType.AUTO)
  private Integer id ;
  @TableField("uuid")
  private String uuid;
  @TableField("updatePersonnel")
  private String updatePersonnel;
  @TableField("updateTime")
  private String updateTime;
  @TableField("pk_number")
  private String pk_number;
  @TableField("pk_material")
  private String pk_material;
  @TableField("pk_price")
  private double pk_price;
  @TableField("pk_unit")
  private String pk_unit;
  @TableField("pk_consumption")
  private double pk_consumption;
  @TableField("pk_supplier")
  private String pk_supplier;
}
