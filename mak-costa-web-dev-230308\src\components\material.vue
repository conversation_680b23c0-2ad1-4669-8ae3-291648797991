<template>
  <div class="main">
    <div class="query">
      <div class="query-wrapper">
        <div class="query-info">
          <span>品号：</span>
          <el-input
            v-model="goodsNum"
            size="mini"
            placeholder="请输入品号查询"
            @keyup.enter.native="seach"
            clearable>
          </el-input>
        </div>
        <div class="query-info">
          <span>品名：</span>
          <el-input
            v-model="goodsName"
            size="mini"
            placeholder="请输入品名查询"
            @keyup.enter.native="seach"
            clearable>
          </el-input>
        </div>
        <div class="query-info">
          <span>种类：</span>
          <el-select
            v-model="itemType"
            size="mini"
            placeholder="请选择种类查询"
            @change="seach"
            clearable>
            <el-option v-for="item in itemType_options" :key="item.value" :label="item.label" :value="item.label"></el-option>
          </el-select>
        </div>
        <div class="query-info">
          <el-button size="mini" type="primary" plain @click="seach" icon="el-icon-search">查询</el-button>
        </div>
        <div class="query-info" style="text-align:right" v-if="power === '1' || power === '2'">
          <el-button type="primary" size="mini" icon="el-icon-plus" @click="handleAddGood">新增</el-button>
        </div>
      </div>
    </div>
    <el-table
      v-loading="loading"
      ref="table"
      class="mainTable"
      :data="goodsTableData"
      :header-cell-style='{"backgroundColor": "rgb(244, 244, 245)"}'
      :height="tableHeight"
      stripe
    >
      <el-table-column
        label="#"
        type="index"
        align="center"
        width="60"
      />
      <el-table-column
        prop="rm_number"
        label="品号"
        min-width="110"
      />
      <el-table-column
        prop="rm_material"
        label="品名"
        min-width="140"
      />
      <!-- <el-table-column
        prop="rm_standard"
        label="标准"
      /> -->
      <!-- <el-table-column
        prop="rm_model"
        label="型号"
      /> -->
      <!-- <el-table-column
        prop="rm_succession"
        label="系列"
      /> -->
      <el-table-column
        prop="rm_itemType"
        label="种类"
      />
      <el-table-column
        prop="rm_supplier"
        label="厂商"
      />
      <el-table-column
        prop="rm_price"
        label="价格"
      />
      <el-table-column
        prop="rm_unit"
        label="单位"
      />
      <!-- <el-table-column
        prop="rm_consumption"
        label="用量"
      /> -->
      <el-table-column
        prop="rm_currencyType"
        label="币种"
        :formatter="fmtCurrencyType"
      />
      <el-table-column
        prop="rm_exchangeRate"
        label="汇率"
      />
      <!-- <el-table-column
        prop="rm_amounts"
        label="金额"
      /> -->
      <el-table-column v-if="power === '1' || power === '2' || power === '8'" label="操作" align="center" min-width="130">
        <template slot-scope="scope">
          <el-button type="text" icon="el-icon-edit" @click="handleUpdateGood(scope.row)">修改</el-button>
          <el-button type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="page">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="current"
        :page-sizes="[20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
    <!-- 新增/修改 -->
    <el-dialog :title="handleType==='create'?'新增':`修改《${editTitle}》内容`" :visible.sync="handleModal" @close="handleModalClose" :close-on-click-modal="false" :show-close="false">
      <el-form :model="formData" class="form-wrap" :rules="rules" ref="ruleForm" size="mini">
        <el-form-item v-if="handleType==='update'" prop="rm_number" label="品号" label-width="60px">
          <el-input v-model="formData.rm_number" :disabled="true">
          </el-input>
        </el-form-item>
        <el-form-item prop="rm_material" label="品名" label-width="60px">
          <el-input v-model="formData.rm_material" placeholder="品名命名规则请尽量遵循：总称：颜色+材质+尺寸+特性(+厂商)" clearable>
          </el-input>
        </el-form-item>
        <!-- <el-form-item prop="rm_standard" label="标准" label-width="60px">
          <el-checkbox-group v-model="formData.rm_standard">
            <el-checkbox v-for="(op, i) in standard_options" :key="i" :label="op.label"></el-checkbox>
          </el-checkbox-group>
        </el-form-item> -->
        <!-- <el-form-item prop="rm_model" label="型号" label-width="60px">
          <el-checkbox-group v-model="formData.rm_model">
            <el-checkbox v-for="(op, i) in model_options" :key="i" :label="op.label"></el-checkbox>
          </el-checkbox-group>
        </el-form-item> -->
        <el-form-item prop="rm_itemType" label="种类" label-width="60px">
          <el-checkbox-group v-model="formData.rm_itemType">
            <el-checkbox v-for="(op, i) in itemType_options" :key="i" :label="op.label"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item prop="rm_supplier" label="厂商" label-width="60px">
          <el-input v-model="formData.rm_supplier" placeholder="请输入厂商" clearable>
          </el-input>
        </el-form-item>
        <el-form-item prop="rm_price" label="价格" label-width="60px">
          <el-input-number v-model="formData.rm_price" controls-position="right" placeholder="请输入单价" :min="0" :precision="5" clearable>
          </el-input-number>
        </el-form-item>
        <el-form-item prop="rm_unit" label="单位" label-width="60px" style="position: relative;">
          <el-select v-model="formData.rm_unit" placeholder="注意成品用量变化，并通知主品配置人员" filterable allow-create default-first-optionclearable>
            <el-option
              v-for="item in unit_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <p v-if="handleType==='update'" style="padding: 0;margin: 0;font-size: 12px; color: #F56C6C;position: absolute;top: 64%;left:148px;">注意成品用量变化，并通知主品配置人员</p>
        <!-- <el-form-item prop="rm_consumption" label="用量" label-width="60px">
          <el-input-number v-model="formData.rm_consumption" controls-position="right" placeholder="请输入用量" :min="0" clearable>
          </el-input-number>
        </el-form-item> -->
        <el-form-item prop="rm_currencyType" label="币种" label-width="60px">
          <el-select v-model="formData.rm_currencyType" @change="handleCTchange" placeholder="请选择币种">
            <el-option
              v-for="item in ct_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="rm_exchangeRate" label="汇率" label-width="60px">
          <el-input-number v-model="formData.rm_exchangeRate" controls-position="right" :min="0" clearable />
        </el-form-item>
        <!-- <el-form-item prop="rm_amounts" label="单价" label-width="60px">
          <el-button type="text" icon="el-icon-search" @click="calculateRM" />
          <span>{{formData.rm_amounts}}</span>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="callOf('ruleForm')">取 消</el-button>
        <el-button type="primary" size="mini" @click="handlePostForm('ruleForm')">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { cookies } from '../common/utils'
import ajax from '../common/axiosHttp'
export default {
  name: 'Material',
  data () {
    return {
      power: cookies.getCookie('power'),
      loading: false,
      tableHeight: 0,
      goodsTableData: [],
      current: 1,
      pageSize: 20,
      goodsName: '',
      goodsNum: '',
      itemType: '',
      total: 0,
      handleType: '',
      handleModal: false,
      formData: {
        // rm_model: [],
        // rm_succession: [],
        // rm_standard: [],
        rm_itemType: []
      },
      editTitle: '',
      rules: {
        rm_material: [
          {
            required: true,
            message: '请填写品名',
            trigger: 'blur'
          }
        ],
        // rm_model: [
        //   {
        //     required: true,
        //     message: '请填写型号',
        //     trigger: 'blur'
        //   }
        // ],
        // rm_succession: [
        //   {
        //     required: true,
        //     message: '请填写系列',
        //     trigger: 'blur'
        //   }
        // ],
        // rm_standard: [
        //   {
        //     required: true,
        //     message: '请填写标准',
        //     trigger: 'blur'
        //   }
        // ],
        rm_itemType: [
          {
            required: true,
            message: '请填写种类',
            trigger: 'blur'
          }
        ],
        rm_price: [
          {
            required: true,
            type: 'number',
            message: '请填写单价',
            trigger: 'blur'
          }
        ],
        rm_unit: [
          {
            required: true,
            message: '请填写单位',
            trigger: 'blur'
          }
        ],
        rm_consumption: [
          {
            required: true,
            type: 'number',
            message: '请填写用量',
            trigger: 'blur'
          }
        ],
        rm_currencyType: [
          {
            required: true,
            message: '请选择币种',
            trigger: 'change'
          }
        ]
        // rm_amounts: [
        //   {
        //     required: true,
        //     message: '请填写金额',
        //     trigger: 'blur'
        //   }
        // ]
      }
    }
  },
  computed: {
    ...mapState(['FXRates', 'ct_options', 'CURRENCY_DIC', 'standard_options', 'MODEL_DIC', 'itemType_options', 'ITEM_DIC', 'unit_options'])
  },
  mounted () {
    // 挂载window.onresize事件(动态设置table高度)
    this.$nextTick(() => {
      this.tableHeight = window.innerHeight - 136
    })
    let _this = this
    window.onresize = () => {
      if (_this.resizeFlag) {
        clearTimeout(_this.resizeFlag)
      }
      _this.resizeFlag = setTimeout(() => {
        _this.getTableHeight()
        _this.resizeFlag = null
      }, 100)
    }
  },
  activated () {
    this.getGoods()
  },
  methods: {
    // 计算table高度(动态设置table高度)
    getTableHeight () {
      let tableH = 180 // 距离页面下方的高度
      let tableHeightDetil = window.innerHeight - tableH
      if (tableHeightDetil <= 300) {
        this.tableHeight = 300
      } else {
        this.tableHeight = window.innerHeight - tableH
      }
    },
    // 搜索
    seach () {
      this.current = 1
      this.pageSize = 20
      this.getGoods()
    },
    // 获取货品列表
    getGoods () {
      this.loading = true
      let form = new FormData()
      form.append('current', this.current)
      form.append('size', this.pageSize)
      form.append('rm_material', this.goodsName)
      form.append('rm_number', this.goodsNum)
      form.append('rm_itemType', this.itemType)
      ajax({
        method: 'POST',
        url: '/api/GoodsAll',
        data: form
      }).then(res => {
        this.loading = false
        this.total = res.total
        this.goodsTableData = res.records
      }).catch(() => {
        this.loading = false
      })
    },
    // 页码分页
    handleSizeChange (val) {
      this.pageSize = val
      this.current = 1
      this.getGoods()
    },
    // 分页
    handleCurrentChange (val) {
      this.current = val
      this.getGoods()
    },
    // 格式化币种
    fmtCurrencyType (row, column, cellValue) {
      return this.CURRENCY_DIC[cellValue]
    },
    // 新增
    handleAddGood () {
      this.formData = {
        // rm_model: [],
        // rm_succession: [],
        // rm_standard: [],
        rm_itemType: [],
        rm_currencyType: 'CNY'
      }
      this.handleCTchange('CNY')
      this.handleType = 'create'
      this.handleModal = true
    },
    // 修改
    handleUpdateGood (row) {
      this.editTitle = row.rm_material
      this.formData = {
        ...row,
        rm_itemType: row.rm_itemType.split(',')
        // rm_model: row.rm_model.split(',')
        // rm_standard: row.rm_standard ? row.rm_standard.split(',') : [],
        // rm_succession: row.rm_succession ? row.rm_succession.split(',') : []
      }
      this.handleType = 'update'
      this.handleModal = true
    },
    // 选择币种
    handleCTchange (val) {
      // 自动带出汇率值
      this.formData.rm_exchangeRate = this.FXRates[val]
    },
    // 关闭提醒
    handleModalClose () {
      this.$refs['ruleForm'].resetFields()
    },
    // 关闭模态框
    callOf (formName) {
      this.handleModal = false
      this.$refs[formName].resetFields()
      this.current = 1
      this.getGoods()
    },
    // 计算金额
    calculateRM () {
      if (!this.formData.rm_price === '') return
      if (!this.formData.rm_consumption === '') return
      let AMOUNTS = 0
      let loss = 0.97
      if (this.formData.rm_material === '油墨') {
        console.log('油墨来了')
        loss = 1
      }
      if (this.formData.rm_consumption === 0) {
        AMOUNTS = this.formData.rm_price / loss
      } else {
        AMOUNTS = this.formData.rm_price / this.formData.rm_consumption / loss
      }
      this.formData.rm_amounts = AMOUNTS.toFixed(3)
    },
    // 提交保存
    handlePostForm (formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          let postData = {
            ...this.formData,
            rm_itemType: this.formData.rm_itemType.toString()
            // rm_standard: this.formData.rm_standard.toString(),
            // rm_succession: this.formData.rm_succession.toString(),
            // rm_model: this.formData.rm_model.toString()
            // rm_amounts: ''
          }
          // this.calculateRM()
          if (this.handleType === 'create') {
            ajax({
              method: 'POST',
              url: '/api/goodsadd',
              data: postData
            }).then(res => {
              this.$message({
                offset: 80,
                message: '保存成功！',
                type: 'success'
              })
              this.handleModal = false
              this.current = 1
              this.getGoods()
            })
          }
          if (this.handleType === 'update') {
            ajax({
              method: 'POST',
              url: '/api/updategoods',
              data: {
                ...postData,
                updatePersonnel: cookies.getCookie('userName')
              }
            }).then(res => {
              this.$message({
                offset: 80,
                message: '保存成功！',
                type: 'success'
              })
              this.handleModal = false
              this.current = 1
              this.getGoods()
            })
          }
        } else {
          return false
        }
      })
    },
    // 删除
    handleDelete (row) {
      this.$confirm(`您是否要删除”${row.rm_material}“产品信息?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ajax({
          method: 'POST',
          url: '/api/deletegoods',
          data: {
            goodsid: row.goodsid
          }
        }).then(res => {
          this.$message({
            offset: 80,
            type: 'success',
            message: '操作成功！'
          })
          this.current = 1
          this.getGoods()
        })
      }).catch(() => {
        this.$message({
          offset: 80,
          type: 'info',
          message: '已取消删除！'
        })
      })
    }
  }
}
</script>
<style lang="less" scoped>
.main {
  height:100vh;
  overflow:hidden;
  /deep/ .el-select, .el-input, .el-input-number, .el-input_inner {
    width: 100%;
  }
  /deep/ .el-input__inner {
    text-align: left;
  }
  /deep/ .el-form-item--mini .el-form-item__content, .el-form-item--mini .el-form-item__label {
    text-align: left;
  }
  .query {
    // margin-bottom: 20px;
    &-wrapper {
      display: inline-block;
      width: 100%;
      margin: 0 0 16px 0;
      text-align: left;
      button {
        margin: 5px;
      }
    }
    &-info {
      display: inline-block;
      width: 19%;
      vertical-align: bottom;
      >span {
        font-size: 13px;
        float: left;
        width: 64px;
        line-height: 32px;
      }
      div {
        float: left;
        width: 70%;
      }
    }
  }
  .page {
    margin: 20px 0;
    text-align: center;
  }
  .topBtn {
    float: left;
    margin: 0 0 24px 0;
  }
  .addBtn {
    float: right;
    margin: 0 24px 32px;
  }
}
</style>
