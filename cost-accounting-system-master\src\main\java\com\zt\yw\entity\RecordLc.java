package com.zt.yw.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor //有参构造器
@NoArgsConstructor  //无参构造器
@TableName("record_lc")//原材料表
public class RecordLc implements Serializable {
  private static final long serialVersionUID = 1L;
  @TableId(value = "id", type = IdType.AUTO)
  private Integer id ;
  @TableField("uuid")
  private String uuid;
  @TableField("updatePersonnel")
  private String updatePersonnel;
  @TableField("updateTime")
  private String updateTime;
  @TableField("lc_name")
  private String lc_name;
  @TableField("lc_price")
  private double lc_price;

}
