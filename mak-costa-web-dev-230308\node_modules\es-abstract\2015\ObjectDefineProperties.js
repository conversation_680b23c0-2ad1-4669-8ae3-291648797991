'use strict';

var callBound = require('call-bind/callBound');

var DefinePropertyOrThrow = require('./DefinePropertyOrThrow');
var FromPropertyDescriptor = require('./FromPropertyDescriptor');
var Get = require('./Get');
var ToObject = require('./ToObject');
var ToPropertyDescriptor = require('./ToPropertyDescriptor');

var forEach = require('../helpers/forEach');
var getOwnPropertyDescriptor = require('../helpers/getOwnPropertyDescriptor');
var OwnPropertyKeys = require('../helpers/OwnPropertyKeys');

var $push = callBound('Array.prototype.push');

// https://262.ecma-international.org/6.0/#sec-objectdefineproperties
module.exports = function ObjectDefineProperties(O, Properties) {
	var props = ToObject(Properties); // step 1
	var keys = OwnPropertyKeys(props); // step 2
	var descriptors = []; // step 3

	forEach(keys, function (nextKey) { // step 4
		var propDesc = ToPropertyDescriptor(getOwnPropertyDescriptor(props, nextKey)); // step 4.a
		if (typeof propDesc !== 'undefined' && propDesc['[[Enumerable]]']) { // step 4.b
			var descObj = Get(props, nextKey); // step 4.b.i
			var desc = ToPropertyDescriptor(descObj); // step 4.b.ii
			$push(descriptors, [nextKey, desc]); // step 4.b.iii
		}
	});

	forEach(descriptors, function (pair) { // step 5
		var P = pair[0]; // step 5.a
		var desc = pair[1]; // step 5.b
		desc = FromPropertyDescriptor(desc); // TODO: remove this once DefinePropertyOrThrow is fixed
		DefinePropertyOrThrow(O, P, desc); // step 5.c
	});

	return O; // step 6
};
