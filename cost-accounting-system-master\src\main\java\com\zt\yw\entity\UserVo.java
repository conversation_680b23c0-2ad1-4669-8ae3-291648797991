package com.zt.yw.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor //有参构造器
@NoArgsConstructor  //无参构造器
//用于接收原材料分类的集合
public class UserVo extends RmTable {
    private static final long serialVersionUID = 1L;
    private List<RmTable> binding;
    private List<RmTable> foam;
    private List<RmTable> in;
    private List<RmTable> mid;
    private List<RmTable> nasal;
    private List<RmTable> others;
    private List<RmTable> out;
}
