package com.zt.yw.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor //有参构造器
@NoArgsConstructor  //无参构造器
@TableName("main_table")
public class Maintable implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "admin_id", type = IdType.AUTO)//如数据库为自增id添加该行可解决数据库自增序号突然变大的问题
    private  Integer admin_id;
    @TableField("uuid")
    private String uuid;
    @TableField("productName")
    private  String productName;
    @TableField("updatePersonnel")
    private  String updatePersonnel;
    @TableField("updateTime")
    private String updateTime;
    @TableField("LastTime")
    private String LastTime;
    @TableField("LastModifiedBy")
    private String LastModifiedBy;
    @TableField("CreationDate")
    private String CreationDate;
    @TableField("Creator")
    private String Creator;
    @TableField("productModel")
    private String productModel;
    @TableField("productStandard")
    private String productStandard;
    @TableField("productSuccession")
    private String productSuccession;
    @TableField("productDetail")
    private String productDetail;
    @TableField("productConnection")
    private String productConnection;
    @TableField("productFactory")
    private String productFactory;
    @TableField("productPackage")
    private String productPackage;
    @TableField("pcsCount")
    private int pcsCount;
    @TableField("bagCount")
    private int bagCount;
    @TableField("boxCount")
    private int boxCount;
    @TableField(exist = false)  //自定义字段注释（不关联数据库字段）
    private List<Lctable> laborCostData;
    @TableField(exist = false)
    private List<UserVo> materialData;
    @TableField(exist = false)
    private List<RmTable> rmTableslist;
    @TableField(exist = false)
    private List<PkTable> packageFormData;
    @TableField(exist = false)
    private Frtable freightFormData;
    @TableField(exist = false)
    private List<Maintable> adminIds;


}
