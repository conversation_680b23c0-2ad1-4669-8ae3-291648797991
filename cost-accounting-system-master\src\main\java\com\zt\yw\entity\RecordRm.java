package com.zt.yw.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor //有参构造器
@NoArgsConstructor  //无参构造器
@TableName("record_rm")//用于记录修改原材料表
public class RecordRm implements Serializable {
  private static final long serialVersionUID = 1L;
  @TableId(value = "id", type = IdType.AUTO)
  private Integer id ;
  @TableField("uuid")
  private String uuid;
  @TableField("rm_number")
  private String rm_number;
  @TableField("rm_material")
  private String rm_material;
  @TableField("rm_price")
  private double rm_price;
  @TableField("rm_consumption")
  private double rm_consumption;
  @TableField("rm_unit")
  private String rm_unit;
  @TableField("rm_currencyType")
  private String rm_currencyType;
  @TableField("updatePersonnel")
  private String updatePersonnel;
  @TableField("updateTime")
  private String updateTime;

}
