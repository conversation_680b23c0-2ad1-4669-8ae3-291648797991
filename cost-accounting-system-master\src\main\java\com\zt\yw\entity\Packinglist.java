package com.zt.yw.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor //有参构造器
@NoArgsConstructor  //无参构造器
@TableName("packinglist")
public class Packinglist implements Serializable {
  private static final long serialVersionUID = 1L;
  @TableId(value = "packid", type = IdType.AUTO)//如数据库为自增id添加该行可解决数据库自增序号突然变大的问题
  private Integer packid;
  @TableField("pk_itemType")
  private String pk_itemType;
  @TableField("pk_supplier")
  private String pk_supplier;
  @TableField("pk_number")
  private String pk_number;
  @TableField("pk_material")
  private String pk_material;
  @TableField("pk_price")
  private double pk_price;
  @TableField("pk_unit")
  private String pk_unit;
  //@TableField("pk_consumption")
  //private double pk_consumption;
  @TableField("pk_currencyType")
  private String pk_currencyType;
  @TableField("pk_exchangeRate")
  private double pk_exchangeRate;
  //@TableField("pk_amounts")
  //private double pk_amounts;
  @TableField(exist = false)
  private String updatePersonnel;
  @TableField("pk_length")
  private double pk_length;
  @TableField("pk_width")
  private double pk_width;
  @TableField("pk_height")
  private double pk_height;
}
