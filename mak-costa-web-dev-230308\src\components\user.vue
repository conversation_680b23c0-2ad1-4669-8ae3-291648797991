<template>
  <div class="main">
    <div class="query">
     <div class="query-wrapper">
        <div class="query-info" style="text-align:right">
          <el-button type="primary" size="mini" icon="el-icon-plus" @click="handleAddUser">新增</el-button>
        </div>
      </div>
    </div>
    <el-table
      v-loading="loading"
      ref="table"
      class="mainTable"
      :data="userTableData"
      :header-cell-style='{"backgroundColor": "rgb(244, 244, 245)"}'
      :height="tableHeight"
      stripe
    >
      <el-table-column
        label="#"
        type="index"
        align="center"
        width="60"
      />
      <el-table-column
        prop="userName"
        label="用户名称"
      />
      <el-table-column
        prop="company"
        label="所属公司"
      />
      <el-table-column
        prop="power"
        label="用户权限"
      >
        <template slot-scope="scope">
          <span>{{POWER_DIC[scope.row.power]}}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="registrationdate"
        label="注册时间"
      />
      <el-table-column
        prop="last_login_time"
        label="最后登录时间"
      />
      <el-table-column label="操作" align="center" min-width="100">
        <template slot-scope="scope">
          <el-button type="text" icon="el-icon-edit" @click="handleUpdateLabor(scope.row)">修改</el-button>
          <el-button type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="page">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="current"
        :page-sizes="[20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
    <!-- 新增/修改 -->
    <el-dialog :title="handleType==='create'?'新增':`修改《${editTitle}》内容`" :visible.sync="handleModal" :before-close="handleModalClose">
      <el-form :model="formData" class="form-wrap" :rules="rules" ref="ruleForm" size="mini">
        <el-form-item prop="userName" label="用户名称: " label-width="100px" style="text-align: left; width: 90%;">
          <el-input v-model="formData.userName" size="small" placeholder="请填写">
          </el-input>
        </el-form-item>
        <el-form-item prop="password" label="登录密码: " label-width="100px" style="text-align: left; width: 90%;">
          <el-input type="password" v-model="formData.password" size="small" placeholder="请填写">
          </el-input>
        </el-form-item>
        <el-form-item prop="company" label="所属公司: " label-width="100px" style="text-align: left; width: 90%;">
          <el-select v-model="formData.company" placeholder="请选择">
            <el-option
              v-for="item in company_options"
              :key="item"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="power" label="权限分配: " label-width="100px" style="text-align: left; width: 90%;">
          <el-select v-model="formData.power" placeholder="请选择">
            <el-option
              v-for="item in power_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="callOf('ruleForm')">取 消</el-button>
        <el-button type="primary" size="mini" @click="handlePostForm('ruleForm')">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import ajax from '../common/axiosHttp'
export default {
  name: 'User',
  data () {
    return {
      loading: false,
      tableHeight: 0,
      userTableData: [],
      current: 1,
      pageSize: 20,
      total: 0,
      handleType: '',
      handleModal: false,
      formData: {},
      editTitle: '',
      rules: {
        userName: [
          {
            required: true,
            message: '请填写',
            trigger: 'blur'
          }
        ],
        password: [
          {
            required: true,
            message: '请填写',
            trigger: 'blur'
          }
        ],
        company: [
          {
            required: true,
            message: '请选择',
            trigger: 'blur'
          }
        ],
        power: [
          {
            required: true,
            message: '请选择',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    ...mapState(['power_options', 'POWER_DIC', 'company_options'])
  },
  mounted () {
    // 挂载window.onresize事件(动态设置table高度)
    this.$nextTick(() => {
      this.tableHeight = window.innerHeight - 136
    })
    let _this = this
    window.onresize = () => {
      if (_this.resizeFlag) {
        clearTimeout(_this.resizeFlag)
      }
      _this.resizeFlag = setTimeout(() => {
        _this.getTableHeight()
        _this.resizeFlag = null
      }, 100)
    }
  },
  activated () {
    this.getUserList()
  },
  methods: {
    // 计算table高度(动态设置table高度)
    getTableHeight () {
      let tableH = 180 // 距离页面下方的高度
      let tableHeightDetil = window.innerHeight - tableH
      if (tableHeightDetil <= 300) {
        this.tableHeight = 300
      } else {
        this.tableHeight = window.innerHeight - tableH
      }
    },
    // 获取用户列表
    getUserList () {
      this.loading = true
      let form = new FormData()
      form.append('current', this.current)
      form.append('size', this.pageSize)
      form.append('userName', '')
      ajax({
        method: 'POST',
        url: '/api/selectUser',
        data: form
      }).then(res => {
        this.loading = false
        this.total = res.total
        this.userTableData = res.records
      }).catch(() => {
        this.loading = false
      })
    },
    // 页码分页
    handleSizeChange (val) {
      this.pageSize = val
      this.getUserList()
    },
    // 分页
    handleCurrentChange (val) {
      this.current = val
      this.getUserList()
    },
    // 新增
    handleAddUser () {
      this.handleType = 'create'
      this.formData = {}
      this.handleModal = true
    },
    // 修改
    handleUpdateLabor (row) {
      this.editTitle = row.userName
      this.formData = row
      this.handleType = 'update'
      this.handleModal = true
    },
    // 关闭提醒
    handleModalClose (done) {
      this.$confirm('确认关闭吗？').then(_ => {
        done()
        this.formData = {}
        this.$refs['ruleForm'].resetFields()
        this.getUserList()
      })
        .catch(_ => {})
    },
    // 关闭模态框
    callOf (formName) {
      this.handleModal = false
      this.formData = {}
      this.$refs[formName].resetFields()
      this.getUserList()
    },
    // 提交保存
    handlePostForm (formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (this.handleType === 'create') {
            ajax({
              method: 'POST',
              url: '/api/addUserTo',
              data: this.formData
            }).then(res => {
              this.$message({
                offset: 80,
                message: '保存成功！',
                type: 'success'
              })
              this.handleModal = false
              this.getUserList()
            })
          }
          if (this.handleType === 'update') {
            ajax({
              method: 'POST',
              url: '/api/updateUser',
              data: this.formData
            }).then(res => {
              this.$message({
                offset: 80,
                message: '保存成功！',
                type: 'success'
              })
              this.handleModal = false
              this.getUserList()
            })
          }
        } else {
          return false
        }
      })
    },
    // 删除
    handleDelete (row) {
      this.$confirm(`您是否要删除当前用户数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ajax({
          method: 'POST',
          url: '/api/deleteUser',
          data: {
            userid: row.userid
          }
        }).then(res => {
          this.$message({
            offset: 80,
            type: 'success',
            message: '操作成功！'
          })
          this.current = 1
          this.getUserList()
        })
      }).catch(() => {
        this.$message({
          offset: 80,
          type: 'info',
          message: '已取消删除！'
        })
      })
    }
  }
}
</script>
<style lang="less" scoped>
.main {
  height:100vh;
  overflow:hidden;
  /deep/ .el-select, .el-input, .el-input-number, .el-input_inner {
    width: 100%;
  }
  /deep/ .el-input__inner {
    text-align: left;
  }
  /deep/ .el-form-item--mini .el-form-item__content, .el-form-item--mini .el-form-item__label {
    text-align: left;
  }
  .query {
    // margin-bottom: 20px;
    &-wrapper {
      display: inline-block;
      width: 100%;
      margin: 0 0 16px 0;
      text-align: left;
      button {
        margin: 5px;
      }
    }
    &-info {
      display: inline-block;
      width: 100%;
      vertical-align: bottom;
      >span {
        font-size: 13px;
        float: left;
        width: 80px;
        line-height: 32px;
      }
      div {
        float: left;
        width: 70%;
      }
    }
  }
  .page {
    margin: 20px 0;
    text-align: center;
  }
  .topBtn {
    float: left;
    margin: 0 0 24px 0;
  }
  .addBtn {
    float: right;
    margin: 0 24px 32px;
  }
}
</style>
