package com.zt.yw.common;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

public interface CommonConstant {

    /**
     * 树根节点
     */
    Long TREE_ROOT_ID = -1L;
    /**
     * 树根节点名称
     */
    String TREE_ROOT_NAME = "顶级";
    /**
     * 团队标识
     */
    String TEAM_CODE_KEY = "teamCode";
    /**
     * 默认团队标识
     */
    String TEAM_ROOT_CODE = "000000";

    /**
     * token 请求头 ip
     */
    String TOKEN_HEADER_FORWARDED = "X-Forwarded-For";
    /**
     * 编码
     */
    Charset UTF_8 = StandardCharsets.UTF_8;
    String UTF_8_NAME = UTF_8.name();
    /**
     * contentType
     */
    String CONTENT_TYPE_NAME = "Content-type";
    /**
     * JSON 资源
     */
    String CONTENT_TYPE = "application/json;charset=utf-8";
    /**
     * 默认为空消息
     */
    String DEFAULT_NULL_MESSAGE = "暂无承载数据";
    /**
     * 默认成功消息
     */
    String DEFAULT_SUCCESS_MESSAGE = "成功";
    /**
     * 默认失败消息
     */
    String DEFAULT_FAILURE_MESSAGE = "失败";
    /**
     * 删除
     */
    Integer STATUS_DEL = 1;
    /**
     * 正常
     */
    Integer STATUS_NORMAL = 0;
}


