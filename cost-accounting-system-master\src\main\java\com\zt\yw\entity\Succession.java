package com.zt.yw.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor //有参构造器
@NoArgsConstructor  //无参构造器
@TableName("succession")//系列主品
public class Succession implements Serializable {
  private static final long serialVersionUID = 1L;
  @TableId(value = "sc_id", type = IdType.AUTO)
  private Integer sc_id;
  @TableField("uuid")
  private String uuid;
  @TableField("sc_name")
  private String sc_name;
  @TableField("sc_user")
  private String sc_user;
  @TableField("sc_date")
  private String sc_date;
  @TableField("sc_discripte")
  private String sc_discripte;
  @TableField("sc_standard")
  private String sc_standard;
  @TableField("sc_type")
  private String sc_type; //型号
  @TableField("lowest_offer")
  private double lowest_offer; //最低报价警示

}
