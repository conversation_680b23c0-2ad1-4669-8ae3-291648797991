package com.zt.yw.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor //有参构造器
@NoArgsConstructor  //无参构造器
@TableName("user")
//用户表
public class User implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "userid", type = IdType.AUTO)
    private Integer userid; //自增ID
    @TableField("userName")
    private String userName; //用户名字
    @TableField("password")
    private String password; //用户密码
    @TableField("power")
    private Integer power; //用户权限
    @TableField("registrationdate")
    private String registrationdate; //注册时间
    @TableField("company")
    private String company; //所属公司
    @TableField("last_login_time") //最后登录时间
    private String last_login_time;

}
